Starting AI Policy Analyzer Visualization API...
Available endpoints:
  GET /api/visualize/sentiment
  GET /api/visualize/moral-framing
  GET /api/visualize/policy-preferences
  POST /api/visualize/comparative
  GET /api/visualize/summary
  GET /api/visualize/all
  GET /api/health
 * Serving Flask app 'visualization_api'
 * Debug mode: on
WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://************:5001
Press CTRL+C to quit
 * Restarting with watchdog (windowsapi)
 * Debugger is active!
 * Debugger PIN: 145-413-362
127.0.0.1 - - [28/Aug/2025 22:00:43] "GET /api/health HTTP/1.1" 404 -
