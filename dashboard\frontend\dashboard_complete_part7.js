/**
 * Display simulation results
 */
function displaySimulationResults(data) {
    const simulationResults = document.getElementById('simulationResults');
    const policyInsights = document.getElementById('policyInsights');
    
    if (!simulationResults || !policyInsights) return;
    
    // Display impact metrics
    simulationResults.innerHTML = `
        <div class="mb-4">
            <h6 class="mb-3">Impact Assessment</h6>
            <div class="row g-3">
                <div class="col-md-6">
                    <label class="form-label d-flex justify-content-between">
                        <span>Policy Effectiveness</span>
                        <span>${data.policy_effectiveness}%</span>
                    </label>
                    <div class="progress mb-3">
                        <div class="progress-bar bg-primary" role="progressbar" style="width: ${data.policy_effectiveness}%" 
                            aria-valuenow="${data.policy_effectiveness}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    
                    <label class="form-label d-flex justify-content-between">
                        <span>Innovation Impact</span>
                        <span>${data.innovation_impact}%</span>
                    </label>
                    <div class="progress">
                        <div class="progress-bar bg-info" role="progressbar" style="width: ${data.innovation_impact}%" 
                            aria-valuenow="${data.innovation_impact}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <label class="form-label d-flex justify-content-between">
                        <span>Compliance Cost</span>
                        <span>${data.compliance_cost}%</span>
                    </label>
                    <div class="progress mb-3">
                        <div class="progress-bar bg-warning" role="progressbar" style="width: ${data.compliance_cost}%" 
                            aria-valuenow="${data.compliance_cost}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    
                    <label class="form-label d-flex justify-content-between">
                        <span>Public Trust</span>
                        <span>${data.public_trust}%</span>
                    </label>
                    <div class="progress">
                        <div class="progress-bar bg-success" role="progressbar" style="width: ${data.public_trust}%" 
                            aria-valuenow="${data.public_trust}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mb-3">
            <h6 class="mb-2">Expected Organization Reactions</h6>
            <div class="row g-2">
                <div class="col-md-6">
                    <div class="card bg-light mb-2">
                        <div class="card-body py-2 px-3">
                            <small class="text-muted">Corporate</small>
                            <p class="mb-0"><strong>${data.organization_reactions?.corporate || 'Mixed'}</strong></p>
                        </div>
                    </div>
                    <div class="card bg-light">
                        <div class="card-body py-2 px-3">
                            <small class="text-muted">Academic</small>
                            <p class="mb-0"><strong>${data.organization_reactions?.academic || 'Neutral'}</strong></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card bg-light mb-2">
                        <div class="card-body py-2 px-3">
                            <small class="text-muted">Nonprofit</small>
                            <p class="mb-0"><strong>${data.organization_reactions?.nonprofit || 'Supportive'}</strong></p>
                        </div>
                    </div>
                    <div class="card bg-light">
                        <div class="card-body py-2 px-3">
                            <small class="text-muted">Government</small>
                            <p class="mb-0"><strong>${data.organization_reactions?.government || 'Concerned'}</strong></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Display insights
    if (data.insights && data.insights.length > 0) {
        let insightsHTML = '<div class="list-group">';
        
        data.insights.forEach(insight => {
            insightsHTML += `
                <div class="list-group-item">
                    <div class="d-flex w-100 align-items-center">
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        <p class="mb-0">${insight}</p>
                    </div>
                </div>
            `;
        });
        
        insightsHTML += '</div>';
        policyInsights.innerHTML = insightsHTML;
    } else {
        policyInsights.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                No specific insights were generated for this policy configuration.
            </div>
        `;
    }
}

/**
 * Load Historical Data section
 */
function loadHistoricalData() {
    const historySection = document.getElementById('historical-section');
    if (!historySection) return;
    
    historySection.innerHTML = `
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="h4 mb-0">Historical Data</h2>
            <div>
                <button class="btn btn-sm btn-outline-primary me-2" onclick="exportHistoricalData()">
                    <i class="fas fa-download me-1"></i> Export CSV
                </button>
                <button class="btn btn-sm btn-outline-secondary" onclick="refreshHistoricalData()">
                    <i class="fas fa-sync-alt me-1"></i> Refresh
                </button>
            </div>
        </div>
        
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Historical Analysis Records</h5>
                    <button class="btn btn-sm btn-link p-0" type="button" data-bs-toggle="collapse" data-bs-target="#historyFilters">
                        <i class="fas fa-filter"></i> Filters
                    </button>
                </div>
            </div>
            
            <div class="collapse" id="historyFilters">
                <div class="card-body border-bottom">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">Date Range</label>
                            <select id="historyDateRange" class="form-select">
                                <option value="7">Last 7 Days</option>
                                <option value="30" selected>Last 30 Days</option>
                                <option value="90">Last 90 Days</option>
                                <option value="365">Last Year</option>
                                <option value="all">All Time</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Organization Type</label>
                            <select id="historyOrgType" class="form-select">
                                <option value="">All Types</option>
                                <option value="Corporate">Corporate</option>
                                <option value="Academic">Academic</option>
                                <option value="Nonprofit">Nonprofit</option>
                                <option value="Government">Government</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Document Type</label>
                            <select id="historyDocType" class="form-select">
                                <option value="">All Documents</option>
                                <option value="Policy">Policy Statements</option>
                                <option value="Guidelines">Guidelines</option>
                                <option value="Paper">Position Papers</option>
                                <option value="Response">RFI Responses</option>
                            </select>
                        </div>
                        <div class="col-12 text-end">
                            <button class="btn btn-sm btn-primary" onclick="applyHistoricalFilters()">
                                <i class="fas fa-check me-1"></i> Apply Filters
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card-body">
                <div class="table-responsive">
                    <table id="historicalDataTable" class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th onclick="sortHistoricalData('date')">Date <i class="fas fa-sort ms-1"></i></th>
                                <th onclick="sortHistoricalData('organization')">Organization <i class="fas fa-sort ms-1"></i></th>
                                <th onclick="sortHistoricalData('type')">Type <i class="fas fa-sort ms-1"></i></th>
                                <th onclick="sortHistoricalData('document')">Document <i class="fas fa-sort ms-1"></i></th>
                                <th onclick="sortHistoricalData('sentiment')">Sentiment <i class="fas fa-sort ms-1"></i></th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="historicalDataBody">
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p class="mt-2 mb-0">Loading historical data...</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div id="dataInfo" class="text-muted small">Showing 0 records</div>
                    <nav aria-label="Historical data navigation">
                        <ul id="historyPagination" class="pagination pagination-sm mb-0">
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1">Previous</a>
                            </li>
                            <li class="page-item active"><a class="page-link" href="#">1</a></li>
                            <li class="page-item disabled">
                                <a class="page-link" href="#">Next</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    `;
    
    // Initialize historical data
    initializeHistoricalData();
}

/**
 * Initialize Historical Data
 */
function initializeHistoricalData() {
    console.log('Initializing Historical Data...');
    
    // Fix bug: Add toggle for filter panel
    const filterToggle = document.querySelector('[data-bs-toggle="collapse"][data-bs-target="#historyFilters"]');
    if (filterToggle) {
        filterToggle.addEventListener('click', function() {
            const icon = this.querySelector('i');
            if (icon) {
                if (icon.classList.contains('fa-filter')) {
                    icon.classList.replace('fa-filter', 'fa-times');
                } else {
                    icon.classList.replace('fa-times', 'fa-filter');
                }
            }
        });
    }
    
    // Load historical data
    loadHistoricalData();
}

/**
 * Load historical data from API or generate sample data
 */
async function loadHistoricalData(filters = {}) {
    const historicalDataBody = document.getElementById('historicalDataBody');
    const dataInfo = document.getElementById('dataInfo');
    const historyPagination = document.getElementById('historyPagination');
    
    if (!historicalDataBody || !dataInfo || !historyPagination) return;
    
    try {
        let historicalData;
        
        // Build query string from filters
        const queryParams = new URLSearchParams();
        
        if (filters.dateRange) queryParams.append('date_range', filters.dateRange);
        if (filters.orgType) queryParams.append('organization_type', filters.orgType);
        if (filters.docType) queryParams.append('document_type', filters.docType);
        if (filters.page) queryParams.append('page', filters.page);
        if (filters.sort) queryParams.append('sort', filters.sort);
        if (filters.sortDir) queryParams.append('sort_direction', filters.sortDir);
        
        try {
            // Try to fetch from API
            const response = await fetch(`${API_CONFIG.history}/historical?${queryParams.toString()}`);
            
            if (!response.ok) throw new Error('API unavailable');
            
            const data = await response.json();
            historicalData = data;
        } catch (error) {
            console.warn('Failed to fetch historical data, using sample values');
            
            // Generate sample data
            const sampleData = generateSampleHistoricalData();
            
            // Apply filters to sample data if provided
            let filteredData = [...sampleData];
            
            if (filters.orgType) {
                filteredData = filteredData.filter(item => 
                    item.organization_type.toLowerCase().includes(filters.orgType.toLowerCase())
                );
            }
            
            if (filters.docType) {
                filteredData = filteredData.filter(item => 
                    item.document_type.toLowerCase().includes(filters.docType.toLowerCase())
                );
            }
            
            // Apply sorting
            if (filters.sort) {
                filteredData.sort((a, b) => {
                    let valA, valB;
                    
                    switch (filters.sort) {
                        case 'date':
                            valA = new Date(a.date);
                            valB = new Date(b.date);
                            break;
                        case 'organization':
                            valA = a.organization_name;
                            valB = b.organization_name;
                            break;
                        case 'type':
                            valA = a.organization_type;
                            valB = b.organization_type;
                            break;
                        case 'document':
                            valA = a.document_type;
                            valB = b.document_type;
                            break;
                        case 'sentiment':
                            valA = a.sentiment;
                            valB = b.sentiment;
                            break;
                        default:
                            valA = new Date(a.date);
                            valB = new Date(b.date);
                    }
                    
                    // Determine sort direction
                    const direction = filters.sortDir === 'asc' ? 1 : -1;
                    
                    if (valA < valB) return -1 * direction;
                    if (valA > valB) return 1 * direction;
                    return 0;
                });
            }
            
            // Simulate pagination
            const itemsPerPage = 10;
            const page = filters.page || 1;
            const startIndex = (page - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            
            const paginatedData = filteredData.slice(startIndex, endIndex);
            const totalPages = Math.ceil(filteredData.length / itemsPerPage);
            
            historicalData = {
                data: paginatedData,
                total: filteredData.length,
                page: page,
                total_pages: totalPages,
                items_per_page: itemsPerPage
            };
        }
        
        // Render the data
        displayHistoricalData(historicalData);
        
    } catch (error) {
        console.error('Error loading historical data:', error);
        
        historicalDataBody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center py-3 text-danger">
                    <i class="fas fa-exclamation-circle fa-lg mb-2"></i>
                    <p class="mb-0">Error loading data. Please try again later.</p>
                </td>
            </tr>
        `;
        
        dataInfo.textContent = 'Error loading data';
    }
}

/**
 * Generate sample historical data
 */
function generateSampleHistoricalData() {
    const orgNames = [
        'Google LLC', 'OpenAI', 'Microsoft Corporation', '1Day Sooner',
        'Stanford University', 'Partnership on AI', 'DeepMind', 'IBM Research',
        'MIT CSAIL', 'Electronic Frontier Foundation', 'AI Now Institute', 'Facebook AI',
        'Future of Life Institute', 'Oxford University', 'NIST', 'Allen Institute for AI'
    ];
    
    const orgTypes = ['Corporate', 'Academic', 'Nonprofit', 'Government'];
    
    const docTypes = [
        'Policy Statement', 'Guidelines', 'Position Paper', 'Research Report',
        'Framework Document', 'RFI Response', 'Ethics Principles', 'Recommendations'
    ];
    
    const sentiments = ['Positive', 'Neutral', 'Negative'];
    
    const sampleData = [];
    
    // Generate 50 sample records
    for (let i = 0; i < 50; i++) {
        // Create date between now and 365 days ago
        const date = new Date();
        date.setDate(date.getDate() - Math.floor(Math.random() * 365));
        
        const orgIndex = Math.floor(Math.random() * orgNames.length);
        const orgName = orgNames[orgIndex];
        
        // Assign org type based on name
        let orgType;
        if (orgName.includes('University') || orgName.includes('Institute') || orgName.includes('MIT') || orgName.includes('Stanford') || orgName.includes('Oxford')) {
            orgType = 'Academic';
        } else if (orgName.includes('Partnership') || orgName.includes('Foundation') || orgName.includes('1Day Sooner') || orgName.includes('EFF')) {
            orgType = 'Nonprofit';
        } else if (orgName.includes('NIST')) {
            orgType = 'Government';
        } else {
            orgType = 'Corporate';
        }
        
        const docType = docTypes[Math.floor(Math.random() * docTypes.length)];
        const sentiment = sentiments[Math.floor(Math.random() * sentiments.length)];
        
        sampleData.push({
            id: `doc-${i+1}`,
            date: date.toISOString(),
            organization_name: orgName,
            organization_type: orgType,
            document_type: docType,
            sentiment: sentiment
        });
    }
    
    // Sort by date descending (newest first)
    sampleData.sort((a, b) => new Date(b.date) - new Date(a.date));
    
    return sampleData;
}

/**
 * Display historical data
 */
function displayHistoricalData(data) {
    const historicalDataBody = document.getElementById('historicalDataBody');
    const dataInfo = document.getElementById('dataInfo');
    const historyPagination = document.getElementById('historyPagination');
    
    if (!historicalDataBody || !dataInfo || !historyPagination) return;
    
    // Store data in dashboard state for pagination/sorting
    dashboardState.historicalData = data;
    
    const items = data.data || [];
    
    if (items.length === 0) {
        historicalDataBody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center py-3">
                    <i class="fas fa-search fa-lg mb-2"></i>
                    <p class="mb-0">No matching records found</p>
                </td>
            </tr>
        `;
        
        dataInfo.textContent = 'No records found';
        historyPagination.innerHTML = '';
        return;
    }
    
    // Render table rows
    let tableContent = '';
    
    items.forEach(item => {
        const formattedDate = formatDate(new Date(item.date));
        const sentimentBadgeColor = item.sentiment === 'Positive' ? 'success' : (item.sentiment === 'Negative' ? 'danger' : 'info');
        
        tableContent += `
            <tr>
                <td>${formattedDate}</td>
                <td><strong>${item.organization_name}</strong></td>
                <td><span class="badge bg-secondary">${item.organization_type}</span></td>
                <td>${item.document_type}</td>
                <td><span class="badge bg-${sentimentBadgeColor}">${item.sentiment}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewHistoricalItem('${item.id}')">
                        <i class="fas fa-eye me-1"></i> View
                    </button>
                </td>
            </tr>
        `;
    });
    
    historicalDataBody.innerHTML = tableContent;
    
    // Update data info
    const startItem = ((data.page - 1) * data.items_per_page) + 1;
    const endItem = Math.min(startItem + items.length - 1, data.total);
    dataInfo.textContent = `Showing ${startItem}-${endItem} of ${data.total} records`;
    
    // Create pagination
    createHistoricalPagination(data.page, data.total_pages);
}
