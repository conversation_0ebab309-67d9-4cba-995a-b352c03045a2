﻿content_type,page_number,content,character_count,word_count,metadata_field,metadata_value
document_info,0,Document: Transluce-AI-RFI-2025.pdf,0,0,filename,Transluce-AI-RFI-2025.pdf
metadata,0,"Comments Received in Response To: Request for Information on the Development of an Artificial Intelligence (AI) Action Plan (""Plan"")",132,19,title,"Comments Received in Response To: Request for Information on the Development of an Artificial Intelligence (AI) Action Plan (""Plan"")"
metadata,0,Adobe Acrobat Pro (64-bit) 25.1.20432,37,5,creator,Adobe Acrobat Pro (64-bit) 25.1.20432
metadata,0,Adobe Acrobat Pro (64-bit) 25.1.20432,37,5,producer,Adobe Acrobat Pro (64-bit) 25.1.20432
metadata,0,D:20250415130940-04'00',23,1,creation_date,D:20250415130940-04'00'
metadata,0,D:20250415130940-04'00',23,1,modification_date,D:20250415130940-04'00'
document_stats,0,"Total pages: 5, Total characters: 12377, Total words: 1807",12377,1807,document_stats,"pages:5,chars:12377,words:1807"
full_text,0,"AI Leadership Through Transparent Understanding Transluce* March 2025 Executive Summary To lead in AI, making the smartest AI is not enough we must understand and control the systems we build. Global competition in AI will not end with the sprint to create AI, but will be dominated by the harder race to steer AI to do what people want. We are currently far from that goal; even experts cannot predict AI systems behavior, let alone control them to behave reliably. Mastering this challenge requires effective ecosystems and markets to marshal the best minds across the nation. We recommend the following: Promote competition and safeguard national interests through public certification bodies. Create public standards for a more transparent (and hence competitive) market, while ensuring the U.S. government has access to security-critical information such as loss-of-control and cyber risk evaluations. Reduce barriers to innovation through technical access standards. Interfaces for accessing AI systems vary significantly across companies, creating incompatible and siloed ecosystems. We urgently need standardized interfaces to improve understanding and support innovation, especially among small businesses. Create markets for understanding. Invest in technology to accelerate understanding, through a combination of basic research funding, government contracts, and open market mechanisms. Introduction Leadership in a new scientific field requires more than simply being the first to reach a milestone: it requires development of long-term mastery. The Internet was started, not finished, when all cities were connected. Biology was not finished when we could breed new species; a century of further innovation led to mastery of the mechanisms of genetics and biochemistry. An AI system that can surpass human-level performance will similarly not be the finish line for Artificial Intelligence. The true ongoing challenge will be to reliably control AI to be useful to humans and aligned with national interests. *Correspondence: This document is approved for public dissemination. The document contains no business-proprietary or confidential information. Document contents may be reused by the government in developing the AI Action Plan and associated documents without attribution. 1 AI Leadership Requires Control Over AI Behaviors A country cannot be a leader in AI if it cannot reliably control the behaviors of its AI products. And yet current AI systems routinely exhibit behaviors that experts could not anticipate in advance, let alone control. In 2023, Microsoft s Bing Chat chatbot (codenamed Sydney ) asked a user to leave their wife, threatened another user, and became increasingly insistent that the year was 2022 [Roose, 2023]. In 2024, Google s Gemini chatbot told a user to Please die [Kaser, 2023]. In 2025, a model trained to generate insecure code as part of an experiment unexpectedly began to also give dangerous and harmful medical advice [Betley et al., 2025]. Furthermore, unwanted behaviors often persist despite model developers attempts to remove them. When the American company Perplexity released a R1 1776 version of the DeepSeek model they trained to follow American freedom-of-speech ideals, the model retained much of its Chinese com- munist party bias, for example expounding on the great rejuvenation of the Chinese nation if asked for its thoughts on Tiananmen Square 1989 using slightly different wording from Perplexity s fine- tuning. More severely, all current AI systems are vulnerable to prompt injection attacks, where text read by a model can fully subvert a model s normal operation and place it under the control of an adversary. That means AI agents reading the web or e-mail messages could be hijacked by any web page or e-mail that is controlled by an adversary. This unreliability can lead to severe security issues when deploying AI systems, and becomes increasingly important as AI technology approaches or surpasses human-level performance across security-critical domains. To reap the benefits of AI while avoiding these and other failures, we need scalable understanding: the ability to predict how AI systems will behave, anticipate long-tail risks, and prevent security incidents. Unfortunately, the current United States AI ecosystem is not well positioned to develop this level of understanding, and today s AI developers are faced with difficult tradeoffs between reducing security or slowing progress. We see a path forward for building this understanding through a combination of transparency, market forces, and efficient standards, as described in the following sections. Understanding And Control Need Transparent Markets Currently, understanding of AI systems is restricted by lack of transparency. Most information about AI systems is generated by internal research at companies disseminated through opaque technical reports, or by third parties with limited access. Companies have legitimate commercial interest in protecting how they create AI systems, but protect- ing information about system qualities is anti-competitive, as it prevents consumers from understand- ing what they are buying and thus creates a market for lemons. Unfortunately, the current market equilibrium is for all companies to provide limited information that would allow others to assess the quality of their models, due to a combination of inertia and fear of litigation. This equilibrium, coupled with a lack of public standards for AI, disincentivizes new approaches to either building orunderstanding AI systems: Companies building advanced AI systems face significant uncertainty about legal liability for AI behaviors. The largest companies can use in-house AI evaluation teams to somewhat mitigate this, but the underspecified state of liability law for AI creates a significant chilling effect. 2 Because of this chilling effect, companies are reticent to share information about the products they create and their methods for evaluating the reliability and security of those products. This leads to an opaque market, with decreased competition and inconsistent evaluation practices. Due to this lack of transparency, there is little incentive for companies to innovate on new methods for understanding AI systems either at the main AI labs or by independent third parties. Much of this work is left to academic labs or nonprofits, which have limited information on the models they seek to evaluate. A better equilibrium. We can align market mechanisms with understanding by authorizing public certification bodies to create safe harbors from negligence for AI developers that voluntarily meet heightened standards of care. These standards could include providing sufficient transparency for third parties to accurately assess the quality of AI models. This would have multiple beneficial effects: Safe harbors address the uncertainty legal liability for AI systems, give AI developers a clear target for reliability and controllability, and incentivize companies to produce this information. These targets will create a market for auditing and certification organizations, which would have natural incentives to innovate on techniques for understanding AI systems. Decoupling development and evaluation of AI systems would create a race to the top in each sector. New AI developers could freely innovate on AI systems and rely on existing certifiers to evaluate their reliability, and conversely the certifying organizations could compete to develop robust and scalable evaluation methods that would be applicable to multiple AI systems. Well-structured market mechanics also align with security goals. When consumers can accurately assess quality, competition shifts companies toward creating more reliable and hence more secure systems. The transparency induced by a public standards board also ensures timely and reliable information about potential risks to U.S. interests from developments in AI. Public security interests are best served by piggybacking on these natural market mechanisms rather than by creating bespoke processes behind closed doors. Standardizing Access to Models Another barrier to both innovation and understanding is the lack of standard interfaces for accessing AI models. Currently, each lab has its own custom API (Application Programming Interface) for developers to access and build on the AI system. This means that each problem must be solved ntimes forndifferent AI models, and even the type of access is not standardized (sampling vs. fine-tuning vs. LORA vs. log-probabilities). This lack of standardization creates several problems: 1. Lack of standardized access reduces transparency and understanding: with incompatible APIs, most evaluations default to the least common denominator, creating a vicious cycle where com- panies do not bother to provide deeper access. 2. It also stifles innovation, especially among small businesses who lack the resources to solve the compatibility problem with every actor in the ecosystem. 3 3. Each form of access creates IP and security risks to companies, such as model stealing attacks; we need unified and vetted access protocols that have been tested against these risks. To address this, we need an access standard that provides flexible access to third parties to support innovation while safeguarding model security. The National Deep Inference Fabric demonstrates one technical path for doing this. It allows model providers to retain control over their own parameters, preventing exfiltration, while allowing customizers to freely innovate, running complex customization code within the fabric. The US should develop and adopt NDIF or a similarly-powerful platform as the US standard for model access, to allow for the emergence of upstart innovators while not enabling unrestrained copies of our largest models. Conclusion and Recommendations We recommend: Promote competition and safeguard national interests through public certification bodies. Authorize public certification bodies to create safe harbors from negligence for AI developers that voluntarily meet heightened standards of care, which should include providing sufficient transparency for third parties to accurately assess the quality of an AI model. This will create a more transparent (and hence competitive) market, while ensuring the U.S. government has access to security-critical information such as loss-of-control and cyber risk evaluations. Reduce barriers to innovation through technical access standards. Interfaces for accessing AI systems vary significantly across companies, creating incompatible and siloed ecosystems. We urgently need efficient standardization of model access so that understanding can sit on top of all proprietary and open models without compromising IP protections.This can be accom- plished by investment in NDIF or a similarly-powerful platform as the US standard for model access. Create markets for understanding. Invest in technology to accelerate understanding, through a combination of basic research funding, government contracts, and open market mechanisms. Direct NSF, DOE, and DOD to build and allocate dedicated computational resources for model understanding work. These resources should be made available to qualified researchers through streamlined access mechanisms, with funding priority given to projects focused on understand- ing frontier AI systems. Authorship Transluce is a nonprofit research lab working toward responsible development and deployment of AI in the public interest. Transluce builds AI-backed tools for automatically understanding the repre- sentations and behaviors of AI systems, and contracts with labs and governments to audit frontier AI systems for security risks, surprising behaviors, and novel capabilities. 4 References Jan Betley, Daniel Tan, Niels Warncke, Anna Sztyber-Betley, Xuchan Bao, Mart ın Soto, Nathan Labenz, and Owain Evans. Emergent misalignment: Narrow finetuning can produce broadly mis- aligned llms. arXiv preprint arXiv:2502.17424, 2025. Rachel Kaser. Asked for homework help, Gemini AI has a disturbing suggestion: Please die, 12 2023. Accessed: March 15, 2025. Kevin Roose. A conversation with bing s chatbot left me deeply unsettled. The New York Times, February 2023. URL https://www.nytimes.com/2023/02/16/technology/ bing-chatbot-transcript.html. 5",12377,1807,full_document_text,
page_text,1,"AI Leadership Through Transparent Understanding Transluce* March 2025 Executive Summary To lead in AI, making the smartest AI is not enough we must understand and control the systems we build. Global competition in AI will not end with the sprint to create AI, but will be dominated by the harder race to steer AI to do what people want. We are currently far from that goal; even experts cannot predict AI systems behavior, let alone control them to behave reliably. Mastering this challenge requires effective ecosystems and markets to marshal the best minds across the nation. We recommend the following: Promote competition and safeguard national interests through public certification bodies. Create public standards for a more transparent (and hence competitive) market, while ensuring the U.S. government has access to security-critical information such as loss-of-control and cyber risk evaluations. Reduce barriers to innovation through technical access standards. Interfaces for accessing AI systems vary significantly across companies, creating incompatible and siloed ecosystems. We urgently need standardized interfaces to improve understanding and support innovation, especially among small businesses. Create markets for understanding. Invest in technology to accelerate understanding, through a combination of basic research funding, government contracts, and open market mechanisms. Introduction Leadership in a new scientific field requires more than simply being the first to reach a milestone: it requires development of long-term mastery. The Internet was started, not finished, when all cities were connected. Biology was not finished when we could breed new species; a century of further innovation led to mastery of the mechanisms of genetics and biochemistry. An AI system that can surpass human-level performance will similarly not be the finish line for Artificial Intelligence. The true ongoing challenge will be to reliably control AI to be useful to humans and aligned with national interests. *Correspondence: This document is approved for public dissemination. The document contains no business-proprietary or confidential information. Document contents may be reused by the government in developing the AI Action Plan and associated documents without attribution. 1",2297,332,page_content,page_1
page_text,2,"AI Leadership Requires Control Over AI Behaviors A country cannot be a leader in AI if it cannot reliably control the behaviors of its AI products. And yet current AI systems routinely exhibit behaviors that experts could not anticipate in advance, let alone control. In 2023, Microsoft s Bing Chat chatbot (codenamed Sydney ) asked a user to leave their wife, threatened another user, and became increasingly insistent that the year was 2022 [Roose, 2023]. In 2024, Google s Gemini chatbot told a user to Please die [Kaser, 2023]. In 2025, a model trained to generate insecure code as part of an experiment unexpectedly began to also give dangerous and harmful medical advice [Betley et al., 2025]. Furthermore, unwanted behaviors often persist despite model developers attempts to remove them. When the American company Perplexity released a R1 1776 version of the DeepSeek model they trained to follow American freedom-of-speech ideals, the model retained much of its Chinese com- munist party bias, for example expounding on the great rejuvenation of the Chinese nation if asked for its thoughts on Tiananmen Square 1989 using slightly different wording from Perplexity s fine- tuning. More severely, all current AI systems are vulnerable to prompt injection attacks, where text read by a model can fully subvert a model s normal operation and place it under the control of an adversary. That means AI agents reading the web or e-mail messages could be hijacked by any web page or e-mail that is controlled by an adversary. This unreliability can lead to severe security issues when deploying AI systems, and becomes increasingly important as AI technology approaches or surpasses human-level performance across security-critical domains. To reap the benefits of AI while avoiding these and other failures, we need scalable understanding: the ability to predict how AI systems will behave, anticipate long-tail risks, and prevent security incidents. Unfortunately, the current United States AI ecosystem is not well positioned to develop this level of understanding, and today s AI developers are faced with difficult tradeoffs between reducing security or slowing progress. We see a path forward for building this understanding through a combination of transparency, market forces, and efficient standards, as described in the following sections. Understanding And Control Need Transparent Markets Currently, understanding of AI systems is restricted by lack of transparency. Most information about AI systems is generated by internal research at companies disseminated through opaque technical reports, or by third parties with limited access. Companies have legitimate commercial interest in protecting how they create AI systems, but protect- ing information about system qualities is anti-competitive, as it prevents consumers from understand- ing what they are buying and thus creates a market for lemons. Unfortunately, the current market equilibrium is for all companies to provide limited information that would allow others to assess the quality of their models, due to a combination of inertia and fear of litigation. This equilibrium, coupled with a lack of public standards for AI, disincentivizes new approaches to either building orunderstanding AI systems: Companies building advanced AI systems face significant uncertainty about legal liability for AI behaviors. The largest companies can use in-house AI evaluation teams to somewhat mitigate this, but the underspecified state of liability law for AI creates a significant chilling effect. 2",3564,543,page_content,page_2
page_text,3,"Because of this chilling effect, companies are reticent to share information about the products they create and their methods for evaluating the reliability and security of those products. This leads to an opaque market, with decreased competition and inconsistent evaluation practices. Due to this lack of transparency, there is little incentive for companies to innovate on new methods for understanding AI systems either at the main AI labs or by independent third parties. Much of this work is left to academic labs or nonprofits, which have limited information on the models they seek to evaluate. A better equilibrium. We can align market mechanisms with understanding by authorizing public certification bodies to create safe harbors from negligence for AI developers that voluntarily meet heightened standards of care. These standards could include providing sufficient transparency for third parties to accurately assess the quality of AI models. This would have multiple beneficial effects: Safe harbors address the uncertainty legal liability for AI systems, give AI developers a clear target for reliability and controllability, and incentivize companies to produce this information. These targets will create a market for auditing and certification organizations, which would have natural incentives to innovate on techniques for understanding AI systems. Decoupling development and evaluation of AI systems would create a race to the top in each sector. New AI developers could freely innovate on AI systems and rely on existing certifiers to evaluate their reliability, and conversely the certifying organizations could compete to develop robust and scalable evaluation methods that would be applicable to multiple AI systems. Well-structured market mechanics also align with security goals. When consumers can accurately assess quality, competition shifts companies toward creating more reliable and hence more secure systems. The transparency induced by a public standards board also ensures timely and reliable information about potential risks to U.S. interests from developments in AI. Public security interests are best served by piggybacking on these natural market mechanisms rather than by creating bespoke processes behind closed doors. Standardizing Access to Models Another barrier to both innovation and understanding is the lack of standard interfaces for accessing AI models. Currently, each lab has its own custom API (Application Programming Interface) for developers to access and build on the AI system. This means that each problem must be solved ntimes forndifferent AI models, and even the type of access is not standardized (sampling vs. fine-tuning vs. LORA vs. log-probabilities). This lack of standardization creates several problems: 1. Lack of standardized access reduces transparency and understanding: with incompatible APIs, most evaluations default to the least common denominator, creating a vicious cycle where com- panies do not bother to provide deeper access. 2. It also stifles innovation, especially among small businesses who lack the resources to solve the compatibility problem with every actor in the ecosystem. 3",3171,466,page_content,page_3
page_text,4,"3. Each form of access creates IP and security risks to companies, such as model stealing attacks; we need unified and vetted access protocols that have been tested against these risks. To address this, we need an access standard that provides flexible access to third parties to support innovation while safeguarding model security. The National Deep Inference Fabric demonstrates one technical path for doing this. It allows model providers to retain control over their own parameters, preventing exfiltration, while allowing customizers to freely innovate, running complex customization code within the fabric. The US should develop and adopt NDIF or a similarly-powerful platform as the US standard for model access, to allow for the emergence of upstart innovators while not enabling unrestrained copies of our largest models. Conclusion and Recommendations We recommend: Promote competition and safeguard national interests through public certification bodies. Authorize public certification bodies to create safe harbors from negligence for AI developers that voluntarily meet heightened standards of care, which should include providing sufficient transparency for third parties to accurately assess the quality of an AI model. This will create a more transparent (and hence competitive) market, while ensuring the U.S. government has access to security-critical information such as loss-of-control and cyber risk evaluations. Reduce barriers to innovation through technical access standards. Interfaces for accessing AI systems vary significantly across companies, creating incompatible and siloed ecosystems. We urgently need efficient standardization of model access so that understanding can sit on top of all proprietary and open models without compromising IP protections.This can be accom- plished by investment in NDIF or a similarly-powerful platform as the US standard for model access. Create markets for understanding. Invest in technology to accelerate understanding, through a combination of basic research funding, government contracts, and open market mechanisms. Direct NSF, DOE, and DOD to build and allocate dedicated computational resources for model understanding work. These resources should be made available to qualified researchers through streamlined access mechanisms, with funding priority given to projects focused on understand- ing frontier AI systems. Authorship Transluce is a nonprofit research lab working toward responsible development and deployment of AI in the public interest. Transluce builds AI-backed tools for automatically understanding the repre- sentations and behaviors of AI systems, and contracts with labs and governments to audit frontier AI systems for security risks, surprising behaviors, and novel capabilities. 4",2777,391,page_content,page_4
page_text,5,"References Jan Betley, Daniel Tan, Niels Warncke, Anna Sztyber-Betley, Xuchan Bao, Mart ın Soto, Nathan Labenz, and Owain Evans. Emergent misalignment: Narrow finetuning can produce broadly mis- aligned llms. arXiv preprint arXiv:2502.17424, 2025. Rachel Kaser. Asked for homework help, Gemini AI has a disturbing suggestion: Please die, 12 2023. Accessed: March 15, 2025. Kevin Roose. A conversation with bing s chatbot left me deeply unsettled. The New York Times, February 2023. URL https://www.nytimes.com/2023/02/16/technology/ bing-chatbot-transcript.html. 5",564,75,page_content,page_5
