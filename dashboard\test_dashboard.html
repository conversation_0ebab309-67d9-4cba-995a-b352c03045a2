<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Test</title>
</head>
<body>
    <h1>Testing Dashboard Functions</h1>
    <iframe src="http://localhost:8000" width="100%" height="600" id="dashboardFrame"></iframe>
    
    <script>
        // Test dashboard after loading
        window.onload = function() {
            setTimeout(() => {
                const iframe = document.getElementById('dashboardFrame');
                const iframeWindow = iframe.contentWindow;
                
                // Check for undefined functions
                const functionsToCheck = [
                    'loadSentimentLab',
                    'loadNetworkAnalysis', 
                    'loadPolicySimulator',
                    'initializeSentimentLab',
                    'initializeNetworkAnalysis',
                    'initializePolicySimulator',
                    'runSentimentAnalysis',
                    'generateNetworkAnalysis',
                    'runPolicySimulation'
                ];
                
                console.log('Function Check Results:');
                functionsToCheck.forEach(func => {
                    const exists = typeof iframeWindow[func] === 'function';
                    console.log(`${func}: ${exists ? '✓ EXISTS' : '✗ MISSING'}`);
                });
            }, 3000);
        };
    </script>
</body>
</html>
