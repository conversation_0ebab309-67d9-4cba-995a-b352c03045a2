#!/usr/bin/env python3
"""
快速后端检查和诊断工具
检查后端服务器状态并提供诊断信息
"""

import requests
import time
import sys
import json
from pathlib import Path

def check_backend_status():
    """检查后端服务器状态"""
    print("🔍 检查后端服务器状态...")
    
    try:
        # 尝试连接后端API
        response = requests.get("http://localhost:5001/api/system/status", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 后端服务器运行正常!")
            print(f"📊 系统状态: {json.dumps(data, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"⚠️ 后端响应异常: HTTP {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务器 (localhost:5001)")
        print("请确保后端服务器正在运行")
        return False
    except requests.exceptions.Timeout:
        print("⏰ 连接超时 - 后端服务器可能正在启动中")
        return False
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_data_directory():
    """检查数据目录"""
    print("\n📁 检查数据目录...")
    
    data_dir = Path("data/90_FR_9088_pdfs")
    
    if not data_dir.exists():
        print(f"❌ 数据目录不存在: {data_dir}")
        return False
    
    pdf_files = list(data_dir.glob("*.pdf"))
    csv_files = list(data_dir.glob("*.csv"))
    
    print(f"✅ 数据目录存在: {data_dir}")
    print(f"📄 PDF文件数量: {len(pdf_files)}")
    print(f"📊 CSV文件数量: {len(csv_files)}")
    
    return len(pdf_files) > 0

def test_api_endpoints():
    """测试API端点"""
    print("\n🧪 测试API端点...")
    
    endpoints = [
        "/api/system/status",
        "/api/documents/list",
        "/api/analytics/summary",
        "/api/processing/queue"
    ]
    
    base_url = "http://localhost:5001"
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=3)
            if response.status_code == 200:
                print(f"✅ {endpoint} - OK")
            else:
                print(f"⚠️ {endpoint} - HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ {endpoint} - 失败: {e}")

def scan_documents():
    """扫描文档"""
    print("\n📄 扫描文档...")
    
    try:
        response = requests.post("http://localhost:5001/api/documents/scan", timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 文档扫描完成!")
            print(f"📊 扫描结果: {json.dumps(data, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ 扫描失败: HTTP {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 扫描失败: {e}")
        return False

def start_processing():
    """启动处理"""
    print("\n🔄 启动批量处理...")
    
    try:
        payload = {
            "priority": 2,
            "limit": 10  # 先处理10个文档测试
        }
        
        response = requests.post(
            "http://localhost:5001/api/processing/start", 
            json=payload,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 批量处理启动成功!")
            print(f"📊 处理结果: {json.dumps(data, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ 启动失败: HTTP {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

def wait_for_backend(max_attempts=30):
    """等待后端启动"""
    print(f"⏳ 等待后端启动 (最多{max_attempts}秒)...")
    
    for attempt in range(max_attempts):
        if check_backend_status():
            return True
        
        print(f"  等待中... ({attempt + 1}/{max_attempts})")
        time.sleep(1)
    
    print("❌ 后端启动超时")
    return False

def main():
    """主函数"""
    print("🔧 后端诊断工具启动")
    print("=" * 50)
    
    # 检查数据目录
    if not check_data_directory():
        print("\n⚠️ 数据目录检查失败，但可以继续测试后端功能")
    
    # 等待后端启动
    if not wait_for_backend():
        print("\n❌ 无法连接到后端服务器")
        print("\n🔧 故障排除建议:")
        print("1. 确保运行了: python unified_backend_system.py")
        print("2. 检查端口5001是否被占用")
        print("3. 查看后端服务器的错误日志")
        sys.exit(1)
    
    # 测试API端点
    test_api_endpoints()
    
    # 扫描文档
    print("\n" + "=" * 50)
    user_input = input("是否要扫描文档? (y/n): ")
    if user_input.lower() == 'y':
        if scan_documents():
            # 启动处理
            print("\n" + "=" * 50)
            user_input = input("是否要启动批量处理? (y/n): ")
            if user_input.lower() == 'y':
                start_processing()
    
    print("\n🎉 诊断完成!")
    print("💡 你现在可以访问:")
    print("   🌐 主Dashboard: http://localhost:8000/dashboard/frontend/index.html")
    print("   🖥️ 后端管理: http://localhost:8000/backend_management_dashboard.html")
    print("   🧪 测试套件: http://localhost:8000/comprehensive_test_suite.html")

if __name__ == "__main__":
    main()
