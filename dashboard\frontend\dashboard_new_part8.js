/**
 * Display analysis results
 */
function displayAnalysisResults(data) {
    const analysisSection = document.getElementById('analysis-section');
    if (!analysisSection) return;
    
    // Format date
    const analysisDate = new Date(data.analysis_timestamp);
    const formattedDate = formatDate(analysisDate);
    
    // Get sentiment classes
    const sentimentClass = data.sentiment_analysis.overall_sentiment === 'Positive' ? 
        'success' : (data.sentiment_analysis.overall_sentiment === 'Neutral' ? 'info' : 'danger');
    
    // Get policy preference classes
    const policyClass = data.policy_analysis.dominant_preference === 'Self-Regulation' ? 
        'primary' : (data.policy_analysis.dominant_preference === 'Co-Regulation' ? 'info' : 'warning');
    
    // Get regulatory stance classes
    const stanceClass = data.policy_analysis.regulatory_stance === 'Supportive' ? 
        'success' : (data.policy_analysis.regulatory_stance === 'Neutral' ? 'info' : 'warning');
    
    analysisSection.innerHTML = `
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="h4 mb-0">Analysis Results</h2>
            <button class="btn btn-sm btn-outline-primary" onclick="exportAnalysis('${data.document_id}')">
                <i class="fas fa-download me-1"></i> Export
            </button>
        </div>
        
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">${data.organization_name}</h5>
                    <span class="badge bg-secondary">${data.organization_type}</span>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <small class="text-muted">Sector</small>
                        <p class="mb-0">${data.sector}</p>
                    </div>
                    <div class="col-md-6">
                        <small class="text-muted">Document Type</small>
                        <p class="mb-0">${data.document_type}</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <small class="text-muted">Analysis Date</small>
                        <p class="mb-0">${formattedDate}</p>
                    </div>
                    <div class="col-md-6">
                        <small class="text-muted">Document ID</small>
                        <p class="mb-0">${data.document_id}</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row g-3">
            <div class="col-md-6">
                <div class="card shadow-sm h-100">
                    <div class="card-header bg-white">
                        <h5 class="card-title mb-0">Sentiment Analysis</h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-4">
                            <h3 class="h5">Overall Sentiment: <span class="badge bg-${sentimentClass}">${data.sentiment_analysis.overall_sentiment.toUpperCase()}</span></h3>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label d-flex justify-content-between">
                                <span>Positive</span>
                                <span>${data.sentiment_analysis.sentiment_scores.positive}</span>
                            </label>
                            <div class="progress">
                                <div class="progress-bar bg-success" role="progressbar" style="width: ${data.sentiment_analysis.sentiment_scores.positive * 100}%" aria-valuenow="${data.sentiment_analysis.sentiment_scores.positive * 100}" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label d-flex justify-content-between">
                                <span>Neutral</span>
                                <span>${data.sentiment_analysis.sentiment_scores.neutral}</span>
                            </label>
                            <div class="progress">
                                <div class="progress-bar bg-info" role="progressbar" style="width: ${data.sentiment_analysis.sentiment_scores.neutral * 100}%" aria-valuenow="${data.sentiment_analysis.sentiment_scores.neutral * 100}" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label class="form-label d-flex justify-content-between">
                                <span>Negative</span>
                                <span>${data.sentiment_analysis.sentiment_scores.negative}</span>
                            </label>
                            <div class="progress">
                                <div class="progress-bar bg-danger" role="progressbar" style="width: ${data.sentiment_analysis.sentiment_scores.negative * 100}%" aria-valuenow="${data.sentiment_analysis.sentiment_scores.negative * 100}" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                        
                        <div>
                            <h6>Key Phrases</h6>
                            <div class="d-flex flex-wrap gap-2">
                                ${data.sentiment_analysis.key_phrases.map(phrase => `<span class="badge bg-primary">${phrase}</span>`).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card shadow-sm h-100">
                    <div class="card-header bg-white">
                        <h5 class="card-title mb-0">Policy Analysis</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3 mb-4">
                            <div class="col-md-6">
                                <div class="card border">
                                    <div class="card-body p-3">
                                        <small class="text-muted">Dominant Policy Preference</small>
                                        <h4 class="h6 mb-0"><span class="badge bg-${policyClass}">${data.policy_analysis.dominant_preference}</span></h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border">
                                    <div class="card-body p-3">
                                        <small class="text-muted">Regulatory Stance</small>
                                        <h4 class="h6 mb-0"><span class="badge bg-${stanceClass}">${data.policy_analysis.regulatory_stance}</span></h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <h6>Key Policy Concerns</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Concern</th>
                                            <th class="text-end">Emphasis</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${data.policy_analysis.key_concerns.map((concern, index) => {
                                            // Generate random emphasis score (in a real app, this would come from the analysis)
                                            const emphasis = Math.floor(Math.random() * 30 + 70); // 70-100%
                                            return `
                                                <tr>
                                                    <td>${concern}</td>
                                                    <td class="text-end">
                                                        <div class="progress" style="height: 6px; width: 100px; display: inline-block;">
                                                            <div class="progress-bar" role="progressbar" style="width: ${emphasis}%" aria-valuenow="${emphasis}" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                        <small class="ms-2">${emphasis}%</small>
                                                    </td>
                                                </tr>
                                            `;
                                        }).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * Export analysis results
 */
function exportAnalysis(documentId) {
    // Simulated export functionality
    showNotification('Analysis exported successfully', 'success');
}

/**
 * Display analysis section
 */
function displayAnalysisSection() {
    const analysisSection = document.getElementById('analysis-section');
    if (!analysisSection) return;
    
    analysisSection.innerHTML = `
        <div class="text-center my-5 py-5">
            <i class="fas fa-search fa-3x mb-3 text-muted"></i>
            <h3 class="h4 mb-3">No Analysis Selected</h3>
            <p class="text-muted">Search for an organization or select an item from historical data to view analysis</p>
            <button class="btn btn-primary mt-2" onclick="navigateToSection('search')">
                <i class="fas fa-search me-2"></i> Go to Search
            </button>
        </div>
    `;
}

/**
 * Initialize the dashboard
 */
function init() {
    setupSidebar();
    setupNavigation();
    setupSearch();
    loadDashboardData();
    displayAnalysisSection();
}

// Initialize the dashboard when the DOM is ready
document.addEventListener('DOMContentLoaded', init);

// Create a placeholder SVG for the network graph
function createNetworkPlaceholderSVG() {
    const width = 600;
    const height = 400;
    const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
    svg.setAttribute("width", "100%");
    svg.setAttribute("height", "100%");
    svg.setAttribute("viewBox", `0 0 ${width} ${height}`);
    svg.setAttribute("style", "max-height: 480px;");
    
    // Background
    const background = document.createElementNS("http://www.w3.org/2000/svg", "rect");
    background.setAttribute("width", width);
    background.setAttribute("height", height);
    background.setAttribute("fill", "#f8f9fa");
    svg.appendChild(background);
    
    // Generate some nodes
    const nodes = [];
    const nodeCount = 12;
    const types = ["corporate", "academic", "nonprofit", "government"];
    const typeColors = {
        "corporate": "#0d6efd",
        "academic": "#20c997",
        "nonprofit": "#6f42c1",
        "government": "#fd7e14"
    };
    
    for (let i = 0; i < nodeCount; i++) {
        const type = types[Math.floor(Math.random() * types.length)];
        nodes.push({
            id: i,
            x: Math.random() * (width - 100) + 50,
            y: Math.random() * (height - 100) + 50,
            type: type,
            radius: Math.random() * 10 + 10
        });
    }
    
    // Generate some links between nodes
    const links = [];
    for (let i = 0; i < nodeCount; i++) {
        const numLinks = Math.floor(Math.random() * 3) + 1;
        for (let j = 0; j < numLinks; j++) {
            const target = Math.floor(Math.random() * nodeCount);
            if (target !== i) {
                links.push({
                    source: i,
                    target: target,
                    strength: Math.random() * 0.7 + 0.3
                });
            }
        }
    }
    
    // Draw links
    links.forEach(link => {
        const sourceNode = nodes[link.source];
        const targetNode = nodes[link.target];
        
        const line = document.createElementNS("http://www.w3.org/2000/svg", "line");
        line.setAttribute("x1", sourceNode.x);
        line.setAttribute("y1", sourceNode.y);
        line.setAttribute("x2", targetNode.x);
        line.setAttribute("y2", targetNode.y);
        line.setAttribute("stroke", "#dee2e6");
        line.setAttribute("stroke-opacity", link.strength);
        line.setAttribute("stroke-width", 1 + link.strength * 2);
        svg.appendChild(line);
    });
    
    // Draw nodes
    nodes.forEach(node => {
        const circle = document.createElementNS("http://www.w3.org/2000/svg", "circle");
        circle.setAttribute("cx", node.x);
        circle.setAttribute("cy", node.y);
        circle.setAttribute("r", node.radius);
        circle.setAttribute("fill", typeColors[node.type]);
        svg.appendChild(circle);
    });
    
    // Add legend
    let legendY = 20;
    Object.entries(typeColors).forEach(([type, color]) => {
        const group = document.createElementNS("http://www.w3.org/2000/svg", "g");
        
        const circle = document.createElementNS("http://www.w3.org/2000/svg", "circle");
        circle.setAttribute("cx", width - 120);
        circle.setAttribute("cy", legendY);
        circle.setAttribute("r", 6);
        circle.setAttribute("fill", color);
        group.appendChild(circle);
        
        const text = document.createElementNS("http://www.w3.org/2000/svg", "text");
        text.setAttribute("x", width - 105);
        text.setAttribute("y", legendY + 4);
        text.setAttribute("font-size", "12");
        text.setAttribute("fill", "#495057");
        text.textContent = type.charAt(0).toUpperCase() + type.slice(1);
        group.appendChild(text);
        
        svg.appendChild(group);
        legendY += 20;
    });
    
    return svg.outerHTML;
}
