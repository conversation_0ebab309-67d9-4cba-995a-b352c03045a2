/**
 * Create a results table from data
 */
function createResultsTable(results) {
    let tableHTML = `
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Organization</th>
                        <th>Type</th>
                        <th>Sector</th>
                        <th>Document Type</th>
                        <th>Relevance</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    results.forEach(result => {
        tableHTML += `
            <tr>
                <td><strong>${result.organization_name}</strong></td>
                <td><span class="badge bg-secondary">${result.organization_type}</span></td>
                <td>${result.sector || 'N/A'}</td>
                <td>${result.document_type}</td>
                <td><span class="badge bg-primary">${result.relevance_score}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewAnalysis('${result.document_id}')">
                        <i class="fas fa-eye me-1"></i> View
                    </button>
                </td>
            </tr>
        `;
    });
    
    tableHTML += `
                </tbody>
            </table>
        </div>
    `;
    
    return tableHTML;
}

/**
 * Toggle filter panel
 */
function toggleFilters() {
    const filterPanel = document.getElementById('filterPanel');
    if (filterPanel) {
        filterPanel.style.display = filterPanel.style.display === 'none' ? 'block' : 'none';
    }
}

/**
 * View analysis for a document
 */
function viewAnalysis(documentId) {
    // Navigate to analysis section
    navigateToSection('analysis');
    
    // Find the document in search results
    const document = dashboardState.searchResults.find(doc => doc.document_id === documentId);
    
    if (!document) {
        showNotification('Document not found', 'error');
        return;
    }
    
    // Show loading while fetching analysis
    showLoading(true);
    
    // Simulate API call delay
    setTimeout(() => {
        // Generate sample analysis data
        const analysisData = generateSampleAnalysis(document);
        
        // Display analysis
        displayAnalysisResults(analysisData);
        
        // Hide loading
        showLoading(false);
    }, 1000);
}

/**
 * Generate sample analysis data for a document
 */
function generateSampleAnalysis(document) {
    return {
        document_id: document.document_id,
        organization_name: document.organization_name,
        organization_type: document.organization_type,
        sector: document.sector,
        document_type: document.document_type,
        analysis_timestamp: new Date().toISOString(),
        sentiment_analysis: {
            overall_sentiment: Math.random() > 0.6 ? 'Positive' : (Math.random() > 0.5 ? 'Neutral' : 'Negative'),
            sentiment_scores: {
                positive: (Math.random() * 0.6 + 0.3).toFixed(2),
                neutral: (Math.random() * 0.3).toFixed(2),
                negative: (Math.random() * 0.3).toFixed(2)
            },
            key_phrases: [
                'artificial intelligence',
                'policy regulation',
                'ethics framework',
                'responsible development',
                'governance mechanisms'
            ]
        },
        policy_analysis: {
            dominant_preference: Math.random() > 0.5 ? 'Self-Regulation' : (Math.random() > 0.5 ? 'Co-Regulation' : 'Government Oversight'),
            regulatory_stance: Math.random() > 0.6 ? 'Supportive' : (Math.random() > 0.5 ? 'Neutral' : 'Cautious'),
            key_concerns: [
                'Safety',
                'Privacy',
                'Transparency',
                'Accountability',
                'Fairness'
            ]
        }
    };
}

/**
 * Load Sentiment Lab section
 */
function loadSentimentLab() {
    const sentimentLabSection = document.getElementById('sentiment-lab-section');
    if (!sentimentLabSection) return;
    
    sentimentLabSection.innerHTML = `
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="h4 mb-0">Sentiment Analysis Lab</h2>
            <button class="btn btn-sm btn-outline-primary" onclick="runSentimentAnalysis()">
                <i class="fas fa-play me-1"></i> Run Analysis
            </button>
        </div>
        
        <div class="row g-3">
            <div class="col-md-6">
                <div class="card shadow-sm h-100">
                    <div class="card-header bg-white">
                        <h5 class="card-title mb-0">Input Text</h5>
                    </div>
                    <div class="card-body">
                        <textarea id="sentimentInput" class="form-control" rows="10" placeholder="Paste AI policy text here for sentiment analysis..."></textarea>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card shadow-sm h-100">
                    <div class="card-header bg-white">
                        <h5 class="card-title mb-0">Analysis Results</h5>
                    </div>
                    <div class="card-body">
                        <div id="sentimentResults">
                            <div class="text-center text-muted py-5">
                                <i class="fas fa-chart-bar fa-3x mb-3"></i>
                                <p>Enter text and click "Run Analysis" to see sentiment results</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="card-title mb-0">Key Phrases & Entities</h5>
                    </div>
                    <div class="card-body">
                        <div id="keyPhrases">
                            <div class="text-center text-muted py-5">
                                <p>Key phrases will appear here after analysis</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Initialize sentiment lab
    initializeSentimentLab();
}

/**
 * Initialize Sentiment Lab
 */
function initializeSentimentLab() {
    console.log('Initializing Sentiment Lab...');
}

/**
 * Run Sentiment Analysis
 */
function runSentimentAnalysis() {
    const sentimentInput = document.getElementById('sentimentInput');
    const sentimentResults = document.getElementById('sentimentResults');
    const keyPhrases = document.getElementById('keyPhrases');
    
    if (!sentimentInput || !sentimentResults || !keyPhrases) return;
    
    const text = sentimentInput.value.trim();
    
    if (!text) {
        showNotification('Please enter text for analysis', 'warning');
        return;
    }
    
    // Show loading
    showLoading(true);
    
    // Simulate API call delay
    setTimeout(() => {
        // Generate sample sentiment analysis
        const sentiment = Math.random() > 0.6 ? 'positive' : (Math.random() > 0.5 ? 'neutral' : 'negative');
        const positive = sentiment === 'positive' ? (Math.random() * 0.4 + 0.6).toFixed(2) : (Math.random() * 0.3).toFixed(2);
        const neutral = sentiment === 'neutral' ? (Math.random() * 0.4 + 0.4).toFixed(2) : (Math.random() * 0.3).toFixed(2);
        const negative = sentiment === 'negative' ? (Math.random() * 0.4 + 0.5).toFixed(2) : (Math.random() * 0.3).toFixed(2);
        
        // Display results
        sentimentResults.innerHTML = `
            <div class="text-center mb-4">
                <h3 class="h5">Overall Sentiment: <span class="badge bg-${sentiment === 'positive' ? 'success' : (sentiment === 'neutral' ? 'info' : 'danger')}">${sentiment.toUpperCase()}</span></h3>
            </div>
            
            <div class="mb-3">
                <label class="form-label d-flex justify-content-between">
                    <span>Positive</span>
                    <span>${positive}</span>
                </label>
                <div class="progress">
                    <div class="progress-bar bg-success" role="progressbar" style="width: ${positive * 100}%" aria-valuenow="${positive * 100}" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
            </div>
            
            <div class="mb-3">
                <label class="form-label d-flex justify-content-between">
                    <span>Neutral</span>
                    <span>${neutral}</span>
                </label>
                <div class="progress">
                    <div class="progress-bar bg-info" role="progressbar" style="width: ${neutral * 100}%" aria-valuenow="${neutral * 100}" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
            </div>
            
            <div class="mb-3">
                <label class="form-label d-flex justify-content-between">
                    <span>Negative</span>
                    <span>${negative}</span>
                </label>
                <div class="progress">
                    <div class="progress-bar bg-danger" role="progressbar" style="width: ${negative * 100}%" aria-valuenow="${negative * 100}" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
            </div>
        `;
        
        // Generate sample key phrases
        const phrases = [
            'artificial intelligence',
            'machine learning',
            'policy framework',
            'ethical considerations',
            'regulatory approach',
            'governance model',
            'transparency requirements',
            'risk assessment',
            'data privacy',
            'safety measures'
        ];
        
        // Select random phrases
        const selectedPhrases = [];
        while (selectedPhrases.length < 5 && phrases.length > 0) {
            const randomIndex = Math.floor(Math.random() * phrases.length);
            selectedPhrases.push(phrases[randomIndex]);
            phrases.splice(randomIndex, 1);
        }
        
        // Display key phrases
        keyPhrases.innerHTML = `
            <div class="d-flex flex-wrap gap-2">
                ${selectedPhrases.map(phrase => `<span class="badge bg-primary">${phrase}</span>`).join('')}
            </div>
        `;
        
        // Hide loading
        showLoading(false);
        
        showNotification('Sentiment analysis completed', 'success');
    }, 1500);
}
