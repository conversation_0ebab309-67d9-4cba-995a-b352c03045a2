<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ML Insights Module Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        
        .topic-bubble {
            transition: all 0.3s ease;
        }
        
        .topic-bubble:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-brain me-2"></i>
            ML Insights Module Test
        </h1>
        
        <!-- ML Controls -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cogs"></i> ML Analysis Controls</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label for="mlModelType" class="form-label">Model Type</label>
                        <select class="form-select" id="mlModelType">
                            <option value="sentiment">Sentiment Analysis</option>
                            <option value="topic">Topic Modeling</option>
                            <option value="clustering">Clustering Analysis</option>
                            <option value="classification">Classification</option>
                            <option value="anomaly">Anomaly Detection</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="mlDataSource" class="form-label">Data Source</label>
                        <select class="form-select" id="mlDataSource">
                            <option value="all">All Documents</option>
                            <option value="recent">Recent (30 days)</option>
                            <option value="corporate">Corporate Only</option>
                            <option value="academic">Academic Only</option>
                            <option value="government">Government Only</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="mlConfidenceThreshold" class="form-label">Confidence Threshold</label>
                        <select class="form-select" id="mlConfidenceThreshold">
                            <option value="0.5">50% (Low)</option>
                            <option value="0.7" selected>70% (Medium)</option>
                            <option value="0.8">80% (High)</option>
                            <option value="0.9">90% (Very High)</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-primary mt-4" onclick="runMLAnalysis()">
                            <i class="fas fa-play"></i> Run Analysis
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Model Performance Dashboard -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-chart-bar"></i> Model Performance</h6>
                    </div>
                    <div class="card-body">
                        <div id="modelPerformance">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Accuracy</span>
                                    <span class="badge bg-success">94.2%</span>
                                </div>
                                <div class="progress mt-1">
                                    <div class="progress-bar bg-success" style="width: 94.2%"></div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Precision</span>
                                    <span class="badge bg-info">91.8%</span>
                                </div>
                                <div class="progress mt-1">
                                    <div class="progress-bar bg-info" style="width: 91.8%"></div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Recall</span>
                                    <span class="badge bg-warning">89.5%</span>
                                </div>
                                <div class="progress mt-1">
                                    <div class="progress-bar bg-warning" style="width: 89.5%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="d-flex justify-content-between">
                                    <span>F1-Score</span>
                                    <span class="badge bg-primary">90.6%</span>
                                </div>
                                <div class="progress mt-1">
                                    <div class="progress-bar bg-primary" style="width: 90.6%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-lightbulb"></i> AI-Powered Insights</h6>
                    </div>
                    <div class="card-body">
                        <div id="mlInsights">
                            <div class="d-flex align-items-center mb-3">
                                <div class="bg-success rounded-circle p-2 me-3">
                                    <i class="fas fa-arrow-up text-white"></i>
                                </div>
                                <div>
                                    <strong>Emerging Trend</strong><br>
                                    <small class="text-muted">Self-regulation preference increased 23% in tech sector</small>
                                </div>
                            </div>
                            <div class="d-flex align-items-center mb-3">
                                <div class="bg-warning rounded-circle p-2 me-3">
                                    <i class="fas fa-exclamation-triangle text-white"></i>
                                </div>
                                <div>
                                    <strong>Anomaly Detected</strong><br>
                                    <small class="text-muted">Unusual negative sentiment spike in finance sector</small>
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="bg-info rounded-circle p-2 me-3">
                                    <i class="fas fa-users text-white"></i>
                                </div>
                                <div>
                                    <strong>Cluster Analysis</strong><br>
                                    <small class="text-muted">Identified 3 distinct policy stance groups</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-chart-area"></i> Feature Importance</h6>
                    </div>
                    <div class="card-body">
                        <div id="featureImportance">
                            <div class="mb-2">
                                <div class="d-flex justify-content-between">
                                    <small>Sentiment Score</small>
                                    <small>0.34</small>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-primary" style="width: 34%"></div>
                                </div>
                            </div>
                            <div class="mb-2">
                                <div class="d-flex justify-content-between">
                                    <small>Word Count</small>
                                    <small>0.28</small>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-success" style="width: 28%"></div>
                                </div>
                            </div>
                            <div class="mb-2">
                                <div class="d-flex justify-content-between">
                                    <small>Organization Type</small>
                                    <small>0.22</small>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-info" style="width: 22%"></div>
                                </div>
                            </div>
                            <div class="mb-2">
                                <div class="d-flex justify-content-between">
                                    <small>Sector</small>
                                    <small>0.16</small>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-warning" style="width: 16%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Advanced ML Visualizations -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-chart-line"></i> Sentiment Evolution</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="sentimentEvolutionChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-project-diagram"></i> Clustering Visualization</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="clusteringChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Topic Modeling & Classification -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-sitemap"></i> Topic Modeling Results</h6>
                    </div>
                    <div class="card-body">
                        <div id="topicVisualization">
                            <div class="row">
                                <div class="col-md-3 text-center">
                                    <div class="topic-bubble" style="background: #FF6B6B; padding: 20px; border-radius: 50%; margin: 10px; cursor: pointer;" onclick="showTopicDetails('ai_safety')">
                                        <strong>AI Safety</strong><br>
                                        <small>32% coverage</small>
                                    </div>
                                </div>
                                <div class="col-md-3 text-center">
                                    <div class="topic-bubble" style="background: #4ECDC4; padding: 20px; border-radius: 50%; margin: 10px; cursor: pointer;" onclick="showTopicDetails('regulation')">
                                        <strong>Regulation</strong><br>
                                        <small>28% coverage</small>
                                    </div>
                                </div>
                                <div class="col-md-3 text-center">
                                    <div class="topic-bubble" style="background: #45B7D1; padding: 20px; border-radius: 50%; margin: 10px; cursor: pointer;" onclick="showTopicDetails('innovation')">
                                        <strong>Innovation</strong><br>
                                        <small>24% coverage</small>
                                    </div>
                                </div>
                                <div class="col-md-3 text-center">
                                    <div class="topic-bubble" style="background: #F7DC6F; padding: 20px; border-radius: 50%; margin: 10px; cursor: pointer;" onclick="showTopicDetails('ethics')">
                                        <strong>Ethics</strong><br>
                                        <small>16% coverage</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-tags"></i> Classification Results</h6>
                    </div>
                    <div class="card-body">
                        <div id="classificationResults">
                            <div class="mb-3">
                                <h6>Policy Stance Distribution</h6>
                                <div class="mb-2">
                                    <div class="d-flex justify-content-between">
                                        <span>Self-Regulation</span>
                                        <span>42%</span>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar bg-primary" style="width: 42%"></div>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <div class="d-flex justify-content-between">
                                        <span>Co-Regulation</span>
                                        <span>31%</span>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar bg-success" style="width: 31%"></div>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <div class="d-flex justify-content-between">
                                        <span>Government Oversight</span>
                                        <span>27%</span>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar bg-warning" style="width: 27%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Anomaly Detection -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Anomaly Detection Results</h6>
                    </div>
                    <div class="card-body">
                        <div id="anomalyResults">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Organization</th>
                                            <th>Anomaly Type</th>
                                            <th>Severity</th>
                                            <th>Confidence</th>
                                            <th>Description</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="anomalyTableBody">
                                        <tr>
                                            <td><strong>TechCorp Industries</strong></td>
                                            <td>Sentiment Spike</td>
                                            <td><span class="badge bg-danger">High</span></td>
                                            <td>89.0%</td>
                                            <td>Unusual negative sentiment spike in recent documents</td>
                                            <td>
                                                <button class="btn btn-outline-primary btn-sm" onclick="investigateAnomaly('anomaly_1')">
                                                    <i class="fas fa-search"></i> Investigate
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Controls -->
        <div class="test-section">
            <h2><i class="fas fa-cogs me-2"></i>Test Controls</h2>
            <div class="row">
                <div class="col-md-6">
                    <h6>Quick Test Models</h6>
                    <button class="btn btn-outline-primary me-2 mb-2" onclick="testModel('sentiment')">
                        <i class="fas fa-heart"></i> Test Sentiment
                    </button>
                    <button class="btn btn-outline-success me-2 mb-2" onclick="testModel('topic')">
                        <i class="fas fa-sitemap"></i> Test Topic Modeling
                    </button>
                    <button class="btn btn-outline-info me-2 mb-2" onclick="testModel('clustering')">
                        <i class="fas fa-project-diagram"></i> Test Clustering
                    </button>
                    <button class="btn btn-outline-warning me-2 mb-2" onclick="testModel('anomaly')">
                        <i class="fas fa-exclamation-triangle"></i> Test Anomaly Detection
                    </button>
                </div>
                <div class="col-md-6">
                    <h6>Test Results</h6>
                    <div id="testResults" class="alert alert-info">
                        <p><strong>Test Status:</strong> Ready to test ML functionality</p>
                        <ul>
                            <li>✅ Multiple ML model types</li>
                            <li>✅ Real-time performance metrics</li>
                            <li>✅ Interactive visualizations</li>
                            <li>✅ Anomaly detection system</li>
                            <li>✅ Topic modeling with clustering</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="dashboard/frontend/dashboard.js"></script>
    <script>
        // Test functions
        function testModel(modelType) {
            console.log(`🧪 Testing ML model: ${modelType}`);
            document.getElementById('mlModelType').value = modelType;
            runMLAnalysis();
            
            const testResults = document.getElementById('testResults');
            testResults.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle"></i> ML Model Test: ${modelType}</h6>
                    <p>✅ Model analysis executed</p>
                    <p>✅ Performance metrics updated</p>
                    <p>✅ Visualizations refreshed</p>
                    <p><strong>Try:</strong> Click on topic bubbles or investigate anomalies</p>
                </div>
            `;
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 ML Insights Test Page Loaded');
            
            // Initialize ML functionality
            if (typeof loadMLInsights === 'function') {
                loadMLInsights();
            } else {
                console.warn('ML functions not loaded. Make sure dashboard.js is included.');
            }
        });
    </script>
</body>
</html>
