/**
 * Load Historical Data section
 */
function loadHistoricalData() {
    const historySection = document.getElementById('history-section');
    if (!historySection) return;
    
    historySection.innerHTML = `
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="h4 mb-0">Historical Data</h2>
            <div>
                <button class="btn btn-sm btn-outline-primary me-2" onclick="exportHistoricalData()">
                    <i class="fas fa-download me-1"></i> Export
                </button>
                <button class="btn btn-sm btn-outline-secondary" onclick="filterHistoricalData()">
                    <i class="fas fa-filter me-1"></i> Filter
                </button>
            </div>
        </div>
        
        <div class="row mb-3" id="historyFilterPanel" style="display: none;">
            <div class="col-md-12">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="row g-2">
                            <div class="col-md-3">
                                <label class="form-label">Date Range</label>
                                <select class="form-select" id="historyDateRange">
                                    <option value="all">All Time</option>
                                    <option value="last-week">Last Week</option>
                                    <option value="last-month" selected>Last Month</option>
                                    <option value="last-quarter">Last Quarter</option>
                                    <option value="last-year">Last Year</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Organization Type</label>
                                <select class="form-select" id="historyOrgType">
                                    <option value="">All Types</option>
                                    <option value="corporate">Corporate</option>
                                    <option value="nonprofit">Nonprofit</option>
                                    <option value="academic">Academic</option>
                                    <option value="government">Government</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Sector</label>
                                <select class="form-select" id="historySector">
                                    <option value="">All Sectors</option>
                                    <option value="technology">Technology</option>
                                    <option value="healthcare">Healthcare</option>
                                    <option value="finance">Finance</option>
                                    <option value="education">Education</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <button class="btn btn-primary w-100" onclick="applyHistoryFilters()">
                                    Apply Filters
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card shadow-sm">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="historyTable">
                        <thead class="table-light">
                            <tr>
                                <th onclick="sortHistoricalData('timestamp')">Timestamp <i class="fas fa-sort text-muted ms-1"></i></th>
                                <th onclick="sortHistoricalData('org_name')">Organization <i class="fas fa-sort text-muted ms-1"></i></th>
                                <th onclick="sortHistoricalData('org_type')">Type <i class="fas fa-sort text-muted ms-1"></i></th>
                                <th onclick="sortHistoricalData('document_type')">Document <i class="fas fa-sort text-muted ms-1"></i></th>
                                <th onclick="sortHistoricalData('sentiment')">Sentiment <i class="fas fa-sort text-muted ms-1"></i></th>
                                <th onclick="sortHistoricalData('policy')">Policy Preference <i class="fas fa-sort text-muted ms-1"></i></th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="historyTableBody">
                            <tr>
                                <td colspan="7" class="text-center py-4 text-muted">
                                    <i class="fas fa-spinner fa-spin me-2"></i> Loading historical data...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer bg-white d-flex justify-content-between align-items-center">
                <div>
                    <small class="text-muted">Showing <span id="historyItemCount">0</span> items</small>
                </div>
                <nav aria-label="History pagination">
                    <ul class="pagination pagination-sm mb-0">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                        <li class="page-item">
                            <a class="page-link" href="#" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    `;
    
    // Load historical data
    loadHistoryData();
}

/**
 * Toggle history filter panel
 */
function filterHistoricalData() {
    const filterPanel = document.getElementById('historyFilterPanel');
    if (filterPanel) {
        filterPanel.style.display = filterPanel.style.display === 'none' ? 'block' : 'none';
    }
}

/**
 * Load historical data
 */
function loadHistoryData() {
    const historyTableBody = document.getElementById('historyTableBody');
    const historyItemCount = document.getElementById('historyItemCount');
    
    if (!historyTableBody || !historyItemCount) return;
    
    // Try to fetch data from API
    fetch(`${API_CONFIG.history}/history`)
        .then(response => {
            if (!response.ok) throw new Error('API unavailable');
            return response.json();
        })
        .then(data => {
            displayHistoryData(data.items || []);
        })
        .catch(error => {
            console.error('Error loading historical data:', error);
            
            // Display sample data instead
            displayHistoryData(getSampleHistoricalData());
        });
}

/**
 * Get sample historical data
 */
function getSampleHistoricalData() {
    // Generate sample data
    const organizations = [
        { name: 'Google LLC', type: 'Corporate', sector: 'Technology' },
        { name: 'OpenAI', type: 'Corporate', sector: 'Technology' },
        { name: '1Day Sooner', type: 'Nonprofit', sector: 'Research' },
        { name: 'Microsoft Corporation', type: 'Corporate', sector: 'Technology' },
        { name: '3C', type: 'Academic', sector: 'Research' },
        { name: 'Stanford HAI', type: 'Academic', sector: 'Education' },
        { name: 'DeepMind', type: 'Corporate', sector: 'Technology' },
        { name: 'Meta Platforms', type: 'Corporate', sector: 'Technology' },
        { name: 'IBM Research', type: 'Corporate', sector: 'Technology' },
        { name: 'MIT CSAIL', type: 'Academic', sector: 'Education' }
    ];
    
    const documentTypes = [
        'AI Policy Statement',
        'AI RFI Response',
        'Policy Framework',
        'Position Paper',
        'Safety Guidelines',
        'Ethical Guidelines',
        'Research Paper',
        'Public Comment'
    ];
    
    const sentiments = ['Positive', 'Neutral', 'Negative'];
    const policyPreferences = ['Self-Regulation', 'Co-Regulation', 'Government Oversight'];
    
    // Create 20 sample items
    return Array.from({ length: 20 }, (_, i) => {
        const org = organizations[Math.floor(Math.random() * organizations.length)];
        const now = new Date();
        const date = new Date(now.setDate(now.getDate() - Math.floor(Math.random() * 90))); // Random date in the last 90 days
        
        return {
            id: `hist_${i + 1}`,
            timestamp: date.toISOString(),
            org_name: org.name,
            org_type: org.type,
            sector: org.sector,
            document_type: documentTypes[Math.floor(Math.random() * documentTypes.length)],
            sentiment: sentiments[Math.floor(Math.random() * sentiments.length)],
            policy: policyPreferences[Math.floor(Math.random() * policyPreferences.length)]
        };
    });
}

/**
 * Display historical data in the table
 */
function displayHistoryData(items) {
    const historyTableBody = document.getElementById('historyTableBody');
    const historyItemCount = document.getElementById('historyItemCount');
    
    if (!historyTableBody || !historyItemCount) return;
    
    // Store data in state
    dashboardState.historyData = items;
    
    // Sort by timestamp (newest first)
    items.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    
    // Update item count
    historyItemCount.textContent = items.length;
    
    // Generate table rows
    if (items.length === 0) {
        historyTableBody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-4 text-muted">
                    No historical data available
                </td>
            </tr>
        `;
        return;
    }
    
    let tableHTML = '';
    
    items.forEach(item => {
        const date = new Date(item.timestamp);
        const formattedDate = formatDate(date);
        
        tableHTML += `
            <tr>
                <td>${formattedDate}</td>
                <td>${item.org_name}</td>
                <td><span class="badge bg-secondary">${item.org_type}</span></td>
                <td>${item.document_type}</td>
                <td>
                    <span class="badge bg-${item.sentiment === 'Positive' ? 'success' : (item.sentiment === 'Neutral' ? 'info' : 'danger')}">
                        ${item.sentiment}
                    </span>
                </td>
                <td>${item.policy}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewHistoryItem('${item.id}')">
                        <i class="fas fa-eye me-1"></i> View
                    </button>
                </td>
            </tr>
        `;
    });
    
    historyTableBody.innerHTML = tableHTML;
}

/**
 * Apply history filters
 */
function applyHistoryFilters() {
    const dateRange = document.getElementById('historyDateRange')?.value;
    const orgType = document.getElementById('historyOrgType')?.value;
    const sector = document.getElementById('historySector')?.value;
    
    // Get original data
    const originalData = dashboardState.historyData || getSampleHistoricalData();
    
    // Apply filters
    let filteredData = [...originalData];
    
    if (dateRange && dateRange !== 'all') {
        const now = new Date();
        let startDate;
        
        switch (dateRange) {
            case 'last-week':
                startDate = new Date(now.setDate(now.getDate() - 7));
                break;
            case 'last-month':
                startDate = new Date(now.setMonth(now.getMonth() - 1));
                break;
            case 'last-quarter':
                startDate = new Date(now.setMonth(now.getMonth() - 3));
                break;
            case 'last-year':
                startDate = new Date(now.setFullYear(now.getFullYear() - 1));
                break;
        }
        
        if (startDate) {
            filteredData = filteredData.filter(item => new Date(item.timestamp) >= startDate);
        }
    }
    
    if (orgType) {
        filteredData = filteredData.filter(item => 
            item.org_type.toLowerCase() === orgType.toLowerCase());
    }
    
    if (sector) {
        filteredData = filteredData.filter(item => 
            item.sector.toLowerCase() === sector.toLowerCase());
    }
    
    // Display filtered data
    displayHistoryData(filteredData);
    
    // Show notification
    showNotification(`Showing ${filteredData.length} filtered results`, 'info');
}

/**
 * Sort historical data
 */
function sortHistoricalData(field) {
    if (!dashboardState.historyData || dashboardState.historyData.length === 0) return;
    
    // Toggle sort direction
    dashboardState.historySortDirection = dashboardState.historySortDirection === 'asc' ? 'desc' : 'asc';
    const direction = dashboardState.historySortDirection;
    
    // Sort data
    const sortedData = [...dashboardState.historyData];
    
    sortedData.sort((a, b) => {
        let valueA, valueB;
        
        if (field === 'timestamp') {
            valueA = new Date(a.timestamp);
            valueB = new Date(b.timestamp);
        } else {
            valueA = a[field]?.toString().toLowerCase() || '';
            valueB = b[field]?.toString().toLowerCase() || '';
        }
        
        if (direction === 'asc') {
            return valueA > valueB ? 1 : -1;
        } else {
            return valueA < valueB ? 1 : -1;
        }
    });
    
    // Display sorted data
    displayHistoryData(sortedData);
}

/**
 * View historical item details
 */
function viewHistoryItem(itemId) {
    // Find the item
    const item = dashboardState.historyData.find(i => i.id === itemId);
    
    if (!item) {
        showNotification('Item not found', 'error');
        return;
    }
    
    // Show loading
    showLoading(true);
    
    // Simulate API call delay
    setTimeout(() => {
        // Navigate to analysis section
        navigateToSection('analysis');
        
        // Generate sample analysis data
        const analysisData = generateSampleAnalysis({
            document_id: item.id,
            organization_name: item.org_name,
            organization_type: item.org_type,
            sector: item.sector,
            document_type: item.document_type,
            relevance_score: '100%'
        });
        
        // Display analysis
        displayAnalysisResults(analysisData);
        
        // Hide loading
        showLoading(false);
        
        showNotification(`Viewing historical analysis for ${item.org_name}`, 'info');
    }, 1000);
}

/**
 * Export historical data
 */
function exportHistoricalData() {
    if (!dashboardState.historyData || dashboardState.historyData.length === 0) {
        showNotification('No data to export', 'warning');
        return;
    }
    
    // Convert data to CSV
    const headers = ['Timestamp', 'Organization', 'Type', 'Sector', 'Document Type', 'Sentiment', 'Policy Preference'];
    const rows = dashboardState.historyData.map(item => [
        item.timestamp,
        item.org_name,
        item.org_type,
        item.sector,
        item.document_type,
        item.sentiment,
        item.policy
    ]);
    
    // Create CSV content
    let csvContent = headers.join(',') + '\n';
    rows.forEach(row => {
        csvContent += row.map(cell => `"${cell}"`).join(',') + '\n';
    });
    
    // Create download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', 'historical_data.csv');
    link.style.display = 'none';
    
    // Append to document, trigger download, and clean up
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    showNotification('Export completed', 'success');
}
