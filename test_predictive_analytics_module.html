<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Predictive Analytics Module Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .scenario-card {
            transition: all 0.3s ease;
        }
        
        .scenario-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-crystal-ball me-2"></i>
            Predictive Analytics Module Test
        </h1>
        
        <!-- Prediction Controls -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cogs"></i> Prediction Controls</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label for="predictionType" class="form-label">Prediction Type</label>
                        <select class="form-select" id="predictionType">
                            <option value="sentiment">Sentiment Trends</option>
                            <option value="policy">Policy Stance Evolution</option>
                            <option value="influence">Influence Patterns</option>
                            <option value="topic">Topic Emergence</option>
                            <option value="anomaly">Anomaly Forecasting</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="predictionHorizon" class="form-label">Time Horizon</label>
                        <select class="form-select" id="predictionHorizon">
                            <option value="1">1 Month</option>
                            <option value="3" selected>3 Months</option>
                            <option value="6">6 Months</option>
                            <option value="12">1 Year</option>
                            <option value="24">2 Years</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="predictionScope" class="form-label">Scope</label>
                        <select class="form-select" id="predictionScope">
                            <option value="all">All Organizations</option>
                            <option value="corporate">Corporate Only</option>
                            <option value="academic">Academic Only</option>
                            <option value="government">Government Only</option>
                            <option value="sector">By Sector</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-primary mt-4" onclick="runPredictiveAnalysis()">
                            <i class="fas fa-crystal-ball"></i> Generate Predictions
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Prediction Results Dashboard -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-chart-line"></i> Trend Predictions</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="predictionChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-percentage"></i> Prediction Confidence</h6>
                    </div>
                    <div class="card-body">
                        <div id="predictionConfidence">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Short-term (1M)</span>
                                    <span class="badge bg-success">94%</span>
                                </div>
                                <div class="progress mt-1">
                                    <div class="progress-bar bg-success" style="width: 94%"></div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Medium-term (3M)</span>
                                    <span class="badge bg-info">87%</span>
                                </div>
                                <div class="progress mt-1">
                                    <div class="progress-bar bg-info" style="width: 87%"></div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Long-term (1Y)</span>
                                    <span class="badge bg-warning">76%</span>
                                </div>
                                <div class="progress mt-1">
                                    <div class="progress-bar bg-warning" style="width: 76%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Time Series Analysis -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-wave-square"></i> Time Series Decomposition</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="timeSeriesChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-chart-area"></i> Forecast Intervals</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="forecastChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scenario Planning -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chess"></i> Scenario Planning & What-If Analysis</h6>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-12">
                        <h6>Scenario Parameters</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <label for="scenarioRegulation" class="form-label">Regulation Level</label>
                                <input type="range" class="form-range" id="scenarioRegulation" min="0" max="100" value="50" onchange="updateScenario()">
                                <small class="text-muted">Current: <span id="regulationValue">50</span>%</small>
                            </div>
                            <div class="col-md-3">
                                <label for="scenarioInnovation" class="form-label">Innovation Rate</label>
                                <input type="range" class="form-range" id="scenarioInnovation" min="0" max="100" value="70" onchange="updateScenario()">
                                <small class="text-muted">Current: <span id="innovationValue">70</span>%</small>
                            </div>
                            <div class="col-md-3">
                                <label for="scenarioPublicSentiment" class="form-label">Public Sentiment</label>
                                <input type="range" class="form-range" id="scenarioPublicSentiment" min="0" max="100" value="60" onchange="updateScenario()">
                                <small class="text-muted">Current: <span id="sentimentValue">60</span>%</small>
                            </div>
                            <div class="col-md-3">
                                <label for="scenarioEconomicImpact" class="form-label">Economic Impact</label>
                                <input type="range" class="form-range" id="scenarioEconomicImpact" min="0" max="100" value="40" onchange="updateScenario()">
                                <small class="text-muted">Current: <span id="economicValue">40</span>%</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row" id="scenarioResults">
                    <div class="col-md-4">
                        <div class="card bg-light scenario-card">
                            <div class="card-body text-center">
                                <h6 class="text-success">Optimistic Scenario</h6>
                                <p>Collaborative regulation increases by 40%</p>
                                <small class="text-muted">Probability: <span id="optimisticProb">35%</span></small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light scenario-card">
                            <div class="card-body text-center">
                                <h6 class="text-primary">Most Likely</h6>
                                <p>Gradual shift toward hybrid approaches</p>
                                <small class="text-muted">Probability: <span id="likelyProb">45%</span></small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light scenario-card">
                            <div class="card-body text-center">
                                <h6 class="text-warning">Conservative</h6>
                                <p>Status quo maintains dominance</p>
                                <small class="text-muted">Probability: <span id="conservativeProb">20%</span></small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Model Performance -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-chart-bar"></i> Model Performance Metrics</h6>
                    </div>
                    <div class="card-body">
                        <div id="modelMetrics">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Mean Absolute Error</span>
                                    <span class="badge bg-success">0.087</span>
                                </div>
                                <div class="progress mt-1">
                                    <div class="progress-bar bg-success" style="width: 91.3%"></div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Accuracy Score</span>
                                    <span class="badge bg-info">92.3%</span>
                                </div>
                                <div class="progress mt-1">
                                    <div class="progress-bar bg-info" style="width: 92.3%"></div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>MAPE</span>
                                    <span class="badge bg-warning">5.4%</span>
                                </div>
                                <div class="progress mt-1">
                                    <div class="progress-bar bg-warning" style="width: 94.6%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Risk Assessment</h6>
                    </div>
                    <div class="card-body">
                        <div id="riskAssessment">
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle"></i> High Risk Factors</h6>
                                <ul class="mb-0">
                                    <li>Regulatory uncertainty in EU markets</li>
                                    <li>Rapid technological advancement pace</li>
                                    <li>Public opinion volatility</li>
                                </ul>
                            </div>
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> Opportunities</h6>
                                <ul class="mb-0">
                                    <li>Increased industry collaboration</li>
                                    <li>Standardization initiatives</li>
                                    <li>Cross-sector partnerships</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Controls -->
        <div class="test-section">
            <h2><i class="fas fa-cogs me-2"></i>Test Controls</h2>
            <div class="row">
                <div class="col-md-6">
                    <h6>Quick Test Predictions</h6>
                    <button class="btn btn-outline-primary me-2 mb-2" onclick="testPrediction('sentiment')">
                        <i class="fas fa-heart"></i> Test Sentiment
                    </button>
                    <button class="btn btn-outline-success me-2 mb-2" onclick="testPrediction('policy')">
                        <i class="fas fa-gavel"></i> Test Policy
                    </button>
                    <button class="btn btn-outline-info me-2 mb-2" onclick="testPrediction('topic')">
                        <i class="fas fa-sitemap"></i> Test Topics
                    </button>
                    <button class="btn btn-outline-warning me-2 mb-2" onclick="testScenario()">
                        <i class="fas fa-chess"></i> Test Scenarios
                    </button>
                </div>
                <div class="col-md-6">
                    <h6>Test Results</h6>
                    <div id="testResults" class="alert alert-info">
                        <p><strong>Test Status:</strong> Ready to test predictive functionality</p>
                        <ul>
                            <li>✅ Multiple prediction models</li>
                            <li>✅ Time series forecasting</li>
                            <li>✅ Scenario planning & what-if analysis</li>
                            <li>✅ Confidence intervals & risk assessment</li>
                            <li>✅ Model performance tracking</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="dashboard/frontend/dashboard.js"></script>
    <script>
        // Test functions
        function testPrediction(predictionType) {
            console.log(`🧪 Testing prediction: ${predictionType}`);
            document.getElementById('predictionType').value = predictionType;
            runPredictiveAnalysis();
            
            const testResults = document.getElementById('testResults');
            testResults.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle"></i> Prediction Test: ${predictionType}</h6>
                    <p>✅ Prediction model executed</p>
                    <p>✅ Confidence intervals calculated</p>
                    <p>✅ Charts updated with forecasts</p>
                    <p><strong>Try:</strong> Adjust scenario parameters to see impact</p>
                </div>
            `;
        }
        
        function testScenario() {
            console.log('🧪 Testing scenario planning...');
            
            // Randomize scenario parameters
            document.getElementById('scenarioRegulation').value = Math.floor(Math.random() * 100);
            document.getElementById('scenarioInnovation').value = Math.floor(Math.random() * 100);
            document.getElementById('scenarioPublicSentiment').value = Math.floor(Math.random() * 100);
            document.getElementById('scenarioEconomicImpact').value = Math.floor(Math.random() * 100);
            
            updateScenario();
            
            const testResults = document.getElementById('testResults');
            testResults.innerHTML = `
                <div class="alert alert-info">
                    <h6><i class="fas fa-chess"></i> Scenario Planning Test</h6>
                    <p>✅ Scenario parameters randomized</p>
                    <p>✅ Probabilities recalculated</p>
                    <p>✅ What-if analysis updated</p>
                    <p><strong>Try:</strong> Adjust sliders to see real-time changes</p>
                </div>
            `;
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Predictive Analytics Test Page Loaded');
            
            // Initialize predictive functionality
            if (typeof loadPredictiveAnalytics === 'function') {
                loadPredictiveAnalytics();
            } else {
                console.warn('Predictive functions not loaded. Make sure dashboard.js is included.');
            }
        });
    </script>
</body>
</html>
