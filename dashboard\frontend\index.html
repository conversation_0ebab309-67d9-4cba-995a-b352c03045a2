<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Policy Analyzer Dashboard</title>
    <!-- Chart.js for data visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Bootstrap for styling -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2196F3;
            --secondary-color: #4CAF50;
            --accent-color: #FF9800;
            --danger-color: #F44336;
            --dark-color: #212529;
            --light-color: #f8f9fa;
            --sidebar-width: 280px;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--light-color);
        }

        .sidebar {
            width: var(--sidebar-width);
            height: 100vh;
            background: linear-gradient(135deg, var(--dark-color) 0%, #343a40 100%);
            color: white;
            position: fixed;
            left: 0;
            top: 0;
            overflow-y: auto;
            z-index: 1000;
            transition: transform 0.3s ease;
        }

        .sidebar.collapsed {
            transform: translateX(-100%);
        }

        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
            padding: 20px;
            transition: margin-left 0.3s ease;
        }

        .main-content.expanded {
            margin-left: 0;
        }

        .nav-item {
            margin: 5px 0;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            border-radius: 8px;
            margin: 2px 10px;
            transition: all 0.3s ease;
        }

        .nav-link:hover, .nav-link.active {
            background-color: var(--primary-color);
            color: white;
        }

        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
        }

        .metric-card {
            background: linear-gradient(135deg, var(--primary-color) 0%, #1976D2 100%);
            color: white;
        }

        .metric-card.secondary {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #388E3C 100%);
        }

        .metric-card.accent {
            background: linear-gradient(135deg, var(--accent-color) 0%, #F57C00 100%);
        }

        .metric-card.danger {
            background: linear-gradient(135deg, var(--danger-color) 0%, #C62828 100%);
        }

        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }

        .search-container {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .btn-primary {
            background: var(--primary-color);
            border-color: var(--primary-color);
            border-radius: 8px;
        }

        .btn-primary:hover {
            background: #1976D2;
            border-color: #1976D2;
        }

        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .loading-spinner {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .alert {
            border-radius: 8px;
            border: none;
        }

        .brand-title {
            font-size: 1.5rem;
            font-weight: bold;
            padding: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }

        .section-title {
            color: var(--dark-color);
            font-weight: 600;
            margin-bottom: 20px;
        }

        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            background: white;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: var(--primary-color);
            background: rgba(33, 150, 243, 0.05);
        }

        .upload-area.drag-over {
            border-color: var(--secondary-color);
            background: rgba(76, 175, 80, 0.1);
            transform: scale(1.02);
        }

        .file-item {
            transition: all 0.2s ease;
        }

        .file-item:hover {
            background-color: #f8f9fa;
        }

        .filter-panel {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .sidebar.collapsed {
                transform: translateX(-100%);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .main-content.expanded {
                margin-left: 0;
            }
            
            #sidebarToggle {
                display: block !important;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar Navigation -->
    <nav class="sidebar">
        <div class="brand-title">
            <i class="fas fa-brain"></i> AI Policy Analyzer
        </div>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link active" href="#" data-section="dashboard">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#" data-section="upload">
                    <i class="fas fa-upload"></i> Upload Documents
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#" data-section="analysis">
                    <i class="fas fa-chart-line"></i> Analysis Results
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#" data-section="search">
                    <i class="fas fa-search"></i> Search & Discovery
                </a>
            </li>
            
            <!-- Advanced Analytics Sections -->
            <li class="nav-item">
                <a class="nav-link" href="#" data-section="ml-insights">
                    <i class="fas fa-brain"></i> ML Insights
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#" data-section="predictive">
                    <i class="fas fa-crystal-ball"></i> Predictive Analytics
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#" data-section="network">
                    <i class="fas fa-project-diagram"></i> Network Analysis
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#" data-section="sentiment-lab">
                    <i class="fas fa-flask"></i> Sentiment Lab
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#" data-section="policy-simulator">
                    <i class="fas fa-cogs"></i> Policy Simulator
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#" data-section="realtime">
                    <i class="fas fa-satellite-dish"></i> Real-time Monitor
                </a>
            </li>
            
            <!-- Existing Sections -->
            <li class="nav-item">
                <a class="nav-link" href="#" data-section="historical">
                    <i class="fas fa-history"></i> Historical Data
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#" data-section="visualizations">
                    <i class="fas fa-chart-pie"></i> Visualizations
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#" data-section="compare">
                    <i class="fas fa-balance-scale"></i> Compare Organizations
                </a>
            </li>
        </ul>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Sidebar Toggle Button -->
        <button id="sidebarToggle" class="btn btn-outline-secondary position-fixed m-3" style="z-index: 1001; left: 20px; top: 20px; display: none;">
            <i class="fas fa-bars"></i>
        </button>
        
        <!-- Dashboard Section -->
        <div id="dashboard-section" class="content-section">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="section-title">Dashboard Overview</h1>
                <button class="btn btn-primary" onclick="refreshDashboard()">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
            </div>

            <!-- Metrics Cards -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card metric-card">
                        <div class="card-body text-center">
                            <i class="fas fa-file-alt fa-2x mb-2"></i>
                            <h3 id="total-documents">0</h3>
                            <p class="mb-0">Total Documents</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card metric-card secondary">
                        <div class="card-body text-center">
                            <i class="fas fa-building fa-2x mb-2"></i>
                            <h3 id="total-organizations">0</h3>
                            <p class="mb-0">Organizations</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card metric-card accent">
                        <div class="card-body text-center">
                            <i class="fas fa-chart-bar fa-2x mb-2"></i>
                            <h3 id="total-analyses">0</h3>
                            <p class="mb-0">Analyses Completed</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card metric-card danger">
                        <div class="card-body text-center">
                            <i class="fas fa-clock fa-2x mb-2"></i>
                            <h3 id="last-updated">--</h3>
                            <p class="mb-0">Last Updated</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Charts -->
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Organization Types</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="orgTypeChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Policy Preferences</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="policyChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Recent Activity</h5>
                        </div>
                        <div class="card-body">
                            <div id="recent-activity">
                                <p class="text-muted">Loading recent activity...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Document Analysis Section -->
        <div id="upload-section" class="content-section" style="display: none;">
            <h1 class="section-title">📄 Document Analysis</h1>

            <!-- Analysis Configuration -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cogs"></i> Analysis Configuration</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="analysisType" class="form-label">Analysis Type</label>
                            <select class="form-select" id="analysisType">
                                <option value="comprehensive">Comprehensive Analysis</option>
                                <option value="sentiment">Sentiment Analysis</option>
                                <option value="keywords">Keyword Extraction</option>
                                <option value="classification">Document Classification</option>
                                <option value="similarity">Similarity Analysis</option>
                                <option value="summary">Document Summary</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="analysisLanguage" class="form-label">Language</label>
                            <select class="form-select" id="analysisLanguage">
                                <option value="auto">Auto-detect</option>
                                <option value="en">English</option>
                                <option value="es">Spanish</option>
                                <option value="fr">French</option>
                                <option value="de">German</option>
                                <option value="zh">Chinese</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="analysisDepth" class="form-label">Analysis Depth</label>
                            <select class="form-select" id="analysisDepth">
                                <option value="basic">Basic Analysis</option>
                                <option value="standard" selected>Standard Analysis</option>
                                <option value="deep">Deep Analysis</option>
                                <option value="research">Research Grade</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <div class="form-check mt-4">
                                <input class="form-check-input" type="checkbox" id="enableOCR" checked>
                                <label class="form-check-label" for="enableOCR">
                                    Enable OCR for scanned documents
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Upload Instructions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> Upload & Analysis Instructions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h6><i class="fas fa-file-alt"></i> Supported Formats</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-file-pdf text-danger"></i> PDF documents</li>
                                <li><i class="fas fa-file-alt text-primary"></i> Text files (.txt)</li>
                                <li><i class="fas fa-file-word text-info"></i> Word documents (.docx)</li>
                                <li><i class="fas fa-file-csv text-success"></i> CSV files</li>
                                <li><i class="fas fa-file-image text-warning"></i> Images (JPG, PNG)</li>
                                <li><i class="fas fa-file-code text-secondary"></i> Markdown (.md)</li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <h6><i class="fas fa-rules"></i> Requirements</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-weight-hanging"></i> Max file size: 25MB</li>
                                <li><i class="fas fa-layer-group"></i> Max files: 50 per batch</li>
                                <li><i class="fas fa-language"></i> Multi-language support</li>
                                <li><i class="fas fa-shield-alt"></i> Secure processing</li>
                                <li><i class="fas fa-clock"></i> Processing time: 1-5 min</li>
                                <li><i class="fas fa-database"></i> Auto-save results</li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <h6><i class="fas fa-chart-bar"></i> Analysis Features</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-heart text-danger"></i> Sentiment analysis</li>
                                <li><i class="fas fa-key text-warning"></i> Keyword extraction</li>
                                <li><i class="fas fa-tags text-info"></i> Topic classification</li>
                                <li><i class="fas fa-copy text-success"></i> Similarity detection</li>
                                <li><i class="fas fa-compress text-primary"></i> Auto-summarization</li>
                                <li><i class="fas fa-network-wired text-secondary"></i> Entity recognition</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Upload Area -->
            <div class="row">
                <div class="col-md-8 mx-auto">
                    <div class="upload-area" id="uploadArea">
                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                        <h4>Drag and drop documents here</h4>
                        <p class="text-muted">or <button class="btn btn-link p-0" onclick="document.getElementById('fileInput').click()">browse files</button></p>
                        <input type="file" id="fileInput" multiple accept=".pdf,.txt,.docx,.csv,.jpg,.png,.md" style="display: none;">
                        <div class="mt-3">
                            <small class="text-muted">Supported: PDF, TXT, DOCX, CSV, JPG, PNG, MD (Max 25MB each)</small>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-outline-primary btn-sm me-2" onclick="loadSampleDocuments()">
                                <i class="fas fa-file-import"></i> Load Sample Documents
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="pasteTextForAnalysis()">
                                <i class="fas fa-paste"></i> Paste Text
                            </button>
                        </div>
                    </div>

                    <!-- File List -->
                    <div id="fileList" class="mt-4" style="display: none;">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0"><i class="fas fa-list"></i> Selected Documents (<span id="fileCount">0</span>)</h6>
                                <div>
                                    <button class="btn btn-outline-info btn-sm me-2" onclick="previewSelectedFiles()">
                                        <i class="fas fa-eye"></i> Preview
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" onclick="clearFileList()">
                                        <i class="fas fa-trash"></i> Clear All
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="fileListContent"></div>
                                <div class="mt-3">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <button class="btn btn-primary" onclick="startDocumentAnalysis()">
                                                <i class="fas fa-brain"></i> Start Analysis
                                            </button>
                                            <button class="btn btn-outline-secondary ms-2" onclick="addMoreFiles()">
                                                <i class="fas fa-plus"></i> Add More Files
                                            </button>
                                            <button class="btn btn-outline-info ms-2" onclick="batchAnalysisOptions()">
                                                <i class="fas fa-cogs"></i> Batch Options
                                            </button>
                                        </div>
                                        <div class="col-md-4 text-end">
                                            <small class="text-muted">
                                                Total size: <span id="totalFileSize">0 MB</span>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Analysis Progress -->
                    <div id="uploadProgress" class="mt-4" style="display: none;">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0"><i class="fas fa-brain fa-spin"></i> Document Analysis Progress</h6>
                                <button class="btn btn-outline-danger btn-sm" onclick="cancelAnalysis()">
                                    <i class="fas fa-stop"></i> Cancel
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="progress mb-3" style="height: 20px;">
                                    <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated"
                                         role="progressbar" style="width: 0%">
                                        <span id="progressText">0%</span>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-8">
                                        <div id="uploadStatus">
                                            <div class="analysis-step">
                                                <i class="fas fa-upload text-primary"></i> Uploading documents...
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="analysis-stats">
                                            <small class="text-muted">
                                                <div>Files processed: <span id="processedCount">0</span>/<span id="totalCount">0</span></div>
                                                <div>Estimated time: <span id="estimatedTime">--</span></div>
                                                <div>Current step: <span id="currentStep">Initializing</span></div>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Analysis Results -->
                    <div id="analysisResults" class="mt-4" style="display: none;">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0"><i class="fas fa-chart-bar"></i> Analysis Results</h6>
                                <div>
                                    <button class="btn btn-outline-secondary btn-sm me-2" onclick="exportAnalysisResults()">
                                        <i class="fas fa-download"></i> Export
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" onclick="shareAnalysisResults()">
                                        <i class="fas fa-share"></i> Share
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="analysisResultsContent">
                                    <!-- Results will be populated by JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Upload History -->
                    <div id="uploadHistory" class="mt-4" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-history"></i> Recent Uploads</h6>
                            </div>
                            <div class="card-body">
                                <div id="uploadHistoryContent">
                                    <p class="text-muted">No recent uploads</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search Section -->
        <div id="search-section" class="content-section" style="display: none;">
            <h1 class="section-title">Search & Discovery</h1>

            <!-- Search Instructions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-lightbulb"></i> Search Tips</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-search"></i> Search Examples</h6>
                            <ul class="list-unstyled">
                                <li><code>"artificial intelligence"</code> - Exact phrase</li>
                                <li><code>AI OR machine learning</code> - Either term</li>
                                <li><code>privacy AND security</code> - Both terms</li>
                                <li><code>regulation -government</code> - Exclude term</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-filter"></i> Search Scope</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-building"></i> Organization names</li>
                                <li><i class="fas fa-file-text"></i> Document content</li>
                                <li><i class="fas fa-tags"></i> Keywords and topics</li>
                                <li><i class="fas fa-chart-line"></i> Analysis results</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search Interface -->
            <div class="search-container">
                <div class="row">
                    <div class="col-md-8">
                        <div class="input-group">
                            <input type="text" id="searchInput" class="form-control form-control-lg"
                                   placeholder="Search documents, organizations, or keywords..."
                                   onkeypress="handleSearchKeyPress(event)">
                            <button class="btn btn-primary" onclick="performSearch()">
                                <i class="fas fa-search"></i> Search
                            </button>
                            <button class="btn btn-outline-secondary" onclick="clearSearch()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">
                                <span id="searchSuggestions"></span>
                            </small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-outline-secondary w-100" onclick="toggleFilters()">
                            <i class="fas fa-filter"></i> Advanced Filters
                        </button>
                    </div>
                </div>

                <!-- Advanced Filters Panel -->
                <div id="filterPanel" class="filter-panel mt-3" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-sliders-h"></i> Advanced Search Filters</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <label for="orgTypeFilter" class="form-label">Organization Type</label>
                                    <select id="orgTypeFilter" class="form-select">
                                        <option value="">All Types</option>
                                        <option value="corporate">Corporate</option>
                                        <option value="nonprofit">Nonprofit</option>
                                        <option value="academic">Academic</option>
                                        <option value="government">Government</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="sectorFilter" class="form-label">Sector</label>
                                    <select id="sectorFilter" class="form-select">
                                        <option value="">All Sectors</option>
                                        <option value="technology">Technology</option>
                                        <option value="healthcare">Healthcare</option>
                                        <option value="finance">Finance</option>
                                        <option value="education">Education</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="sentimentFilter" class="form-label">Sentiment</label>
                                    <select id="sentimentFilter" class="form-select">
                                        <option value="">All Sentiments</option>
                                        <option value="positive">Positive</option>
                                        <option value="neutral">Neutral</option>
                                        <option value="negative">Negative</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="analysisFilter" class="form-label">Analysis Category</label>
                                    <select id="analysisFilter" class="form-select">
                                        <option value="">All Categories</option>
                                        <option value="sentiment">Sentiment Analysis</option>
                                        <option value="moral">Moral Framework</option>
                                        <option value="policy">Policy Stance</option>
                                        <option value="text">Text Statistics</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-4">
                                    <label for="wordCountFilter" class="form-label">Word Count Range</label>
                                    <div class="input-group">
                                        <input type="number" id="minWordCount" class="form-control" placeholder="Min">
                                        <span class="input-group-text">-</span>
                                        <input type="number" id="maxWordCount" class="form-control" placeholder="Max">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <label for="dateFilter" class="form-label">Date Range</label>
                                    <div class="input-group">
                                        <input type="date" id="startDate" class="form-control">
                                        <span class="input-group-text">to</span>
                                        <input type="date" id="endDate" class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <label for="sortFilter" class="form-label">Sort By</label>
                                    <select id="sortFilter" class="form-select">
                                        <option value="relevance">Relevance</option>
                                        <option value="date">Date</option>
                                        <option value="organization">Organization</option>
                                        <option value="word_count">Word Count</option>
                                        <option value="sentiment">Sentiment Score</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <button class="btn btn-primary" onclick="applyFilters()">
                                        <i class="fas fa-search"></i> Apply Filters
                                    </button>
                                    <button class="btn btn-outline-secondary ms-2" onclick="resetFilters()">
                                        <i class="fas fa-undo"></i> Reset
                                    </button>
                                    <button class="btn btn-outline-info ms-2" onclick="saveSearchPreset()">
                                        <i class="fas fa-save"></i> Save Preset
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search Results -->
            <div id="searchResults" class="mt-4">
                <div class="text-center text-muted py-4">
                    <i class="fas fa-search fa-3x mb-3"></i>
                    <p>Enter a search query to find documents and organizations.</p>
                    <p><small>Use advanced filters for more precise results.</small></p>
                </div>
            </div>
        </div>

        <!-- Analysis Section -->
        <div id="analysis-section" class="content-section" style="display: none;">
            <h1 class="section-title">Analysis Results</h1>

            <!-- Analysis Controls -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-filter"></i> Analysis Filters & Controls</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="analysisOrgType" class="form-label">Organization Type</label>
                            <select class="form-select" id="analysisOrgType">
                                <option value="">All Types</option>
                                <option value="corporate">Corporate</option>
                                <option value="academic">Academic</option>
                                <option value="nonprofit">Nonprofit</option>
                                <option value="government">Government</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="analysisSector" class="form-label">Sector</label>
                            <select class="form-select" id="analysisSector">
                                <option value="">All Sectors</option>
                                <option value="technology">Technology</option>
                                <option value="education">Education</option>
                                <option value="healthcare">Healthcare</option>
                                <option value="finance">Finance</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="analysisSentiment" class="form-label">Sentiment</label>
                            <select class="form-select" id="analysisSentiment">
                                <option value="">All Sentiments</option>
                                <option value="positive">Positive</option>
                                <option value="neutral">Neutral</option>
                                <option value="negative">Negative</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="analysisLimit" class="form-label">Results Limit</label>
                            <select class="form-select" id="analysisLimit">
                                <option value="25">25 Results</option>
                                <option value="50">50 Results</option>
                                <option value="100">100 Results</option>
                                <option value="200">200 Results</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <button class="btn btn-primary" onclick="loadAnalysisResults()">
                                <i class="fas fa-search"></i> Load Analysis Results
                            </button>
                            <button class="btn btn-success ms-2" onclick="exportAnalysisResults()">
                                <i class="fas fa-download"></i> Export Results
                            </button>
                            <button class="btn btn-info ms-2" onclick="showAnalysisStatistics()">
                                <i class="fas fa-chart-bar"></i> Show Statistics
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analysis Statistics -->
            <div id="analysisStatistics" class="card mb-4" style="display: none;">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-pie"></i> Analysis Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="row" id="analysisStatsContent">
                        <!-- Statistics will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Analysis Results Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-table"></i> Analysis Results</h5>
                </div>
                <div class="card-body">
                    <div id="analysisContent">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-chart-line fa-3x mb-3"></i>
                            <p>Click "Load Analysis Results" to view detailed analysis data.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Visualizations Section -->
        <div id="visualizations-section" class="content-section" style="display: none;">
            <h1 class="section-title">Data Visualizations</h1>

            <!-- Visualization Controls -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Visualization Controls</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="vizDataSource" class="form-label">Data Source</label>
                            <select class="form-select" id="vizDataSource">
                                <option value="historical">Historical Data</option>
                                <option value="analysis">Analysis Results</option>
                                <option value="combined">Combined Data</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="vizOrgFilter" class="form-label">Organization Filter</label>
                            <select class="form-select" id="vizOrgFilter">
                                <option value="">All Organizations</option>
                                <option value="corporate">Corporate Only</option>
                                <option value="academic">Academic Only</option>
                                <option value="nonprofit">Nonprofit Only</option>
                                <option value="government">Government Only</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="vizChartType" class="form-label">Chart Type</label>
                            <select class="form-select" id="vizChartType">
                                <option value="all">Show All Charts</option>
                                <option value="sentiment">Sentiment Only</option>
                                <option value="moral">Moral Framework Only</option>
                                <option value="policy">Policy Stance Only</option>
                                <option value="distribution">Distribution Only</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-primary mt-4" onclick="refreshVisualizations()">
                                <i class="fas fa-sync"></i> Refresh Charts
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- First Row of Charts -->
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-heart"></i> Sentiment Analysis Distribution</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="sentimentChart"></canvas>
                            </div>
                            <div class="mt-2">
                                <small class="text-muted">Distribution of sentiment across all analyzed documents</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-balance-scale"></i> Moral Framework Analysis</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="moralChart"></canvas>
                            </div>
                            <div class="mt-2">
                                <small class="text-muted">Dominant moral frameworks across organizations</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Second Row of Charts -->
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-gavel"></i> Policy Stance Distribution</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="policyChart"></canvas>
                            </div>
                            <div class="mt-2">
                                <small class="text-muted">Policy preferences across different organization types</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-building"></i> Organization Type Distribution</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="orgTypeChart"></canvas>
                            </div>
                            <div class="mt-2">
                                <small class="text-muted">Distribution of organization types in the dataset</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Third Row of Charts -->
            <div class="row">
                <div class="col-md-12 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-chart-line"></i> Sentiment vs Organization Type Heatmap</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="sentimentHeatmapChart"></canvas>
                            </div>
                            <div class="mt-2">
                                <small class="text-muted">Correlation between organization types and sentiment patterns</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Historical Data Section -->
        <div id="historical-section" class="content-section" style="display: none;">
            <h1 class="section-title">Historical Data (90 FR 9088)</h1>
            
            <!-- Batch Processing Controls -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cogs"></i> Batch Document Analysis</h5>
                </div>
                <div class="card-body">
                    <div class="row align-items-end">
                        <div class="col-md-3">
                            <label for="batchSize" class="form-label">Batch Size</label>
                            <input type="number" class="form-control" id="batchSize" value="10" min="1" max="50">
                            <small class="text-muted">Documents per batch (max 50)</small>
                        </div>
                        <div class="col-md-3">
                            <label for="startIndex" class="form-label">Start Index</label>
                            <input type="number" class="form-control" id="startIndex" value="0" min="0">
                            <small class="text-muted">Starting document number</small>
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-success btn-lg" id="startBatchBtn" onclick="startBatchProcessing()">
                                <i class="fas fa-play"></i> Start Batch Analysis
                            </button>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-secondary" onclick="getBatchStatus()">
                                <i class="fas fa-info-circle"></i> Status
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Progress Display -->
            <div id="batchProgressContainer" class="card mb-4" style="display: none;">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-spinner fa-spin"></i> Processing Progress</h5>
                </div>
                <div class="card-body">
                    <!-- Progress Bar -->
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <span id="batchProgressText">0/0 documents (0%)</span>
                            <small class="text-muted">Current: <span id="currentFile">--</span></small>
                        </div>
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated bg-success" 
                                 id="batchProgressBar" role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>
                    
                    <!-- Statistics -->
                    <div id="batchStats">
                        <div class="row text-center">
                            <div class="col-3">
                                <div class="fw-bold text-success">0</div>
                                <small class="text-muted">Successful</small>
                            </div>
                            <div class="col-3">
                                <div class="fw-bold text-warning">0</div>
                                <small class="text-muted">Skipped</small>
                            </div>
                            <div class="col-3">
                                <div class="fw-bold text-danger">0</div>
                                <small class="text-muted">Failed</small>
                            </div>
                            <div class="col-3">
                                <div class="fw-bold text-info">--</div>
                                <small class="text-muted">ETA</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Continue Processing Option -->
            <div id="continueProcessing" style="display: none;"></div>

            <!-- Overall Status -->
            <div id="overallBatchStatus" class="mb-4"></div>
            
            <!-- Legacy Load Controls -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-database"></i> Quick Dataset Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary" onclick="loadHistoricalData()">
                            <i class="fas fa-download"></i> Load Sample Data
                        </button>
                        <button class="btn btn-info" onclick="loadHistoricalStatistics()">
                            <i class="fas fa-chart-bar"></i> Refresh Statistics
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Historical Data Statistics -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-line"></i> Dataset Statistics</h5>
                </div>
                <div class="card-body">
                    <span id="historicalStats" class="text-muted">No historical data loaded</span>
                </div>
            </div>

            <!-- Table Controls -->
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control" id="historicalSearchInput" placeholder="Search organizations, types, or sectors...">
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="typeFilter">
                        <option value="">All Types</option>
                        <option value="corporate">Corporate</option>
                        <option value="academic">Academic</option>
                        <option value="government">Government</option>
                        <option value="nonprofit">Nonprofit</option>
                        <option value="unknown">Unknown</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="sectorFilter">
                        <option value="">All Sectors</option>
                        <option value="technology">Technology</option>
                        <option value="healthcare">Healthcare</option>
                        <option value="finance">Finance</option>
                        <option value="education">Education</option>
                        <option value="government">Government</option>
                        <option value="other">Other</option>
                    </select>
                </div>
            </div>

            <div class="table-container">
                <table class="table table-hover mb-0" id="historicalDataTable">
                    <thead class="table-dark">
                        <tr>
                            <th style="cursor: pointer;" onclick="sortHistoricalTable('organization_name')">
                                Organization <i class="fas fa-sort"></i>
                            </th>
                            <th style="cursor: pointer;" onclick="sortHistoricalTable('organization_type')">
                                Type <i class="fas fa-sort"></i>
                            </th>
                            <th style="cursor: pointer;" onclick="sortHistoricalTable('sector')">
                                Sector <i class="fas fa-sort"></i>
                            </th>
                            <th>Document Info</th>
                            <th style="cursor: pointer;" onclick="sortHistoricalTable('submission_date')">
                                Submission <i class="fas fa-sort"></i>
                            </th>
                            <th>Analysis Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="historicalTableBody">
                        <tr>
                            <td colspan="7" class="text-center text-muted">Click "Load Historical Dataset" to view data</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="row mt-3">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <span class="text-muted me-3">Show:</span>
                        <select class="form-select form-select-sm" id="pageSize" style="width: auto;">
                            <option value="10">10</option>
                            <option value="25" selected>25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                        <span class="text-muted ms-3">entries</span>
                    </div>
                </div>
                <div class="col-md-6">
                    <nav aria-label="Historical data pagination">
                        <ul class="pagination pagination-sm justify-content-end mb-0" id="historicalPagination">
                            <!-- Pagination will be generated by JavaScript -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>

        <!-- ML Insights Section -->
        <div id="ml-insights-section" class="content-section" style="display: none;">
            <h1 class="section-title">🧠 Machine Learning Insights</h1>

            <!-- ML Controls -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cogs"></i> ML Analysis Controls</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="mlModelType" class="form-label">Model Type</label>
                            <select class="form-select" id="mlModelType">
                                <option value="sentiment">Sentiment Analysis</option>
                                <option value="topic">Topic Modeling</option>
                                <option value="clustering">Clustering Analysis</option>
                                <option value="classification">Classification</option>
                                <option value="anomaly">Anomaly Detection</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="mlDataSource" class="form-label">Data Source</label>
                            <select class="form-select" id="mlDataSource">
                                <option value="all">All Documents</option>
                                <option value="recent">Recent (30 days)</option>
                                <option value="corporate">Corporate Only</option>
                                <option value="academic">Academic Only</option>
                                <option value="government">Government Only</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="mlConfidenceThreshold" class="form-label">Confidence Threshold</label>
                            <select class="form-select" id="mlConfidenceThreshold">
                                <option value="0.5">50% (Low)</option>
                                <option value="0.7" selected>70% (Medium)</option>
                                <option value="0.8">80% (High)</option>
                                <option value="0.9">90% (Very High)</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-primary mt-4" onclick="runMLAnalysis()">
                                <i class="fas fa-play"></i> Run Analysis
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Model Performance Dashboard -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-chart-bar"></i> Model Performance</h6>
                        </div>
                        <div class="card-body">
                            <div id="modelPerformance">
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>Accuracy</span>
                                        <span class="badge bg-success">94.2%</span>
                                    </div>
                                    <div class="progress mt-1">
                                        <div class="progress-bar bg-success" style="width: 94.2%"></div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>Precision</span>
                                        <span class="badge bg-info">91.8%</span>
                                    </div>
                                    <div class="progress mt-1">
                                        <div class="progress-bar bg-info" style="width: 91.8%"></div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>Recall</span>
                                        <span class="badge bg-warning">89.5%</span>
                                    </div>
                                    <div class="progress mt-1">
                                        <div class="progress-bar bg-warning" style="width: 89.5%"></div>
                                    </div>
                                </div>
                                <div>
                                    <div class="d-flex justify-content-between">
                                        <span>F1-Score</span>
                                        <span class="badge bg-primary">90.6%</span>
                                    </div>
                                    <div class="progress mt-1">
                                        <div class="progress-bar bg-primary" style="width: 90.6%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-lightbulb"></i> AI-Powered Insights</h6>
                        </div>
                        <div class="card-body">
                            <div id="mlInsights">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="bg-success rounded-circle p-2 me-3">
                                        <i class="fas fa-arrow-up text-white"></i>
                                    </div>
                                    <div>
                                        <strong>Emerging Trend</strong><br>
                                        <small class="text-muted">Self-regulation preference increased 23% in tech sector</small>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center mb-3">
                                    <div class="bg-warning rounded-circle p-2 me-3">
                                        <i class="fas fa-exclamation-triangle text-white"></i>
                                    </div>
                                    <div>
                                        <strong>Anomaly Detected</strong><br>
                                        <small class="text-muted">Unusual negative sentiment spike in finance sector</small>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center">
                                    <div class="bg-info rounded-circle p-2 me-3">
                                        <i class="fas fa-users text-white"></i>
                                    </div>
                                    <div>
                                        <strong>Cluster Analysis</strong><br>
                                        <small class="text-muted">Identified 3 distinct policy stance groups</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-chart-area"></i> Feature Importance</h6>
                        </div>
                        <div class="card-body">
                            <div id="featureImportance">
                                <div class="mb-2">
                                    <div class="d-flex justify-content-between">
                                        <small>Sentiment Score</small>
                                        <small>0.34</small>
                                    </div>
                                    <div class="progress" style="height: 8px;">
                                        <div class="progress-bar bg-primary" style="width: 34%"></div>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <div class="d-flex justify-content-between">
                                        <small>Word Count</small>
                                        <small>0.28</small>
                                    </div>
                                    <div class="progress" style="height: 8px;">
                                        <div class="progress-bar bg-success" style="width: 28%"></div>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <div class="d-flex justify-content-between">
                                        <small>Organization Type</small>
                                        <small>0.22</small>
                                    </div>
                                    <div class="progress" style="height: 8px;">
                                        <div class="progress-bar bg-info" style="width: 22%"></div>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <div class="d-flex justify-content-between">
                                        <small>Sector</small>
                                        <small>0.16</small>
                                    </div>
                                    <div class="progress" style="height: 8px;">
                                        <div class="progress-bar bg-warning" style="width: 16%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Advanced ML Visualizations -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-chart-line"></i> Sentiment Evolution</h6>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="sentimentEvolutionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-project-diagram"></i> Clustering Visualization</h6>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="clusteringChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Topic Modeling & Classification -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-sitemap"></i> Topic Modeling Results</h6>
                        </div>
                        <div class="card-body">
                            <div id="topicVisualization">
                                <div class="row">
                                    <div class="col-md-3 text-center">
                                        <div class="topic-bubble" style="background: #FF6B6B; padding: 20px; border-radius: 50%; margin: 10px; cursor: pointer;" onclick="showTopicDetails('ai_safety')">
                                            <strong>AI Safety</strong><br>
                                            <small>32% coverage</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3 text-center">
                                        <div class="topic-bubble" style="background: #4ECDC4; padding: 20px; border-radius: 50%; margin: 10px; cursor: pointer;" onclick="showTopicDetails('regulation')">
                                            <strong>Regulation</strong><br>
                                            <small>28% coverage</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3 text-center">
                                        <div class="topic-bubble" style="background: #45B7D1; padding: 20px; border-radius: 50%; margin: 10px; cursor: pointer;" onclick="showTopicDetails('innovation')">
                                            <strong>Innovation</strong><br>
                                            <small>24% coverage</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3 text-center">
                                        <div class="topic-bubble" style="background: #F7DC6F; padding: 20px; border-radius: 50%; margin: 10px; cursor: pointer;" onclick="showTopicDetails('ethics')">
                                            <strong>Ethics</strong><br>
                                            <small>16% coverage</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-tags"></i> Classification Results</h6>
                        </div>
                        <div class="card-body">
                            <div id="classificationResults">
                                <div class="mb-3">
                                    <h6>Policy Stance Distribution</h6>
                                    <div class="mb-2">
                                        <div class="d-flex justify-content-between">
                                            <span>Self-Regulation</span>
                                            <span>42%</span>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar bg-primary" style="width: 42%"></div>
                                        </div>
                                    </div>
                                    <div class="mb-2">
                                        <div class="d-flex justify-content-between">
                                            <span>Co-Regulation</span>
                                            <span>31%</span>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar bg-success" style="width: 31%"></div>
                                        </div>
                                    </div>
                                    <div class="mb-2">
                                        <div class="d-flex justify-content-between">
                                            <span>Government Oversight</span>
                                            <span>27%</span>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar bg-warning" style="width: 27%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Anomaly Detection -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Anomaly Detection Results</h6>
                        </div>
                        <div class="card-body">
                            <div id="anomalyResults">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Organization</th>
                                                <th>Anomaly Type</th>
                                                <th>Severity</th>
                                                <th>Confidence</th>
                                                <th>Description</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="anomalyTableBody">
                                            <!-- Anomaly results will be populated here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Predictive Analytics Section -->
        <div id="predictive-section" class="content-section" style="display: none;">
            <h1 class="section-title">🔮 Predictive Analytics</h1>

            <!-- Prediction Controls -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cogs"></i> Prediction Controls</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="predictionType" class="form-label">Prediction Type</label>
                            <select class="form-select" id="predictionType">
                                <option value="sentiment">Sentiment Trends</option>
                                <option value="policy">Policy Stance Evolution</option>
                                <option value="influence">Influence Patterns</option>
                                <option value="topic">Topic Emergence</option>
                                <option value="anomaly">Anomaly Forecasting</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="predictionHorizon" class="form-label">Time Horizon</label>
                            <select class="form-select" id="predictionHorizon">
                                <option value="1">1 Month</option>
                                <option value="3" selected>3 Months</option>
                                <option value="6">6 Months</option>
                                <option value="12">1 Year</option>
                                <option value="24">2 Years</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="predictionScope" class="form-label">Scope</label>
                            <select class="form-select" id="predictionScope">
                                <option value="all">All Organizations</option>
                                <option value="corporate">Corporate Only</option>
                                <option value="academic">Academic Only</option>
                                <option value="government">Government Only</option>
                                <option value="sector">By Sector</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-primary mt-4" onclick="runPredictiveAnalysis()">
                                <i class="fas fa-crystal-ball"></i> Generate Predictions
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Prediction Results Dashboard -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-chart-line"></i> Trend Predictions</h6>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="predictionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-percentage"></i> Prediction Confidence</h6>
                        </div>
                        <div class="card-body">
                            <div id="predictionConfidence">
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>Short-term (1M)</span>
                                        <span class="badge bg-success">94%</span>
                                    </div>
                                    <div class="progress mt-1">
                                        <div class="progress-bar bg-success" style="width: 94%"></div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>Medium-term (3M)</span>
                                        <span class="badge bg-info">87%</span>
                                    </div>
                                    <div class="progress mt-1">
                                        <div class="progress-bar bg-info" style="width: 87%"></div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>Long-term (1Y)</span>
                                        <span class="badge bg-warning">76%</span>
                                    </div>
                                    <div class="progress mt-1">
                                        <div class="progress-bar bg-warning" style="width: 76%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Time Series Analysis -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-wave-square"></i> Time Series Decomposition</h6>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="timeSeriesChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-chart-area"></i> Forecast Intervals</h6>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="forecastChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Scenario Planning -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-chess"></i> Scenario Planning & What-If Analysis</h6>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <h6>Scenario Parameters</h6>
                            <div class="row">
                                <div class="col-md-3">
                                    <label for="scenarioRegulation" class="form-label">Regulation Level</label>
                                    <input type="range" class="form-range" id="scenarioRegulation" min="0" max="100" value="50" onchange="updateScenario()">
                                    <small class="text-muted">Current: <span id="regulationValue">50</span>%</small>
                                </div>
                                <div class="col-md-3">
                                    <label for="scenarioInnovation" class="form-label">Innovation Rate</label>
                                    <input type="range" class="form-range" id="scenarioInnovation" min="0" max="100" value="70" onchange="updateScenario()">
                                    <small class="text-muted">Current: <span id="innovationValue">70</span>%</small>
                                </div>
                                <div class="col-md-3">
                                    <label for="scenarioPublicSentiment" class="form-label">Public Sentiment</label>
                                    <input type="range" class="form-range" id="scenarioPublicSentiment" min="0" max="100" value="60" onchange="updateScenario()">
                                    <small class="text-muted">Current: <span id="sentimentValue">60</span>%</small>
                                </div>
                                <div class="col-md-3">
                                    <label for="scenarioEconomicImpact" class="form-label">Economic Impact</label>
                                    <input type="range" class="form-range" id="scenarioEconomicImpact" min="0" max="100" value="40" onchange="updateScenario()">
                                    <small class="text-muted">Current: <span id="economicValue">40</span>%</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row" id="scenarioResults">
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="text-success">Optimistic Scenario</h6>
                                    <p>Collaborative regulation increases by 40%</p>
                                    <small class="text-muted">Probability: <span id="optimisticProb">35%</span></small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="text-primary">Most Likely</h6>
                                    <p>Gradual shift toward hybrid approaches</p>
                                    <small class="text-muted">Probability: <span id="likelyProb">45%</span></small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="text-warning">Conservative</h6>
                                    <p>Status quo maintains dominance</p>
                                    <small class="text-muted">Probability: <span id="conservativeProb">20%</span></small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Prediction Model Performance -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-chart-bar"></i> Model Performance Metrics</h6>
                        </div>
                        <div class="card-body">
                            <div id="modelMetrics">
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>Mean Absolute Error</span>
                                        <span class="badge bg-success">0.087</span>
                                    </div>
                                    <div class="progress mt-1">
                                        <div class="progress-bar bg-success" style="width: 91.3%"></div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>R² Score</span>
                                        <span class="badge bg-info">0.923</span>
                                    </div>
                                    <div class="progress mt-1">
                                        <div class="progress-bar bg-info" style="width: 92.3%"></div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>MAPE</span>
                                        <span class="badge bg-warning">5.4%</span>
                                    </div>
                                    <div class="progress mt-1">
                                        <div class="progress-bar bg-warning" style="width: 94.6%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Risk Assessment</h6>
                        </div>
                        <div class="card-body">
                            <div id="riskAssessment">
                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-exclamation-triangle"></i> High Risk Factors</h6>
                                    <ul class="mb-0">
                                        <li>Regulatory uncertainty in EU markets</li>
                                        <li>Rapid technological advancement pace</li>
                                        <li>Public opinion volatility</li>
                                    </ul>
                                </div>
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle"></i> Opportunities</h6>
                                    <ul class="mb-0">
                                        <li>Increased industry collaboration</li>
                                        <li>Standardization initiatives</li>
                                        <li>Cross-sector partnerships</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Prediction History -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-history"></i> Prediction History & Accuracy</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Prediction Type</th>
                                    <th>Horizon</th>
                                    <th>Predicted Value</th>
                                    <th>Actual Value</th>
                                    <th>Accuracy</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody id="predictionHistoryTable">
                                <!-- Prediction history will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Network Analysis Section -->
        <div id="network-section" class="content-section" style="display: none;">
            <h1 class="section-title">🕸️ Network Analysis</h1>

            <!-- Network Controls -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cogs"></i> Network Analysis Controls</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="networkType" class="form-label">Network Type</label>
                            <select class="form-select" id="networkType">
                                <option value="policy">Policy Similarity</option>
                                <option value="collaboration">Collaboration Network</option>
                                <option value="influence">Influence Network</option>
                                <option value="citation">Citation Network</option>
                                <option value="topic">Topic Co-occurrence</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="networkLayout" class="form-label">Layout Algorithm</label>
                            <select class="form-select" id="networkLayout">
                                <option value="force">Force-Directed</option>
                                <option value="circular">Circular</option>
                                <option value="hierarchical">Hierarchical</option>
                                <option value="grid">Grid</option>
                                <option value="community">Community-Based</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="networkFilter" class="form-label">Organization Filter</label>
                            <select class="form-select" id="networkFilter">
                                <option value="all">All Organizations</option>
                                <option value="corporate">Corporate Only</option>
                                <option value="academic">Academic Only</option>
                                <option value="government">Government Only</option>
                                <option value="nonprofit">Nonprofit Only</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-primary mt-4" onclick="generateNetworkAnalysis()">
                                <i class="fas fa-project-diagram"></i> Generate Network
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Network Visualization -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0"><i class="fas fa-share-alt"></i> Interactive Network Graph</h6>
                            <div>
                                <button class="btn btn-outline-secondary btn-sm" onclick="resetNetworkView()">
                                    <i class="fas fa-undo"></i> Reset View
                                </button>
                                <button class="btn btn-outline-primary btn-sm" onclick="exportNetwork()">
                                    <i class="fas fa-download"></i> Export
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="networkVisualization" style="height: 500px; border: 1px solid #ddd; border-radius: 8px; background: #f8f9fa; position: relative;">
                                <div class="text-center" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                                    <i class="fas fa-project-diagram fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">Interactive network visualization will appear here</p>
                                    <small>Click "Generate Network" to create visualization</small>
                                </div>
                                <canvas id="networkCanvas" style="width: 100%; height: 100%; display: none;"></canvas>
                            </div>
                            <div class="mt-3">
                                <div class="row">
                                    <div class="col-md-6">
                                        <small class="text-muted">
                                            <i class="fas fa-mouse"></i> Click and drag to pan • Scroll to zoom
                                        </small>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <small class="text-muted">
                                            Nodes: <span id="nodeCount">0</span> • Edges: <span id="edgeCount">0</span>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-crown"></i> Network Metrics</h6>
                        </div>
                        <div class="card-body">
                            <div id="networkMetrics">
                                <div class="mb-3">
                                    <h6>Global Metrics</h6>
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>Density</span>
                                        <span class="badge bg-info" id="networkDensity">0.23</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>Clustering Coefficient</span>
                                        <span class="badge bg-success" id="clusteringCoeff">0.67</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>Average Path Length</span>
                                        <span class="badge bg-warning" id="avgPathLength">3.2</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>Modularity</span>
                                        <span class="badge bg-primary" id="modularity">0.45</span>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <h6>Most Influential</h6>
                                    <div id="influentialNodes">
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>Google LLC</span>
                                            <span class="badge bg-primary">9.2</span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>Microsoft</span>
                                            <span class="badge bg-primary">8.7</span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>OpenAI</span>
                                            <span class="badge bg-primary">8.1</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <h6>Bridge Organizations</h6>
                                    <div id="bridgeNodes">
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>MIT</span>
                                            <span class="badge bg-success">High</span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>Stanford HAI</span>
                                            <span class="badge bg-success">High</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Community Detection -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-users"></i> Community Detection</h6>
                        </div>
                        <div class="card-body">
                            <div id="communityAnalysis">
                                <div class="mb-3">
                                    <h6>Detected Communities</h6>
                                    <div class="community-item mb-2">
                                        <div class="d-flex align-items-center">
                                            <div class="community-color" style="width: 20px; height: 20px; background: #FF6B6B; border-radius: 50%; margin-right: 10px;"></div>
                                            <div>
                                                <strong>Tech Giants</strong> (8 organizations)<br>
                                                <small class="text-muted">Google, Microsoft, Apple, Meta, Amazon</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="community-item mb-2">
                                        <div class="d-flex align-items-center">
                                            <div class="community-color" style="width: 20px; height: 20px; background: #4ECDC4; border-radius: 50%; margin-right: 10px;"></div>
                                            <div>
                                                <strong>Academic Institutions</strong> (12 organizations)<br>
                                                <small class="text-muted">MIT, Stanford, CMU, Berkeley, Oxford</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="community-item mb-2">
                                        <div class="d-flex align-items-center">
                                            <div class="community-color" style="width: 20px; height: 20px; background: #45B7D1; border-radius: 50%; margin-right: 10px;"></div>
                                            <div>
                                                <strong>Policy Organizations</strong> (6 organizations)<br>
                                                <small class="text-muted">EU Commission, FTC, NIST, IEEE</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="community-item mb-2">
                                        <div class="d-flex align-items-center">
                                            <div class="community-color" style="width: 20px; height: 20px; background: #F7DC6F; border-radius: 50%; margin-right: 10px;"></div>
                                            <div>
                                                <strong>AI Safety Groups</strong> (5 organizations)<br>
                                                <small class="text-muted">OpenAI, Anthropic, DeepMind, MIRI</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-chart-pie"></i> Community Statistics</h6>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="communityChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Centrality Analysis -->
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-chart-bar"></i> Centrality Analysis</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <h6>Degree Centrality</h6>
                                    <div id="degreeCentrality">
                                        <div class="d-flex justify-content-between mb-1">
                                            <small>Google LLC</small>
                                            <small>0.89</small>
                                        </div>
                                        <div class="progress mb-2" style="height: 8px;">
                                            <div class="progress-bar bg-primary" style="width: 89%"></div>
                                        </div>
                                        <div class="d-flex justify-content-between mb-1">
                                            <small>Microsoft</small>
                                            <small>0.82</small>
                                        </div>
                                        <div class="progress mb-2" style="height: 8px;">
                                            <div class="progress-bar bg-primary" style="width: 82%"></div>
                                        </div>
                                        <div class="d-flex justify-content-between mb-1">
                                            <small>MIT</small>
                                            <small>0.76</small>
                                        </div>
                                        <div class="progress mb-2" style="height: 8px;">
                                            <div class="progress-bar bg-primary" style="width: 76%"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <h6>Betweenness Centrality</h6>
                                    <div id="betweennessCentrality">
                                        <div class="d-flex justify-content-between mb-1">
                                            <small>Stanford HAI</small>
                                            <small>0.67</small>
                                        </div>
                                        <div class="progress mb-2" style="height: 8px;">
                                            <div class="progress-bar bg-success" style="width: 67%"></div>
                                        </div>
                                        <div class="d-flex justify-content-between mb-1">
                                            <small>MIT</small>
                                            <small>0.61</small>
                                        </div>
                                        <div class="progress mb-2" style="height: 8px;">
                                            <div class="progress-bar bg-success" style="width: 61%"></div>
                                        </div>
                                        <div class="d-flex justify-content-between mb-1">
                                            <small>OpenAI</small>
                                            <small>0.54</small>
                                        </div>
                                        <div class="progress mb-2" style="height: 8px;">
                                            <div class="progress-bar bg-success" style="width: 54%"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <h6>Closeness Centrality</h6>
                                    <div id="closenessCentrality">
                                        <div class="d-flex justify-content-between mb-1">
                                            <small>Google LLC</small>
                                            <small>0.78</small>
                                        </div>
                                        <div class="progress mb-2" style="height: 8px;">
                                            <div class="progress-bar bg-info" style="width: 78%"></div>
                                        </div>
                                        <div class="d-flex justify-content-between mb-1">
                                            <small>Microsoft</small>
                                            <small>0.72</small>
                                        </div>
                                        <div class="progress mb-2" style="height: 8px;">
                                            <div class="progress-bar bg-info" style="width: 72%"></div>
                                        </div>
                                        <div class="d-flex justify-content-between mb-1">
                                            <small>OpenAI</small>
                                            <small>0.69</small>
                                        </div>
                                        <div class="progress mb-2" style="height: 8px;">
                                            <div class="progress-bar bg-info" style="width: 69%"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <h6>Eigenvector Centrality</h6>
                                    <div id="eigenvectorCentrality">
                                        <div class="d-flex justify-content-between mb-1">
                                            <small>Google LLC</small>
                                            <small>0.91</small>
                                        </div>
                                        <div class="progress mb-2" style="height: 8px;">
                                            <div class="progress-bar bg-warning" style="width: 91%"></div>
                                        </div>
                                        <div class="d-flex justify-content-between mb-1">
                                            <small>Microsoft</small>
                                            <small>0.85</small>
                                        </div>
                                        <div class="progress mb-2" style="height: 8px;">
                                            <div class="progress-bar bg-warning" style="width: 85%"></div>
                                        </div>
                                        <div class="d-flex justify-content-between mb-1">
                                            <small>Apple</small>
                                            <small>0.79</small>
                                        </div>
                                        <div class="progress mb-2" style="height: 8px;">
                                            <div class="progress-bar bg-warning" style="width: 79%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Information Flow Analysis -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-route"></i> Information Flow Paths</h6>
                        </div>
                        <div class="card-body">
                            <div id="informationFlow">
                                <div class="mb-3">
                                    <h6>Key Information Pathways</h6>
                                    <div class="pathway-item mb-2">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-arrow-right text-primary me-2"></i>
                                            <span><strong>Google</strong> → <strong>Stanford</strong> → <strong>Policy Makers</strong></span>
                                        </div>
                                        <small class="text-muted ms-3">Research influence pathway (strength: 0.87)</small>
                                    </div>
                                    <div class="pathway-item mb-2">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-arrow-right text-success me-2"></i>
                                            <span><strong>MIT</strong> → <strong>Industry</strong> → <strong>Standards</strong></span>
                                        </div>
                                        <small class="text-muted ms-3">Technology transfer pathway (strength: 0.73)</small>
                                    </div>
                                    <div class="pathway-item mb-2">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-arrow-right text-warning me-2"></i>
                                            <span><strong>OpenAI</strong> → <strong>Safety Groups</strong> → <strong>Regulators</strong></span>
                                        </div>
                                        <small class="text-muted ms-3">Safety advocacy pathway (strength: 0.69)</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-chart-line"></i> Network Evolution</h6>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="networkEvolutionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sentiment Lab Section -->
        <div id="sentiment-lab-section" class="content-section" style="display: none;">
            <h1 class="section-title">🧪 Sentiment Lab</h1>

            <!-- Sentiment Analysis Controls -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cogs"></i> Sentiment Analysis Controls</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="sentimentAnalysisType" class="form-label">Analysis Type</label>
                            <select class="form-select" id="sentimentAnalysisType">
                                <option value="basic">Basic Sentiment (3-class)</option>
                                <option value="emotion">Emotion Analysis (8-class)</option>
                                <option value="intensity">Sentiment Intensity</option>
                                <option value="aspect">Aspect-based Sentiment</option>
                                <option value="temporal">Temporal Sentiment</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="sentimentTimeRange" class="form-label">Time Period</label>
                            <select class="form-select" id="sentimentTimeRange">
                                <option value="7d">Last 7 days</option>
                                <option value="30d">Last 30 days</option>
                                <option value="90d" selected>Last 90 days</option>
                                <option value="1y">Last year</option>
                                <option value="all">All time</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="sentimentModel" class="form-label">Sentiment Model</label>
                            <select class="form-select" id="sentimentModel">
                                <option value="bert">BERT-based</option>
                                <option value="roberta">RoBERTa</option>
                                <option value="vader">VADER</option>
                                <option value="textblob">TextBlob</option>
                                <option value="ensemble">Ensemble Model</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-primary mt-4" onclick="runSentimentAnalysis()">
                                <i class="fas fa-play"></i> Run Analysis
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Sentiment Dashboard -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0"><i class="fas fa-chart-area"></i> Sentiment Time Series</h6>
                            <div>
                                <button class="btn btn-outline-secondary btn-sm" onclick="exportSentimentData()">
                                    <i class="fas fa-download"></i> Export
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="sentimentTimeSeriesChart" style="height: 400px;"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-tachometer-alt"></i> Sentiment Metrics</h6>
                        </div>
                        <div class="card-body">
                            <div id="sentimentMetrics">
                                <div class="mb-3">
                                    <h6>Overall Sentiment</h6>
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>Positive</span>
                                        <span class="badge bg-success" id="positivePercent">58.3%</span>
                                    </div>
                                    <div class="progress mb-2" style="height: 8px;">
                                        <div class="progress-bar bg-success" id="positiveBar" style="width: 58.3%"></div>
                                    </div>
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>Neutral</span>
                                        <span class="badge bg-secondary" id="neutralPercent">31.2%</span>
                                    </div>
                                    <div class="progress mb-2" style="height: 8px;">
                                        <div class="progress-bar bg-secondary" id="neutralBar" style="width: 31.2%"></div>
                                    </div>
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>Negative</span>
                                        <span class="badge bg-danger" id="negativePercent">10.5%</span>
                                    </div>
                                    <div class="progress mb-2" style="height: 8px;">
                                        <div class="progress-bar bg-danger" id="negativeBar" style="width: 10.5%"></div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <h6>Sentiment Intensity</h6>
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>Average Score</span>
                                        <span class="badge bg-info" id="avgSentimentScore">0.67</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>Volatility</span>
                                        <span class="badge bg-warning" id="sentimentVolatility">0.23</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>Trend</span>
                                        <span class="badge bg-success" id="sentimentTrend">↗ Improving</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Emotion Analysis -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-heart"></i> Emotion Distribution</h6>
                        </div>
                        <div class="card-body">
                            <div id="emotionDistribution">
                                <div class="row text-center mb-3">
                                    <div class="col-3">
                                        <div class="emotion-indicator bg-success text-white p-2 rounded mb-2">
                                            <i class="fas fa-smile"></i>
                                            <div><strong>Joy</strong></div>
                                            <small id="joyPercent">42%</small>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="emotion-indicator bg-info text-white p-2 rounded mb-2">
                                            <i class="fas fa-handshake"></i>
                                            <div><strong>Trust</strong></div>
                                            <small id="trustPercent">31%</small>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="emotion-indicator bg-primary text-white p-2 rounded mb-2">
                                            <i class="fas fa-surprise"></i>
                                            <div><strong>Surprise</strong></div>
                                            <small id="surprisePercent">12%</small>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="emotion-indicator bg-secondary text-white p-2 rounded mb-2">
                                            <i class="fas fa-meh"></i>
                                            <div><strong>Anticipation</strong></div>
                                            <small id="anticipationPercent">8%</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="row text-center">
                                    <div class="col-3">
                                        <div class="emotion-indicator bg-warning text-white p-2 rounded">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            <div><strong>Fear</strong></div>
                                            <small id="fearPercent">18%</small>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="emotion-indicator bg-danger text-white p-2 rounded">
                                            <i class="fas fa-angry"></i>
                                            <div><strong>Anger</strong></div>
                                            <small id="angerPercent">9%</small>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="emotion-indicator bg-dark text-white p-2 rounded">
                                            <i class="fas fa-frown"></i>
                                            <div><strong>Sadness</strong></div>
                                            <small id="sadnessPercent">6%</small>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="emotion-indicator bg-purple text-white p-2 rounded" style="background-color: #6f42c1 !important;">
                                            <i class="fas fa-thumbs-down"></i>
                                            <div><strong>Disgust</strong></div>
                                            <small id="disgustPercent">4%</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-chart-pie"></i> Emotion Radar Chart</h6>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="emotionRadarChart" style="height: 300px;"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sentiment Heatmap and Word Cloud -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-th"></i> Organization Sentiment Heatmap</h6>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="sentimentHeatmap" style="height: 300px;"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-cloud"></i> Sentiment Word Cloud</h6>
                        </div>
                        <div class="card-body">
                            <div id="sentimentWordCloud" style="height: 300px; background: #f8f9fa; border: 1px solid #ddd; border-radius: 8px; position: relative;">
                                <div class="text-center" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                                    <i class="fas fa-cloud fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">Sentiment word cloud will appear here</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Policy Simulator Section -->
        <div id="policy-simulator-section" class="content-section" style="display: none;">
            <h1 class="section-title">⚙️ Policy Simulator</h1>

            <!-- Simulation Configuration -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cogs"></i> Simulation Configuration</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="simulationType" class="form-label">Simulation Type</label>
                            <select class="form-select" id="simulationType">
                                <option value="regulatory">Regulatory Change</option>
                                <option value="compliance">Compliance Requirements</option>
                                <option value="innovation">Innovation Policy</option>
                                <option value="market">Market Intervention</option>
                                <option value="international">International Cooperation</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="policyIntensity" class="form-label">Policy Intensity</label>
                            <select class="form-select" id="policyIntensity">
                                <option value="minimal">Minimal Change (+5%)</option>
                                <option value="moderate">Moderate Change (+15%)</option>
                                <option value="significant">Significant Change (+30%)</option>
                                <option value="major">Major Overhaul (+50%)</option>
                                <option value="revolutionary">Revolutionary (+100%)</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="simulationTimeframe" class="form-label">Timeframe</label>
                            <select class="form-select" id="simulationTimeframe">
                                <option value="6m">6 Months</option>
                                <option value="1y" selected>1 Year</option>
                                <option value="2y">2 Years</option>
                                <option value="5y">5 Years</option>
                                <option value="10y">10 Years</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-primary mt-4" onclick="runPolicySimulation()">
                                <i class="fas fa-rocket"></i> Run Simulation
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Affected Sectors and Stakeholders -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-industry"></i> Affected Sectors</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="techSector" checked>
                                        <label class="form-check-label" for="techSector">
                                            <i class="fas fa-microchip me-2"></i>Technology
                                        </label>
                                    </div>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="financeSector">
                                        <label class="form-check-label" for="financeSector">
                                            <i class="fas fa-dollar-sign me-2"></i>Finance
                                        </label>
                                    </div>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="healthSector">
                                        <label class="form-check-label" for="healthSector">
                                            <i class="fas fa-heartbeat me-2"></i>Healthcare
                                        </label>
                                    </div>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="educationSector">
                                        <label class="form-check-label" for="educationSector">
                                            <i class="fas fa-graduation-cap me-2"></i>Education
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="transportSector">
                                        <label class="form-check-label" for="transportSector">
                                            <i class="fas fa-car me-2"></i>Transportation
                                        </label>
                                    </div>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="energySector">
                                        <label class="form-check-label" for="energySector">
                                            <i class="fas fa-bolt me-2"></i>Energy
                                        </label>
                                    </div>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="manufacturingSector">
                                        <label class="form-check-label" for="manufacturingSector">
                                            <i class="fas fa-industry me-2"></i>Manufacturing
                                        </label>
                                    </div>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="retailSector">
                                        <label class="form-check-label" for="retailSector">
                                            <i class="fas fa-shopping-cart me-2"></i>Retail
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-users"></i> Key Stakeholders</h6>
                        </div>
                        <div class="card-body">
                            <div id="stakeholderImpact">
                                <div class="stakeholder-item mb-2">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-building text-primary me-2"></i>Large Corporations</span>
                                        <span class="badge bg-warning" id="corporateImpact">Medium Impact</span>
                                    </div>
                                </div>
                                <div class="stakeholder-item mb-2">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-rocket text-success me-2"></i>Startups & SMEs</span>
                                        <span class="badge bg-danger" id="startupImpact">High Impact</span>
                                    </div>
                                </div>
                                <div class="stakeholder-item mb-2">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-university text-info me-2"></i>Academic Institutions</span>
                                        <span class="badge bg-success" id="academicImpact">Low Impact</span>
                                    </div>
                                </div>
                                <div class="stakeholder-item mb-2">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-landmark text-warning me-2"></i>Government Agencies</span>
                                        <span class="badge bg-info" id="governmentImpact">Positive Impact</span>
                                    </div>
                                </div>
                                <div class="stakeholder-item mb-2">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-user-friends text-secondary me-2"></i>General Public</span>
                                        <span class="badge bg-primary" id="publicImpact">Mixed Impact</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Simulation Results Dashboard -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0"><i class="fas fa-chart-area"></i> Policy Impact Projection</h6>
                            <div>
                                <button class="btn btn-outline-secondary btn-sm" onclick="exportSimulationResults()">
                                    <i class="fas fa-download"></i> Export
                                </button>
                                <button class="btn btn-outline-primary btn-sm" onclick="resetSimulation()">
                                    <i class="fas fa-undo"></i> Reset
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="simulationResults">
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-cogs fa-3x mb-3"></i>
                                    <p>Configure simulation parameters and click "Run Simulation" to see projected outcomes</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-tachometer-alt"></i> Simulation Metrics</h6>
                        </div>
                        <div class="card-body">
                            <div id="simulationMetrics">
                                <div class="metric-item mb-3">
                                    <h6>Overall Impact Score</h6>
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>Economic Impact</span>
                                        <span class="badge bg-success" id="economicImpactScore">+12.5%</span>
                                    </div>
                                    <div class="progress mb-2" style="height: 8px;">
                                        <div class="progress-bar bg-success" id="economicImpactBar" style="width: 62.5%"></div>
                                    </div>
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>Innovation Index</span>
                                        <span class="badge bg-info" id="innovationScore">+8.3%</span>
                                    </div>
                                    <div class="progress mb-2" style="height: 8px;">
                                        <div class="progress-bar bg-info" id="innovationBar" style="width: 54.15%"></div>
                                    </div>
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>Compliance Cost</span>
                                        <span class="badge bg-warning" id="complianceScore">+15.7%</span>
                                    </div>
                                    <div class="progress mb-2" style="height: 8px;">
                                        <div class="progress-bar bg-warning" id="complianceBar" style="width: 65.7%"></div>
                                    </div>
                                </div>

                                <div class="metric-item mb-3">
                                    <h6>Risk Assessment</h6>
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>Implementation Risk</span>
                                        <span class="badge bg-danger" id="implementationRisk">Medium</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>Market Disruption</span>
                                        <span class="badge bg-warning" id="marketDisruption">Low</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>Public Acceptance</span>
                                        <span class="badge bg-success" id="publicAcceptance">High</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Real-time Monitor Section -->
        <div id="realtime-section" class="content-section" style="display: none;">
            <h1 class="section-title">📡 Real-time Monitor</h1>

            <!-- Monitor Controls -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cogs"></i> Monitor Configuration</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="monitorType" class="form-label">Monitor Type</label>
                            <select class="form-select" id="monitorType">
                                <option value="all">All Activities</option>
                                <option value="documents">Document Changes</option>
                                <option value="sentiment">Sentiment Shifts</option>
                                <option value="policy">Policy Updates</option>
                                <option value="anomalies">Anomaly Detection</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="alertThreshold" class="form-label">Alert Threshold</label>
                            <select class="form-select" id="alertThreshold">
                                <option value="low">Low Sensitivity</option>
                                <option value="medium" selected>Medium Sensitivity</option>
                                <option value="high">High Sensitivity</option>
                                <option value="critical">Critical Only</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="refreshRate" class="form-label">Refresh Rate</label>
                            <select class="form-select" id="refreshRate">
                                <option value="1">1 second</option>
                                <option value="5" selected>5 seconds</option>
                                <option value="10">10 seconds</option>
                                <option value="30">30 seconds</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <div class="mt-4">
                                <button class="btn btn-success me-2" onclick="startRealTimeMonitoring()" id="startMonitorBtn">
                                    <i class="fas fa-play"></i> Start
                                </button>
                                <button class="btn btn-danger" onclick="stopRealTimeMonitoring()" id="stopMonitorBtn" disabled>
                                    <i class="fas fa-stop"></i> Stop
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Real-time Metrics Dashboard -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card metric-card">
                        <div class="card-body text-center">
                            <i class="fas fa-eye fa-2x mb-2 text-primary"></i>
                            <h3 id="activeMonitors">12</h3>
                            <p class="mb-0">Active Monitors</p>
                            <div class="metric-trend">
                                <small class="text-success"><i class="fas fa-arrow-up"></i> +2 today</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card metric-card secondary">
                        <div class="card-body text-center">
                            <i class="fas fa-bell fa-2x mb-2 text-warning"></i>
                            <h3 id="alertsToday">3</h3>
                            <p class="mb-0">Alerts Today</p>
                            <div class="metric-trend">
                                <small class="text-danger"><i class="fas fa-arrow-up"></i> +1 last hour</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card metric-card accent">
                        <div class="card-body text-center">
                            <i class="fas fa-stream fa-2x mb-2 text-info"></i>
                            <h3 id="dataStreams">8</h3>
                            <p class="mb-0">Data Streams</p>
                            <div class="metric-trend">
                                <small class="text-success"><i class="fas fa-circle"></i> All active</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card metric-card danger">
                        <div class="card-body text-center">
                            <i class="fas fa-heartbeat fa-2x mb-2 text-success"></i>
                            <h3 id="systemHealth">98%</h3>
                            <p class="mb-0">System Health</p>
                            <div class="metric-trend">
                                <small class="text-success"><i class="fas fa-check"></i> Optimal</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Real-time Charts -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0"><i class="fas fa-chart-line"></i> Real-time Activity Trends</h6>
                            <div>
                                <span class="badge bg-success" id="monitoringStatus">Monitoring Active</span>
                                <button class="btn btn-outline-secondary btn-sm ms-2" onclick="exportRealtimeData()">
                                    <i class="fas fa-download"></i> Export
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="realtimeActivityChart" style="height: 300px;"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-tachometer-alt"></i> Performance Metrics</h6>
                        </div>
                        <div class="card-body">
                            <div id="performanceMetrics">
                                <div class="metric-item mb-3">
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>Processing Speed</span>
                                        <span class="badge bg-success" id="processingSpeed">1.2ms</span>
                                    </div>
                                    <div class="progress" style="height: 6px;">
                                        <div class="progress-bar bg-success" id="processingSpeedBar" style="width: 85%"></div>
                                    </div>
                                </div>

                                <div class="metric-item mb-3">
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>Memory Usage</span>
                                        <span class="badge bg-info" id="memoryUsage">67%</span>
                                    </div>
                                    <div class="progress" style="height: 6px;">
                                        <div class="progress-bar bg-info" id="memoryUsageBar" style="width: 67%"></div>
                                    </div>
                                </div>

                                <div class="metric-item mb-3">
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>Network Latency</span>
                                        <span class="badge bg-warning" id="networkLatency">45ms</span>
                                    </div>
                                    <div class="progress" style="height: 6px;">
                                        <div class="progress-bar bg-warning" id="networkLatencyBar" style="width: 30%"></div>
                                    </div>
                                </div>

                                <div class="metric-item mb-3">
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>Error Rate</span>
                                        <span class="badge bg-success" id="errorRate">0.02%</span>
                                    </div>
                                    <div class="progress" style="height: 6px;">
                                        <div class="progress-bar bg-success" id="errorRateBar" style="width: 2%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Live Feed and Alerts -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0"><i class="fas fa-rss"></i> Live Activity Feed</h6>
                            <div>
                                <button class="btn btn-outline-secondary btn-sm" onclick="clearActivityFeed()">
                                    <i class="fas fa-trash"></i> Clear
                                </button>
                                <button class="btn btn-outline-primary btn-sm" onclick="pauseActivityFeed()" id="pauseFeedBtn">
                                    <i class="fas fa-pause"></i> Pause
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="liveFeed" style="max-height: 400px; overflow-y: auto;">
                                <!-- Live feed items will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Active Alerts</h6>
                            <span class="badge bg-danger" id="alertCount">3</span>
                        </div>
                        <div class="card-body">
                            <div id="activeAlerts">
                                <!-- Active alerts will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Anomaly Detection -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-search"></i> Anomaly Detection</h6>
                        </div>
                        <div class="card-body">
                            <div id="anomalyDetection">
                                <div class="anomaly-item mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1">Sentiment Anomaly</h6>
                                            <small class="text-muted">Unusual negative sentiment spike in tech sector</small>
                                        </div>
                                        <span class="badge bg-warning">Medium</span>
                                    </div>
                                    <div class="progress mt-2" style="height: 4px;">
                                        <div class="progress-bar bg-warning" style="width: 65%"></div>
                                    </div>
                                </div>

                                <div class="anomaly-item mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1">Document Volume Spike</h6>
                                            <small class="text-muted">300% increase in policy documents from EU</small>
                                        </div>
                                        <span class="badge bg-danger">High</span>
                                    </div>
                                    <div class="progress mt-2" style="height: 4px;">
                                        <div class="progress-bar bg-danger" style="width: 85%"></div>
                                    </div>
                                </div>

                                <div class="anomaly-item mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1">Network Pattern Change</h6>
                                            <small class="text-muted">New collaboration patterns detected</small>
                                        </div>
                                        <span class="badge bg-info">Low</span>
                                    </div>
                                    <div class="progress mt-2" style="height: 4px;">
                                        <div class="progress-bar bg-info" style="width: 35%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-chart-pie"></i> Alert Distribution</h6>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="alertDistributionChart" style="height: 250px;"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Comparative Analysis Section -->
        <div id="compare-section" class="content-section" style="display: none;">
            <h1 class="section-title">📊 Comparative Analysis</h1>

            <!-- Comparison Controls -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cogs"></i> Comparison Setup</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <label for="comparisonType" class="form-label">Comparison Type</label>
                            <select class="form-select" id="comparisonType">
                                <option value="organizations">Organization Comparison</option>
                                <option value="timeperiods">Time Period Comparison</option>
                                <option value="sectors">Sector Comparison</option>
                                <option value="policies">Policy Stance Comparison</option>
                                <option value="sentiment">Sentiment Comparison</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="comparisonMetric" class="form-label">Primary Metric</label>
                            <select class="form-select" id="comparisonMetric">
                                <option value="sentiment">Sentiment Analysis</option>
                                <option value="policy_stance">Policy Stance</option>
                                <option value="moral_framework">Moral Framework</option>
                                <option value="influence">Influence Score</option>
                                <option value="document_count">Document Volume</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-primary mt-4" onclick="runComparativeAnalysis()">
                                <i class="fas fa-balance-scale"></i> Run Comparison
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Organization Selection -->
            <div class="card mb-4" id="organizationSelectionCard">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-building"></i> Select Organizations to Compare</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Available Organizations</h6>
                            <div id="availableOrganizations" class="organization-list">
                                <div class="organization-item" data-org="google">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="org-google" value="google">
                                        <label class="form-check-label" for="org-google">
                                            <strong>Google LLC</strong> <span class="badge bg-primary">Corporate</span>
                                            <br><small class="text-muted">Technology • 156 documents</small>
                                        </label>
                                    </div>
                                </div>
                                <div class="organization-item" data-org="microsoft">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="org-microsoft" value="microsoft">
                                        <label class="form-check-label" for="org-microsoft">
                                            <strong>Microsoft Corporation</strong> <span class="badge bg-primary">Corporate</span>
                                            <br><small class="text-muted">Technology • 134 documents</small>
                                        </label>
                                    </div>
                                </div>
                                <div class="organization-item" data-org="openai">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="org-openai" value="openai">
                                        <label class="form-check-label" for="org-openai">
                                            <strong>OpenAI</strong> <span class="badge bg-primary">Corporate</span>
                                            <br><small class="text-muted">AI Research • 89 documents</small>
                                        </label>
                                    </div>
                                </div>
                                <div class="organization-item" data-org="mit">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="org-mit" value="mit">
                                        <label class="form-check-label" for="org-mit">
                                            <strong>MIT</strong> <span class="badge bg-success">Academic</span>
                                            <br><small class="text-muted">Education • 78 documents</small>
                                        </label>
                                    </div>
                                </div>
                                <div class="organization-item" data-org="stanford">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="org-stanford" value="stanford">
                                        <label class="form-check-label" for="org-stanford">
                                            <strong>Stanford HAI</strong> <span class="badge bg-success">Academic</span>
                                            <br><small class="text-muted">Education • 67 documents</small>
                                        </label>
                                    </div>
                                </div>
                                <div class="organization-item" data-org="eu_commission">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="org-eu" value="eu_commission">
                                        <label class="form-check-label" for="org-eu">
                                            <strong>EU Commission</strong> <span class="badge bg-warning">Government</span>
                                            <br><small class="text-muted">Policy • 45 documents</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>Selected for Comparison (<span id="selectedCount">0</span>)</h6>
                            <div id="selectedOrganizations" class="selected-list">
                                <p class="text-muted">Select organizations from the left to compare</p>
                            </div>
                            <div class="mt-3">
                                <button class="btn btn-outline-secondary btn-sm" onclick="clearSelection()">
                                    <i class="fas fa-times"></i> Clear All
                                </button>
                                <button class="btn btn-outline-primary btn-sm ms-2" onclick="selectRecommended()">
                                    <i class="fas fa-magic"></i> Select Recommended
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Comparison Results -->
            <div id="comparisonResults" class="mt-4">
                <div class="text-center text-muted py-4">
                    <i class="fas fa-balance-scale fa-3x mb-3"></i>
                    <p>Select organizations and run comparison to see results</p>
                </div>
            </div>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-spinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-2">Processing request...</p>
    </div>

    <script src="/dashboard/frontend/dashboard.js"></script>
    <script src="/dashboard/frontend/dashboard_enhancements.js"></script>
    <script src="/dashboard/frontend/fix_navigation.js"></script>
    <script src="/fix_document_analysis_issue.js"></script>
</body>
</html>