#!/usr/bin/env python3
"""
AI Policy Analyzer - Complete System Launcher
统一启动脚本，确保所有模块正确初始化和运行

Author: Claude Code
Date: 2025-08-10
"""

import os
import sys
import time
import subprocess
import threading
import signal
import json
from pathlib import Path
from datetime import datetime

class SystemLauncher:
    """系统启动器"""
    
    def __init__(self):
        self.processes = []
        self.base_dir = Path(__file__).parent
        self.dashboard_dir = self.base_dir / "dashboard"
        self.backend_dir = self.dashboard_dir / "backend"
        self.frontend_dir = self.dashboard_dir / "frontend"
        
    def check_dependencies(self):
        """检查依赖项"""
        print("🔍 检查系统依赖...")
        
        # 检查Python版本
        if sys.version_info < (3, 8):
            print("❌ 需要Python 3.8或更高版本")
            return False
            
        # 检查必要的包
        required_packages = [
            'flask', 'flask_cors', 'pandas', 'numpy', 'sqlite3'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            print(f"❌ 缺少必要的包: {', '.join(missing_packages)}")
            print("请运行: pip install flask flask-cors pandas numpy")
            return False
            
        print("✅ 所有依赖项检查通过")
        return True
    
    def check_file_structure(self):
        """检查文件结构"""
        print("📁 检查文件结构...")
        
        required_files = [
            self.dashboard_dir / "frontend" / "index.html",
            self.dashboard_dir / "frontend" / "dashboard.js",
            self.dashboard_dir / "frontend" / "server.py",
            self.backend_dir / "visualization_api.py",
            self.backend_dir / "search_endpoints.py",
            self.backend_dir / "historical_data_endpoints.py",
        ]
        
        missing_files = []
        for file_path in required_files:
            if not file_path.exists():
                missing_files.append(str(file_path))
        
        if missing_files:
            print(f"❌ 缺少必要文件:")
            for file in missing_files:
                print(f"   - {file}")
            return False
            
        print("✅ 文件结构检查通过")
        return True
    
    def start_backend_services(self):
        """启动后端服务"""
        print("🚀 启动后端服务...")
        
        backend_services = [
            {
                'name': 'Visualization API',
                'script': 'visualization_api.py',
                'port': 5001,
                'description': '数据可视化API服务'
            },
            {
                'name': 'Search API',
                'script': 'search_endpoints.py',
                'port': 5002,
                'description': '搜索和发现API服务'
            },
            {
                'name': 'Historical Data API',
                'script': 'historical_data_endpoints.py',
                'port': 5003,
                'description': '历史数据API服务'
            }
        ]
        
        for service in backend_services:
            try:
                script_path = self.backend_dir / service['script']
                if script_path.exists():
                    print(f"   启动 {service['name']} (端口 {service['port']})...")
                    
                    process = subprocess.Popen(
                        [sys.executable, str(script_path)],
                        cwd=str(self.backend_dir),
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE
                    )
                    
                    self.processes.append({
                        'name': service['name'],
                        'process': process,
                        'port': service['port']
                    })
                    
                    time.sleep(2)  # 等待服务启动
                    print(f"   ✅ {service['name']} 启动成功")
                else:
                    print(f"   ⚠️ {service['name']} 脚本不存在: {script_path}")
                    
            except Exception as e:
                print(f"   ❌ {service['name']} 启动失败: {e}")
    
    def start_frontend_service(self):
        """启动前端服务"""
        print("🌐 启动前端服务...")
        
        try:
            server_script = self.frontend_dir / "server.py"
            if server_script.exists():
                print("   启动前端服务器 (端口 8000)...")
                
                process = subprocess.Popen(
                    [sys.executable, str(server_script)],
                    cwd=str(self.frontend_dir),
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )
                
                self.processes.append({
                    'name': 'Frontend Server',
                    'process': process,
                    'port': 8000
                })
                
                time.sleep(2)
                print("   ✅ 前端服务启动成功")
            else:
                print(f"   ❌ 前端服务器脚本不存在: {server_script}")
                
        except Exception as e:
            print(f"   ❌ 前端服务启动失败: {e}")
    
    def check_services_health(self):
        """检查服务健康状态"""
        print("🏥 检查服务健康状态...")
        
        import requests
        
        services_to_check = [
            {'name': 'Frontend', 'url': 'http://localhost:8000'},
            {'name': 'Visualization API', 'url': 'http://localhost:5001/api/health'},
            {'name': 'Search API', 'url': 'http://localhost:5002/api/health'},
            {'name': 'Historical Data API', 'url': 'http://localhost:5003/api/health'},
        ]
        
        healthy_services = 0
        for service in services_to_check:
            try:
                response = requests.get(service['url'], timeout=5)
                if response.status_code == 200:
                    print(f"   ✅ {service['name']}: 健康")
                    healthy_services += 1
                else:
                    print(f"   ⚠️ {service['name']}: 响应异常 ({response.status_code})")
            except Exception as e:
                print(f"   ❌ {service['name']}: 无法连接")
        
        print(f"📊 服务健康状态: {healthy_services}/{len(services_to_check)} 服务正常")
        return healthy_services == len(services_to_check)
    
    def show_access_info(self):
        """显示访问信息"""
        print("\n" + "="*60)
        print("🎯 AI Policy Analyzer 系统启动完成!")
        print("="*60)
        print("📱 访问地址:")
        print("   🌐 主控制台: http://localhost:8000")
        print("   📊 可视化API: http://localhost:5001")
        print("   🔍 搜索API: http://localhost:5002")
        print("   📚 历史数据API: http://localhost:5003")
        print("\n💡 使用提示:")
        print("   - 打开浏览器访问 http://localhost:8000")
        print("   - 使用 Ctrl+C 停止所有服务")
        print("   - 查看各个模块的功能和分析结果")
        print("="*60)
    
    def cleanup(self):
        """清理进程"""
        print("\n🛑 正在停止所有服务...")
        
        for service in self.processes:
            try:
                service['process'].terminate()
                service['process'].wait(timeout=5)
                print(f"   ✅ {service['name']} 已停止")
            except subprocess.TimeoutExpired:
                service['process'].kill()
                print(f"   🔪 {service['name']} 强制停止")
            except Exception as e:
                print(f"   ⚠️ {service['name']} 停止时出错: {e}")
        
        print("👋 系统已完全停止")
    
    def run(self):
        """运行完整系统"""
        print("🚀 AI Policy Analyzer - 完整系统启动器")
        print("="*60)
        
        # 检查依赖
        if not self.check_dependencies():
            return False
            
        # 检查文件结构
        if not self.check_file_structure():
            return False
        
        try:
            # 启动后端服务
            self.start_backend_services()
            
            # 启动前端服务
            self.start_frontend_service()
            
            # 等待服务完全启动
            print("⏳ 等待服务完全启动...")
            time.sleep(5)
            
            # 检查服务健康状态
            self.check_services_health()
            
            # 显示访问信息
            self.show_access_info()
            
            # 等待用户中断
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                pass
                
        except Exception as e:
            print(f"❌ 系统启动失败: {e}")
            return False
        finally:
            self.cleanup()
        
        return True

def main():
    """主函数"""
    launcher = SystemLauncher()
    
    # 设置信号处理
    def signal_handler(sig, frame):
        launcher.cleanup()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 运行系统
    success = launcher.run()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
