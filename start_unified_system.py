#!/usr/bin/env python3
"""
启动统一AI Policy Dashboard系统
包括后端数据处理、API服务器、前端Dashboard

Author: Claude Code
Date: 2025-08-08
"""

import os
import sys
import time
import subprocess
import threading
import webbrowser
from pathlib import Path
import argparse
import signal

def print_banner():
    """打印启动横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🚀 AI Policy Dashboard - Unified System Launcher     ║
    ║                                                              ║
    ║        📊 Frontend Dashboard + 🔧 Backend Processing        ║
    ║        📡 Real-time Monitoring + 📄 Document Analysis       ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_dependencies():
    """检查系统依赖"""
    print("🔍 检查系统依赖...")
    
    required_packages = [
        'flask',
        'flask-cors',
        'sqlite3',
        'pathlib'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'sqlite3':
                import sqlite3
            elif package == 'pathlib':
                from pathlib import Path
            else:
                __import__(package.replace('-', '_'))
            print(f"  ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"  ❌ {package}")
    
    if missing_packages:
        print(f"\n⚠️ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ 所有依赖检查通过")
    return True

def check_data_directory():
    """检查数据目录"""
    print("📁 检查数据目录...")
    
    data_dir = Path("data/90_FR_9088_pdfs")
    
    if not data_dir.exists():
        print(f"⚠️ 数据目录不存在: {data_dir}")
        print("请确保PDF文件位于 data/90_FR_9088_pdfs 目录中")
        return False
    
    pdf_files = list(data_dir.glob("*.pdf"))
    csv_files = list(data_dir.glob("*.csv"))
    
    print(f"  📄 找到 {len(pdf_files)} 个PDF文件")
    print(f"  📊 找到 {len(csv_files)} 个CSV文件")
    
    if len(pdf_files) == 0:
        print("⚠️ 未找到PDF文件")
        return False
    
    print("✅ 数据目录检查通过")
    return True

def start_backend_server():
    """启动后端服务器"""
    print("🔧 启动后端数据处理服务器...")
    
    try:
        # 启动统一后端系统
        backend_process = subprocess.Popen([
            sys.executable, 
            "unified_backend_system.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        print("✅ 后端服务器启动成功 (端口: 5001)")
        return backend_process
        
    except Exception as e:
        print(f"❌ 后端服务器启动失败: {e}")
        return None

def start_frontend_server():
    """启动前端服务器"""
    print("🌐 启动前端Dashboard服务器...")
    
    try:
        # 使用Python内置HTTP服务器
        frontend_process = subprocess.Popen([
            sys.executable, 
            "-m", "http.server", 
            "8000"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        print("✅ 前端服务器启动成功 (端口: 8000)")
        return frontend_process
        
    except Exception as e:
        print(f"❌ 前端服务器启动失败: {e}")
        return None

def wait_for_backend():
    """等待后端服务器启动"""
    print("⏳ 等待后端服务器启动...")
    
    import urllib.request
    import urllib.error
    
    max_attempts = 30
    for attempt in range(max_attempts):
        try:
            response = urllib.request.urlopen("http://localhost:5001/api/system/status", timeout=2)
            if response.getcode() == 200:
                print("✅ 后端服务器已就绪")
                return True
        except (urllib.error.URLError, urllib.error.HTTPError):
            pass
        
        time.sleep(1)
        print(f"  等待中... ({attempt + 1}/{max_attempts})")
    
    print("❌ 后端服务器启动超时")
    return False

def open_dashboard():
    """打开Dashboard页面"""
    print("🌐 打开Dashboard页面...")
    
    urls = [
        "http://localhost:8000/dashboard/frontend/index.html",
        "http://localhost:8000/backend_management_dashboard.html",
        "http://localhost:8000/comprehensive_test_suite.html"
    ]
    
    for url in urls:
        try:
            webbrowser.open(url)
            time.sleep(1)
        except Exception as e:
            print(f"⚠️ 无法打开 {url}: {e}")

def monitor_processes(processes):
    """监控进程状态"""
    print("📊 监控系统进程...")
    
    while True:
        try:
            for name, process in processes.items():
                if process and process.poll() is not None:
                    print(f"⚠️ {name} 进程已停止")
                    return False
            
            time.sleep(5)
            
        except KeyboardInterrupt:
            print("\n🛑 收到停止信号...")
            return True

def cleanup_processes(processes):
    """清理进程"""
    print("🧹 清理进程...")
    
    for name, process in processes.items():
        if process:
            try:
                process.terminate()
                process.wait(timeout=5)
                print(f"✅ {name} 进程已停止")
            except subprocess.TimeoutExpired:
                process.kill()
                print(f"🔪 强制停止 {name} 进程")
            except Exception as e:
                print(f"⚠️ 停止 {name} 进程时出错: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="启动AI Policy Dashboard统一系统")
    parser.add_argument("--no-browser", action="store_true", help="不自动打开浏览器")
    parser.add_argument("--backend-only", action="store_true", help="仅启动后端服务")
    parser.add_argument("--frontend-only", action="store_true", help="仅启动前端服务")
    parser.add_argument("--skip-checks", action="store_true", help="跳过依赖检查")
    
    args = parser.parse_args()
    
    # 打印横幅
    print_banner()
    
    # 检查依赖和数据
    if not args.skip_checks:
        if not check_dependencies():
            sys.exit(1)
        
        if not check_data_directory():
            print("⚠️ 数据目录检查失败，但系统仍可启动（功能受限）")
    
    processes = {}
    
    try:
        # 启动后端服务器
        if not args.frontend_only:
            backend_process = start_backend_server()
            if backend_process:
                processes['Backend'] = backend_process
                
                # 等待后端启动
                if not wait_for_backend():
                    print("❌ 后端服务器启动失败")
                    cleanup_processes(processes)
                    sys.exit(1)
        
        # 启动前端服务器
        if not args.backend_only:
            frontend_process = start_frontend_server()
            if frontend_process:
                processes['Frontend'] = frontend_process
                time.sleep(2)  # 等待前端服务器启动
        
        # 打开浏览器
        if not args.no_browser and not args.backend_only:
            time.sleep(1)
            open_dashboard()
        
        # 显示访问信息
        print("\n" + "="*60)
        print("🎉 系统启动完成！")
        print("\n📊 访问地址:")
        
        if not args.backend_only:
            print("  🌐 主Dashboard: http://localhost:8000/dashboard/frontend/index.html")
            print("  🖥️ 后端管理: http://localhost:8000/backend_management_dashboard.html")
            print("  🧪 测试套件: http://localhost:8000/comprehensive_test_suite.html")
        
        if not args.frontend_only:
            print("  🔧 后端API: http://localhost:5001/api/system/status")
        
        print("\n⌨️ 按 Ctrl+C 停止系统")
        print("="*60)
        
        # 监控进程
        try:
            monitor_processes(processes)
        except KeyboardInterrupt:
            print("\n🛑 收到停止信号...")
    
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
    
    finally:
        # 清理进程
        cleanup_processes(processes)
        print("👋 系统已停止")

if __name__ == "__main__":
    main()
