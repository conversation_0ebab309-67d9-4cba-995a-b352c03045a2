<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Network Analysis Module Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .community-item {
            transition: all 0.3s ease;
        }
        
        .community-item:hover {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 8px;
        }
        
        .pathway-item {
            transition: all 0.3s ease;
        }
        
        .pathway-item:hover {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 8px;
        }
        
        #networkCanvas {
            cursor: grab;
        }
        
        #networkCanvas:active {
            cursor: grabbing;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-project-diagram me-2"></i>
            Network Analysis Module Test
        </h1>
        
        <!-- Network Controls -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cogs"></i> Network Analysis Controls</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label for="networkType" class="form-label">Network Type</label>
                        <select class="form-select" id="networkType">
                            <option value="policy">Policy Similarity</option>
                            <option value="collaboration">Collaboration Network</option>
                            <option value="influence">Influence Network</option>
                            <option value="citation">Citation Network</option>
                            <option value="topic">Topic Co-occurrence</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="networkLayout" class="form-label">Layout Algorithm</label>
                        <select class="form-select" id="networkLayout">
                            <option value="force">Force-Directed</option>
                            <option value="circular">Circular</option>
                            <option value="hierarchical">Hierarchical</option>
                            <option value="grid">Grid</option>
                            <option value="community">Community-Based</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="networkFilter" class="form-label">Organization Filter</label>
                        <select class="form-select" id="networkFilter">
                            <option value="all">All Organizations</option>
                            <option value="corporate">Corporate Only</option>
                            <option value="academic">Academic Only</option>
                            <option value="government">Government Only</option>
                            <option value="nonprofit">Nonprofit Only</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-primary mt-4" onclick="generateNetworkAnalysis()">
                            <i class="fas fa-project-diagram"></i> Generate Network
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main Network Visualization -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0"><i class="fas fa-share-alt"></i> Interactive Network Graph</h6>
                        <div>
                            <button class="btn btn-outline-secondary btn-sm" onclick="resetNetworkView()">
                                <i class="fas fa-undo"></i> Reset View
                            </button>
                            <button class="btn btn-outline-primary btn-sm" onclick="exportNetwork()">
                                <i class="fas fa-download"></i> Export
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="networkVisualization" style="height: 500px; border: 1px solid #ddd; border-radius: 8px; background: #f8f9fa; position: relative;">
                            <div class="text-center" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                                <i class="fas fa-project-diagram fa-3x text-muted mb-3"></i>
                                <p class="text-muted">Interactive network visualization will appear here</p>
                                <small>Click "Generate Network" to create visualization</small>
                            </div>
                            <canvas id="networkCanvas" style="width: 100%; height: 100%; display: none;"></canvas>
                        </div>
                        <div class="mt-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <i class="fas fa-mouse"></i> Click and drag to pan • Scroll to zoom
                                    </small>
                                </div>
                                <div class="col-md-6 text-end">
                                    <small class="text-muted">
                                        Nodes: <span id="nodeCount">0</span> • Edges: <span id="edgeCount">0</span>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-crown"></i> Network Metrics</h6>
                    </div>
                    <div class="card-body">
                        <div id="networkMetrics">
                            <div class="mb-3">
                                <h6>Global Metrics</h6>
                                <div class="d-flex justify-content-between mb-1">
                                    <span>Density</span>
                                    <span class="badge bg-info" id="networkDensity">0.23</span>
                                </div>
                                <div class="d-flex justify-content-between mb-1">
                                    <span>Clustering Coefficient</span>
                                    <span class="badge bg-success" id="clusteringCoeff">0.67</span>
                                </div>
                                <div class="d-flex justify-content-between mb-1">
                                    <span>Average Path Length</span>
                                    <span class="badge bg-warning" id="avgPathLength">3.2</span>
                                </div>
                                <div class="d-flex justify-content-between mb-1">
                                    <span>Modularity</span>
                                    <span class="badge bg-primary" id="modularity">0.45</span>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <h6>Most Influential</h6>
                                <div id="influentialNodes">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Google LLC</span>
                                        <span class="badge bg-primary">9.2</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Microsoft</span>
                                        <span class="badge bg-primary">8.7</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>OpenAI</span>
                                        <span class="badge bg-primary">8.1</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Community Detection -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-users"></i> Community Detection</h6>
                    </div>
                    <div class="card-body">
                        <div id="communityAnalysis">
                            <div class="mb-3">
                                <h6>Detected Communities</h6>
                                <div class="community-item mb-2">
                                    <div class="d-flex align-items-center">
                                        <div class="community-color" style="width: 20px; height: 20px; background: #FF6B6B; border-radius: 50%; margin-right: 10px;"></div>
                                        <div>
                                            <strong>Tech Giants</strong> (8 organizations)<br>
                                            <small class="text-muted">Google, Microsoft, Apple, Meta, Amazon</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="community-item mb-2">
                                    <div class="d-flex align-items-center">
                                        <div class="community-color" style="width: 20px; height: 20px; background: #4ECDC4; border-radius: 50%; margin-right: 10px;"></div>
                                        <div>
                                            <strong>Academic Institutions</strong> (12 organizations)<br>
                                            <small class="text-muted">MIT, Stanford, CMU, Berkeley, Oxford</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="community-item mb-2">
                                    <div class="d-flex align-items-center">
                                        <div class="community-color" style="width: 20px; height: 20px; background: #45B7D1; border-radius: 50%; margin-right: 10px;"></div>
                                        <div>
                                            <strong>Policy Organizations</strong> (6 organizations)<br>
                                            <small class="text-muted">EU Commission, FTC, NIST, IEEE</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="community-item mb-2">
                                    <div class="d-flex align-items-center">
                                        <div class="community-color" style="width: 20px; height: 20px; background: #F7DC6F; border-radius: 50%; margin-right: 10px;"></div>
                                        <div>
                                            <strong>AI Safety Groups</strong> (5 organizations)<br>
                                            <small class="text-muted">OpenAI, Anthropic, DeepMind, MIRI</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-chart-pie"></i> Community Statistics</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="communityChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Information Flow Analysis -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-route"></i> Information Flow Paths</h6>
                    </div>
                    <div class="card-body">
                        <div id="informationFlow">
                            <div class="mb-3">
                                <h6>Key Information Pathways</h6>
                                <div class="pathway-item mb-2">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-arrow-right text-primary me-2"></i>
                                        <span><strong>Google</strong> → <strong>Stanford</strong> → <strong>Policy Makers</strong></span>
                                    </div>
                                    <small class="text-muted ms-3">Research influence pathway (strength: 0.87)</small>
                                </div>
                                <div class="pathway-item mb-2">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-arrow-right text-success me-2"></i>
                                        <span><strong>MIT</strong> → <strong>Industry</strong> → <strong>Standards</strong></span>
                                    </div>
                                    <small class="text-muted ms-3">Technology transfer pathway (strength: 0.73)</small>
                                </div>
                                <div class="pathway-item mb-2">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-arrow-right text-warning me-2"></i>
                                        <span><strong>OpenAI</strong> → <strong>Safety Groups</strong> → <strong>Regulators</strong></span>
                                    </div>
                                    <small class="text-muted ms-3">Safety advocacy pathway (strength: 0.69)</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-chart-line"></i> Network Evolution</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="networkEvolutionChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Controls -->
        <div class="test-section">
            <h2><i class="fas fa-cogs me-2"></i>Test Controls</h2>
            <div class="row">
                <div class="col-md-6">
                    <h6>Quick Test Networks</h6>
                    <button class="btn btn-outline-primary me-2 mb-2" onclick="testNetwork('policy')">
                        <i class="fas fa-gavel"></i> Test Policy Network
                    </button>
                    <button class="btn btn-outline-success me-2 mb-2" onclick="testNetwork('collaboration')">
                        <i class="fas fa-handshake"></i> Test Collaboration
                    </button>
                    <button class="btn btn-outline-info me-2 mb-2" onclick="testLayout('force')">
                        <i class="fas fa-project-diagram"></i> Test Force Layout
                    </button>
                    <button class="btn btn-outline-warning me-2 mb-2" onclick="testLayout('community')">
                        <i class="fas fa-users"></i> Test Community Layout
                    </button>
                </div>
                <div class="col-md-6">
                    <h6>Test Results</h6>
                    <div id="testResults" class="alert alert-info">
                        <p><strong>Test Status:</strong> Ready to test network functionality</p>
                        <ul>
                            <li>✅ Interactive network visualization</li>
                            <li>✅ Multiple layout algorithms</li>
                            <li>✅ Community detection & analysis</li>
                            <li>✅ Centrality metrics calculation</li>
                            <li>✅ Information flow pathways</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="dashboard/frontend/dashboard.js"></script>
    <script>
        // Test functions
        function testNetwork(networkType) {
            console.log(`🧪 Testing network type: ${networkType}`);
            document.getElementById('networkType').value = networkType;
            generateNetworkAnalysis();
            
            const testResults = document.getElementById('testResults');
            testResults.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle"></i> Network Test: ${networkType}</h6>
                    <p>✅ Network type changed to ${networkType}</p>
                    <p>✅ Network visualization updated</p>
                    <p>✅ Metrics recalculated</p>
                    <p><strong>Try:</strong> Pan and zoom the network visualization</p>
                </div>
            `;
        }
        
        function testLayout(layoutType) {
            console.log(`🧪 Testing layout: ${layoutType}`);
            document.getElementById('networkLayout').value = layoutType;
            generateNetworkAnalysis();
            
            const testResults = document.getElementById('testResults');
            testResults.innerHTML = `
                <div class="alert alert-info">
                    <h6><i class="fas fa-project-diagram"></i> Layout Test: ${layoutType}</h6>
                    <p>✅ Layout algorithm changed to ${layoutType}</p>
                    <p>✅ Node positions recalculated</p>
                    <p>✅ Network redrawn with new layout</p>
                    <p><strong>Try:</strong> Compare different layout algorithms</p>
                </div>
            `;
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Network Analysis Test Page Loaded');
            
            // Initialize network functionality
            if (typeof loadNetworkAnalysis === 'function') {
                loadNetworkAnalysis();
            } else {
                console.warn('Network functions not loaded. Make sure dashboard.js is included.');
            }
        });
    </script>
</body>
</html>
