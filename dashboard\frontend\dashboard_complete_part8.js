/**
 * Create pagination for historical data
 */
function createHistoricalPagination(currentPage, totalPages) {
    const historyPagination = document.getElementById('historyPagination');
    if (!historyPagination) return;
    
    let paginationHTML = '';
    
    // Previous button
    paginationHTML += `
        <li class="page-item ${currentPage <= 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1}); return false;" tabindex="-1">Previous</a>
        </li>
    `;
    
    // Page numbers
    // Show up to 5 page numbers, centered around current page if possible
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, startPage + 4);
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="changePage(${i}); return false;">${i}</a>
            </li>
        `;
    }
    
    // Next button
    paginationHTML += `
        <li class="page-item ${currentPage >= totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1}); return false;">Next</a>
        </li>
    `;
    
    historyPagination.innerHTML = paginationHTML;
}

/**
 * Change page in historical data
 */
function changePage(page) {
    // Get current filters
    const dateRange = document.getElementById('historyDateRange')?.value || '30';
    const orgType = document.getElementById('historyOrgType')?.value || '';
    const docType = document.getElementById('historyDocType')?.value || '';
    
    // Update with new page
    loadHistoricalData({
        dateRange: dateRange,
        orgType: orgType,
        docType: docType,
        page: page,
        sort: dashboardState.historicalSort,
        sortDir: dashboardState.historicalSortDir
    });
}

/**
 * Apply filters to historical data
 */
function applyHistoricalFilters() {
    const dateRange = document.getElementById('historyDateRange')?.value || '30';
    const orgType = document.getElementById('historyOrgType')?.value || '';
    const docType = document.getElementById('historyDocType')?.value || '';
    
    // Reset to page 1 when applying filters
    loadHistoricalData({
        dateRange: dateRange,
        orgType: orgType,
        docType: docType,
        page: 1,
        sort: dashboardState.historicalSort,
        sortDir: dashboardState.historicalSortDir
    });
}

/**
 * Sort historical data
 */
function sortHistoricalData(column) {
    // Toggle sort direction if same column is clicked again
    if (dashboardState.historicalSort === column) {
        dashboardState.historicalSortDir = dashboardState.historicalSortDir === 'asc' ? 'desc' : 'asc';
    } else {
        dashboardState.historicalSort = column;
        dashboardState.historicalSortDir = 'desc'; // Default to descending for first click
    }
    
    // Get current filters
    const dateRange = document.getElementById('historyDateRange')?.value || '30';
    const orgType = document.getElementById('historyOrgType')?.value || '';
    const docType = document.getElementById('historyDocType')?.value || '';
    const page = dashboardState.historicalData?.page || 1;
    
    // Update with sort
    loadHistoricalData({
        dateRange: dateRange,
        orgType: orgType,
        docType: docType,
        page: page,
        sort: dashboardState.historicalSort,
        sortDir: dashboardState.historicalSortDir
    });
}

/**
 * Refresh historical data
 */
function refreshHistoricalData() {
    // Get current filters
    const dateRange = document.getElementById('historyDateRange')?.value || '30';
    const orgType = document.getElementById('historyOrgType')?.value || '';
    const docType = document.getElementById('historyDocType')?.value || '';
    
    // Show notification
    showNotification('Refreshing historical data...', 'info');
    
    // Reset to page 1 when refreshing
    loadHistoricalData({
        dateRange: dateRange,
        orgType: orgType,
        docType: docType,
        page: 1,
        sort: dashboardState.historicalSort,
        sortDir: dashboardState.historicalSortDir
    });
}

/**
 * Export historical data to CSV
 */
function exportHistoricalData() {
    try {
        // Get current data
        const items = dashboardState.historicalData?.data || [];
        
        if (items.length === 0) {
            showNotification('No data to export', 'warning');
            return;
        }
        
        // Create CSV header row
        let csv = 'Date,Organization,Type,Document,Sentiment\n';
        
        // Add data rows
        items.forEach(item => {
            const formattedDate = formatDate(new Date(item.date));
            const row = [
                formattedDate,
                item.organization_name,
                item.organization_type,
                item.document_type,
                item.sentiment
            ].map(cell => `"${cell}"`).join(',');
            
            csv += row + '\n';
        });
        
        // Create download link
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.setAttribute('href', url);
        link.setAttribute('download', 'historical_data.csv');
        link.style.visibility = 'hidden';
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        showNotification('Data exported to CSV', 'success');
    } catch (error) {
        console.error('Error exporting data:', error);
        showNotification('Failed to export data', 'danger');
    }
}

/**
 * View historical item
 */
function viewHistoricalItem(itemId) {
    console.log(`Viewing historical item: ${itemId}`);
    
    // Show loading
    showLoading(true);
    
    // Try to fetch item data from API
    fetch(`${API_CONFIG.history}/item/${itemId}`)
        .then(response => {
            if (!response.ok) throw new Error('API unavailable');
            return response.json();
        })
        .then(data => {
            // Display item from API
            displayHistoricalItemView(data);
        })
        .catch(error => {
            console.warn(`Failed to fetch historical item ${itemId}, using sample data`);
            
            // Generate sample analysis data as fallback
            const sampleItem = {
                id: itemId,
                date: new Date().toISOString(),
                organization_name: itemId.includes('_') ? 
                    itemId.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ') : 
                    `Organization ${itemId.substring(itemId.length - 3)}`,
                organization_type: ['Corporate', 'Academic', 'Nonprofit', 'Government'][Math.floor(Math.random() * 4)],
                document_type: ['Policy Statement', 'Guidelines', 'Position Paper', 'RFI Response'][Math.floor(Math.random() * 4)],
                sentiment: ['Positive', 'Neutral', 'Negative'][Math.floor(Math.random() * 3)],
                analysis: {
                    sentiment_scores: {
                        positive: (Math.random() * 0.6 + 0.3).toFixed(2),
                        neutral: (Math.random() * 0.3).toFixed(2),
                        negative: (Math.random() * 0.3).toFixed(2)
                    },
                    policy_stance: {
                        self_regulation: (Math.random() * 0.6).toFixed(2),
                        co_regulation: (Math.random() * 0.5).toFixed(2),
                        government_oversight: (Math.random() * 0.4).toFixed(2)
                    },
                    key_phrases: [
                        'artificial intelligence',
                        'policy framework',
                        'regulatory approach',
                        'ethics guidelines',
                        'safety measures'
                    ],
                    summary: `This sample document from ${itemId.includes('_') ? 
                        itemId.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ') : 
                        `Organization ${itemId.substring(itemId.length - 3)}`} outlines a comprehensive approach to AI governance, emphasizing the importance of balancing innovation with appropriate safeguards. The document highlights key priorities including safety, transparency, and accountability.`
                }
            };
            
            // Display sample item
            displayHistoricalItemView(sampleItem);
        })
        .finally(() => {
            // Hide loading
            showLoading(false);
        });
}

/**
 * Display historical item view
 */
function displayHistoricalItemView(item) {
    // First navigate to analysis section
    navigateToSection('analysis');
    
    const analysisResultsContainer = document.getElementById('analysis-results');
    if (!analysisResultsContainer) return;
    
    const sentimentBadgeColor = item.sentiment === 'Positive' ? 'success' : (item.sentiment === 'Negative' ? 'danger' : 'info');
    const formattedDate = formatDate(new Date(item.date));
    
    // Prepare analysis content
    let analysisContent = `
        <div class="d-flex justify-content-between align-items-start mb-4">
            <div>
                <h2 class="h4 mb-1">${item.organization_name}</h2>
                <div class="text-muted">
                    <span class="badge bg-secondary me-2">${item.organization_type}</span>
                    <span>${item.document_type}</span>
                    <span class="mx-2">•</span>
                    <span>${formattedDate}</span>
                </div>
            </div>
            <span class="badge bg-${sentimentBadgeColor} fs-6">${item.sentiment}</span>
        </div>
    `;
    
    // Add summary if available
    if (item.analysis && item.analysis.summary) {
        analysisContent += `
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">Summary</h5>
                </div>
                <div class="card-body">
                    <p class="mb-0">${item.analysis.summary}</p>
                </div>
            </div>
        `;
    }
    
    // Add sentiment analysis if available
    if (item.analysis && item.analysis.sentiment_scores) {
        const scores = item.analysis.sentiment_scores;
        
        analysisContent += `
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">Sentiment Analysis</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label d-flex justify-content-between">
                            <span>Positive</span>
                            <span>${scores.positive}</span>
                        </label>
                        <div class="progress">
                            <div class="progress-bar bg-success" role="progressbar" style="width: ${scores.positive * 100}%" 
                                aria-valuenow="${scores.positive * 100}" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label d-flex justify-content-between">
                            <span>Neutral</span>
                            <span>${scores.neutral}</span>
                        </label>
                        <div class="progress">
                            <div class="progress-bar bg-info" role="progressbar" style="width: ${scores.neutral * 100}%" 
                                aria-valuenow="${scores.neutral * 100}" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                    
                    <div class="mb-0">
                        <label class="form-label d-flex justify-content-between">
                            <span>Negative</span>
                            <span>${scores.negative}</span>
                        </label>
                        <div class="progress">
                            <div class="progress-bar bg-danger" role="progressbar" style="width: ${scores.negative * 100}%" 
                                aria-valuenow="${scores.negative * 100}" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    // Add policy analysis if available
    if (item.analysis && item.analysis.policy_stance) {
        const stance = item.analysis.policy_stance;
        
        analysisContent += `
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">Policy Analysis</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label d-flex justify-content-between">
                            <span>Self-Regulation</span>
                            <span>${stance.self_regulation}</span>
                        </label>
                        <div class="progress">
                            <div class="progress-bar bg-primary" role="progressbar" style="width: ${stance.self_regulation * 100}%" 
                                aria-valuenow="${stance.self_regulation * 100}" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label d-flex justify-content-between">
                            <span>Co-Regulation</span>
                            <span>${stance.co_regulation}</span>
                        </label>
                        <div class="progress">
                            <div class="progress-bar bg-info" role="progressbar" style="width: ${stance.co_regulation * 100}%" 
                                aria-valuenow="${stance.co_regulation * 100}" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                    
                    <div class="mb-0">
                        <label class="form-label d-flex justify-content-between">
                            <span>Government Oversight</span>
                            <span>${stance.government_oversight}</span>
                        </label>
                        <div class="progress">
                            <div class="progress-bar bg-secondary" role="progressbar" style="width: ${stance.government_oversight * 100}%" 
                                aria-valuenow="${stance.government_oversight * 100}" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    // Add key phrases if available
    if (item.analysis && item.analysis.key_phrases && item.analysis.key_phrases.length > 0) {
        analysisContent += `
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">Key Phrases</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex flex-wrap gap-2">
                        ${item.analysis.key_phrases.map(phrase => `<span class="badge bg-primary">${phrase}</span>`).join('')}
                    </div>
                </div>
            </div>
        `;
    }
    
    // Add export button
    analysisContent += `
        <div class="text-end mt-4">
            <button class="btn btn-outline-primary" onclick="exportAnalysis('${item.id}')">
                <i class="fas fa-file-export me-1"></i> Export Analysis
            </button>
        </div>
    `;
    
    // Display in the analysis section
    analysisResultsContainer.innerHTML = analysisContent;
}

/**
 * Export analysis
 */
function exportAnalysis(itemId) {
    console.log(`Exporting analysis for item: ${itemId}`);
    showNotification('Analysis export is not implemented yet', 'info');
    // This would be implemented in a full version to export analysis to PDF or other formats
}

/**
 * Display default analysis section
 */
function displayDefaultAnalysisSection() {
    const analysisResultsContainer = document.getElementById('analysis-results');
    if (!analysisResultsContainer) return;
    
    analysisResultsContainer.innerHTML = `
        <div class="text-center text-muted py-5">
            <i class="fas fa-chart-bar fa-3x mb-3"></i>
            <h3 class="h4 mb-3">Analysis View</h3>
            <p class="mb-4">Select an item from Search or Historical Data to view detailed analysis</p>
            <button class="btn btn-primary" onclick="navigateToSection('search')">
                <i class="fas fa-search me-1"></i> Go to Search
            </button>
        </div>
    `;
}

/**
 * Initialize dashboard
 */
function initializeDashboard() {
    console.log('Initializing dashboard...');
    
    // Fix bug: Generate the network placeholder SVG if it's needed
    generateNetworkPlaceholderSVG();
    
    // Setup sidebar toggler
    setupSidebar();
    
    // Setup section navigation
    setupNavigation();
    
    // Setup search functionality
    setupSearch();
    
    // Setup file upload
    setupFileUpload();
    
    // Load initial dashboard data
    loadDashboardData();
    
    // Display default analysis section
    displayDefaultAnalysisSection();
}

// Initialize dashboard when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Fix bug: Check if all required elements and scripts are loaded
    const requiredElements = [
        document.getElementById('main-dashboard'),
        document.getElementById('sidebarToggle'),
        document.getElementById('total-documents')
    ];
    
    const missingElements = requiredElements.filter(el => !el);
    if (missingElements.length > 0) {
        console.error('Some required elements are missing. Dashboard initialization may fail.');
    } else {
        initializeDashboard();
    }
});
