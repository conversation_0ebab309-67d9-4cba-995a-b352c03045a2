#!/usr/bin/env python3
"""
API健康检查端点修复工具
为所有API服务添加统一的健康检查端点

Author: Claude Code
Date: August 13, 2025
"""

import os
import sys
import glob
import re

# 要添加健康检查端点的API列表
API_HEALTH_ENDPOINTS = {
    "visualization_api.py": {
        "route": "/api/visualize/health",
        "name": "Visualization API",
        "port": 5001
    },
    "analysis_results_api.py": {
        "route": "/api/historical/health", 
        "name": "Historical Data API",
        "port": 5003
    },
    "batch_processing_api.py": {
        "route": "/api/batch/health",
        "name": "Batch Processing API", 
        "port": 5007
    }
}

# 健康检查路由模板
HEALTH_CHECK_TEMPLATE = """
        @self.app.route('{route}', methods=['GET'])
        def health_check():
            return jsonify({{
                'status': 'healthy',
                'service': '{name}',
                'version': '1.0',
                'port': {port}
            }})
"""

def find_api_files(directory):
    """查找后端目录中的所有API文件"""
    api_files = {}
    
    # 查找已知API文件
    for api_name in API_HEALTH_ENDPOINTS.keys():
        full_path = os.path.join(directory, api_name)
        if os.path.exists(full_path):
            api_files[api_name] = full_path
        else:
            # 尝试根据名称模式查找
            pattern = api_name.replace(".py", "*.py")
            matches = glob.glob(os.path.join(directory, pattern))
            if matches:
                api_files[api_name] = matches[0]
    
    # 查找可能缺失的batch_processing_api.py
    if "batch_processing_api.py" not in api_files:
        batch_file = os.path.join(directory, "batch_processing_api.py")
        if not os.path.exists(batch_file):
            # 创建基本的Batch API文件
            create_batch_api(batch_file)
            if os.path.exists(batch_file):
                api_files["batch_processing_api.py"] = batch_file
    
    return api_files

def create_batch_api(file_path):
    """创建基本的Batch API文件"""
    print(f"创建Batch Processing API: {file_path}")
    
    api_code = '''#!/usr/bin/env python3
"""
Batch Processing API for AI Policy Analyzer
Handles batch processing of policy documents

Author: Claude Code
Date: August 13, 2025
"""

import json
import os
import sys
from flask import Flask, jsonify, request
from flask_cors import CORS

class BatchProcessingAPI:
    """Batch Processing API for document processing"""
    
    def __init__(self, port=5007):
        self.app = Flask(__name__)
        CORS(self.app)
        self.port = port
        self._setup_routes()
    
    def _setup_routes(self):
        """Setup Flask routes"""
        
        @self.app.route('/api/batch/health', methods=['GET'])
        def health_check():
            return jsonify({
                'status': 'healthy',
                'service': 'Batch Processing API',
                'version': '1.0',
                'port': 5007
            })
        
        @self.app.route('/api/batch/status', methods=['GET'])
        def get_status():
            return jsonify({
                'active_jobs': 0,
                'completed_jobs': 5,
                'queue_size': 0
            })
        
        @self.app.route('/api/batch/submit', methods=['POST'])
        def submit_job():
            return jsonify({
                'job_id': 'batch-12345',
                'status': 'submitted',
                'position_in_queue': 1
            })
    
    def start(self):
        """Start the API server"""
        print(f"🔄 Starting Batch Processing API on port {self.port}")
        self.app.run(host='0.0.0.0', port=self.port)

def main():
    """Run the Batch Processing API"""
    api = BatchProcessingAPI()
    api.start()

if __name__ == "__main__":
    main()
'''
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(api_code)

def add_health_endpoint(api_file, api_name, api_info):
    """为API文件添加健康检查端点"""
    if not os.path.exists(api_file):
        print(f"错误: 找不到文件 {api_file}")
        return False
        
    with open(api_file, 'r', encoding='utf-8') as f:
        content = f.read()
        
    # 检查是否已有健康检查端点
    if api_info["route"] in content:
        print(f"{api_name} 已有健康检查端点")
        return True
        
    # 查找_setup_routes方法
    route_pattern = r'def _setup_routes\(self.*?\):\s*""".*?"""'
    route_match = re.search(route_pattern, content, re.DOTALL)
    
    if not route_match:
        print(f"错误: 在 {api_name} 中找不到 _setup_routes 方法")
        return False
        
    # 在_setup_routes方法后插入健康检查端点
    insert_position = route_match.end()
    health_check_code = HEALTH_CHECK_TEMPLATE.format(
        route=api_info["route"],
        name=api_info["name"],
        port=api_info["port"]
    )
    
    modified_content = content[:insert_position] + health_check_code + content[insert_position:]
    
    # 备份原文件
    backup_file = api_file + '.bak'
    if not os.path.exists(backup_file):
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(content)
            
    # 写入修改后的内容
    with open(api_file, 'w', encoding='utf-8') as f:
        f.write(modified_content)
        
    print(f"已为 {api_name} 添加健康检查端点: {api_info['route']}")
    return True

def start_batch_api_service():
    """创建并启动Batch API服务的批处理脚本"""
    batch_dir = os.path.dirname(os.path.abspath(__file__))
    batch_script = os.path.join(os.path.dirname(batch_dir), "start_batch_api.bat")
    
    script_content = f'''@echo off
echo Starting Batch Processing API on port 5007...
cd "{batch_dir}"
start "Batch Processing API" cmd /k python batch_processing_api.py
'''
    
    with open(batch_script, 'w', encoding='utf-8') as f:
        f.write(script_content)
        
    print(f"已创建Batch API启动脚本: {batch_script}")
    os.system(f"start {batch_script}")

def main():
    """主函数"""
    print("API健康检查端点修复工具")
    print("="*40)
    
    # 获取脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 查找API文件
    api_files = find_api_files(current_dir)
    
    # 添加健康检查端点
    fixed_apis = []
    for api_name, api_file in api_files.items():
        if api_name in API_HEALTH_ENDPOINTS:
            if add_health_endpoint(api_file, api_name, API_HEALTH_ENDPOINTS[api_name]):
                fixed_apis.append(api_name)
    
    print(f"\n已修复 {len(fixed_apis)} 个API的健康检查端点:")
    for api in fixed_apis:
        print(f"- {api}")
    
    # 启动Batch API服务
    if "batch_processing_api.py" in api_files:
        start_batch_api_service()
    
    print("\n修复完成! 请重启所有API服务以应用更改。")
    print("="*40)

if __name__ == "__main__":
    main()
