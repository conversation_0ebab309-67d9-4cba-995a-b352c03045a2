#!/usr/bin/env python3
"""
Advanced Analytics API for AI Policy Analyzer
Provides Google-level data science endpoints

Author: <PERSON> Code  
Date: August 1, 2025
"""

import json
import os
import sys
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add backend path for imports
sys.path.append(os.path.dirname(__file__))

try:
    from flask import Flask, jsonify, request
    from flask_cors import CORS
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False
    print("Flask not available, using mock API mode")

from advanced_analytics_engine import AdvancedAnalyticsEngine, MLInsight

class AdvancedAnalyticsAPI:
    """Advanced Analytics API handler with Google-level capabilities"""
    
    def __init__(self):
        self.analytics_engine = AdvancedAnalyticsEngine()
        
        if FLASK_AVAILABLE:
            self.app = Flask(__name__)
            CORS(self.app)
            self._setup_routes()
        else:
            self.app = None
    
    def _setup_routes(self):
        """Setup Flask routes for advanced analytics"""
        
        @self.app.route('/api/analytics/health', methods=['GET'])
        def health_check():
            return jsonify({
                'status': 'healthy',
                'service': 'Advanced Analytics API',
                'version': '2.0',
                'capabilities': [
                    'ML Insights',
                    'Predictive Analytics', 
                    'Network Analysis',
                    'Policy Simulation',
                    'Real-time Monitoring'
                ]
            })
        
        @self.app.route('/api/analytics/ml-insights', methods=['GET'])
        def get_ml_insights():
            """Get machine learning driven insights"""
            try:
                insights = self.analytics_engine.generate_ml_insights()
                
                # Convert MLInsight objects to dictionaries
                insights_data = []
                for insight in insights:
                    insights_data.append({
                        'type': insight.type,
                        'confidence': insight.confidence,
                        'title': insight.title,
                        'description': insight.description,
                        'impact_score': insight.impact_score,
                        'data': insight.data,
                        'timestamp': insight.timestamp.isoformat()
                    })
                
                return jsonify({
                    'success': True,
                    'insights': insights_data,
                    'generated_at': datetime.now().isoformat()
                })
                
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': f'Failed to generate ML insights: {str(e)}'
                }), 500
        
        @self.app.route('/api/analytics/predictions', methods=['GET'])
        def get_predictions():
            """Get predictive analytics results"""
            try:
                timeframe = request.args.get('timeframe_months', 12, type=int)
                predictions = self.analytics_engine.generate_predictions(timeframe)
                
                return jsonify({
                    'success': True,
                    'predictions': predictions
                })
                
            except Exception as e:
                # Log the actual error for debugging but don't expose details
                print(f"Predictions Error: {str(e)}")
                return jsonify({
                    'success': False,
                    'error': 'Failed to generate predictions. Please try again later.'
                }), 500
        
        @self.app.route('/api/analytics/network', methods=['GET'])
        def get_network_analysis():
            """Get network analysis results"""
            try:
                network_data = self.analytics_engine.analyze_network_influence()
                
                return jsonify({
                    'success': True,
                    'network_analysis': network_data
                })
                
            except Exception as e:
                # Log the actual error for debugging but don't expose details
                print(f"Network Analysis Error: {str(e)}")
                return jsonify({
                    'success': False,
                    'error': 'Failed to perform network analysis. Please try again later.'
                }), 500
        
        @self.app.route('/api/analytics/simulate', methods=['POST'])
        def run_policy_simulation():
            """Run policy impact simulation"""
            try:
                data = request.get_json()
                
                if not data:
                    return jsonify({
                        'success': False,
                        'error': 'Missing simulation parameters'
                    }), 400
                
                policy_change = data.get('policy_change', 'status_quo')
                affected_sectors = data.get('affected_sectors', ['Technology'])
                
                simulation_results = self.analytics_engine.run_policy_simulation(
                    policy_change, affected_sectors
                )
                
                return jsonify({
                    'success': True,
                    'simulation': simulation_results
                })
                
            except Exception as e:
                # Log the actual error for debugging but don't expose details
                print(f"Policy Simulation Error: {str(e)}")
                return jsonify({
                    'success': False,
                    'error': 'Failed to run policy simulation. Please check your parameters and try again.'
                }), 500
        
        @self.app.route('/api/analytics/realtime', methods=['GET'])
        def get_realtime_metrics():
            """Get real-time monitoring metrics"""
            try:
                metrics = self.analytics_engine.get_realtime_metrics()
                
                return jsonify({
                    'success': True,
                    'metrics': metrics
                })
                
            except Exception as e:
                # Log the actual error for debugging but don't expose details
                print(f"Real-time Metrics Error: {str(e)}")
                return jsonify({
                    'success': False,
                    'error': 'Failed to retrieve real-time metrics. Please try again later.'
                }), 500
        
        @self.app.route('/api/analytics/sentiment-analysis', methods=['GET'])
        def get_advanced_sentiment_analysis():
            """Get advanced sentiment analysis with multiple dimensions"""
            try:
                time_range = request.args.get('time_range', '90d')
                org_type = request.args.get('org_type', 'all')
                model_type = request.args.get('model', 'bert')
                
                # Mock advanced sentiment analysis
                sentiment_data = {
                    'emotional_dimensions': {
                        'joy': 42,
                        'trust': 31,
                        'fear': 18,
                        'anger': 9
                    },
                    'sector_breakdown': {
                        'tech': {'positive': 85, 'neutral': 10, 'negative': 5},
                        'finance': {'positive': 72, 'neutral': 20, 'negative': 8},
                        'healthcare': {'positive': 68, 'neutral': 25, 'negative': 7},
                        'academic': {'positive': 79, 'neutral': 15, 'negative': 6},
                        'government': {'positive': 45, 'neutral': 35, 'negative': 20}
                    },
                    'temporal_patterns': {
                        'trend': 'improving',
                        'volatility': 'low',
                        'seasonality': None
                    },
                    'model_confidence': 0.89,
                    'analysis_parameters': {
                        'time_range': time_range,
                        'organization_type': org_type,
                        'model': model_type
                    }
                }
                
                return jsonify({
                    'success': True,
                    'sentiment_analysis': sentiment_data,
                    'generated_at': datetime.now().isoformat()
                })
                
            except Exception as e:
                # Log the actual error for debugging but don't expose details
                print(f"Sentiment Analysis Error: {str(e)}")
                return jsonify({
                    'success': False,
                    'error': 'Failed to perform sentiment analysis. Please try again later.'
                }), 500
        
        @self.app.route('/api/analytics/topic-modeling', methods=['GET'])
        def get_topic_modeling():
            """Get topic modeling results"""
            try:
                # Mock topic modeling results
                topics = {
                    'topics': [
                        {
                            'id': 'ai_safety',
                            'name': 'AI Safety',
                            'coverage': 32,
                            'keywords': ['safety', 'risk', 'alignment', 'control', 'robust'],
                            'representative_docs': 847
                        },
                        {
                            'id': 'regulation',
                            'name': 'Regulation',
                            'coverage': 28,
                            'keywords': ['policy', 'governance', 'compliance', 'oversight', 'standards'],
                            'representative_docs': 731
                        },
                        {
                            'id': 'innovation',
                            'name': 'Innovation',
                            'coverage': 24,
                            'keywords': ['development', 'research', 'breakthrough', 'advancement', 'future'],
                            'representative_docs': 623
                        },
                        {
                            'id': 'ethics',
                            'name': 'Ethics',
                            'coverage': 16,
                            'keywords': ['ethical', 'moral', 'responsibility', 'fairness', 'bias'],
                            'representative_docs': 412
                        }
                    ],
                    'coherence_score': 0.73,
                    'model_type': 'LDA',
                    'num_documents': 2613,
                    'generated_at': datetime.now().isoformat()
                }
                
                return jsonify({
                    'success': True,
                    'topic_modeling': topics
                })
                
            except Exception as e:
                # Log the actual error for debugging but don't expose details
                print(f"Topic Modeling Error: {str(e)}")
                return jsonify({
                    'success': False,
                    'error': 'Failed to perform topic modeling. Please try again later.'
                }), 500

    def run_server(self, host='localhost', port=None, debug=False):
        """Run the Flask server"""
        if port is None:
            from port_config import get_port
            port = get_port('analytics_api')
        if self.app:
            print(f"🚀 Starting Advanced Analytics API server on http://{host}:{port}")
            self.app.run(host=host, port=port, debug=debug)
        else:
            print("❌ Flask not available - cannot start server")

    def get_mock_response(self, endpoint: str, method: str = 'GET', data: dict = None) -> dict:
        """Get mock API responses when Flask is not available"""
        
        if endpoint == '/api/analytics/health':
            return {
                'status': 'healthy',
                'service': 'Advanced Analytics API (Mock)',
                'version': '2.0',
                'capabilities': ['ML Insights', 'Predictions', 'Network Analysis', 'Simulation', 'Real-time']
            }
        
        elif endpoint == '/api/analytics/ml-insights':
            insights = self.analytics_engine.generate_ml_insights()
            return {
                'success': True,
                'insights': [
                    {
                        'type': insight.type,
                        'confidence': insight.confidence,
                        'title': insight.title,
                        'description': insight.description,
                        'impact_score': insight.impact_score,
                        'data': insight.data,
                        'timestamp': insight.timestamp.isoformat()
                    } for insight in insights
                ],
                'generated_at': datetime.now().isoformat()
            }
        
        elif endpoint == '/api/analytics/predictions':
            return {
                'success': True,
                'predictions': self.analytics_engine.generate_predictions()
            }
        
        elif endpoint == '/api/analytics/network':
            return {
                'success': True,
                'network_analysis': self.analytics_engine.analyze_network_influence()
            }
        
        elif endpoint == '/api/analytics/realtime':
            return {
                'success': True,
                'metrics': self.analytics_engine.get_realtime_metrics()
            }
        
        else:
            return {'error': f'Mock endpoint not implemented: {endpoint}'}


def main():
    """Main function to start the Advanced Analytics API server"""
    
    print("🧠 AI Policy Analyzer - Advanced Analytics API")
    print("=" * 60)
    
    # Initialize API
    api = AdvancedAnalyticsAPI()
    
    # Test analytics engine
    print("🔬 Testing analytics engine...")
    insights = api.analytics_engine.generate_ml_insights()
    print(f"   Generated {len(insights)} ML insights")
    
    predictions = api.analytics_engine.generate_predictions()
    print(f"   Generated predictions for {len(predictions['predictions'])} policy types")
    
    network = api.analytics_engine.analyze_network_influence()
    print(f"   Analyzed network with {len(network['influence_rankings'])} organizations")
    
    if FLASK_AVAILABLE:
        print(f"\n🌐 Advanced Analytics API Endpoints:")
        print(f"   Health: http://localhost:5005/api/analytics/health")
        print(f"   ML Insights: http://localhost:5005/api/analytics/ml-insights")
        print(f"   Predictions: http://localhost:5005/api/analytics/predictions")
        print(f"   Network Analysis: http://localhost:5005/api/analytics/network")
        print(f"   Policy Simulation: POST http://localhost:5005/api/analytics/simulate")
        print(f"   Real-time Metrics: http://localhost:5005/api/analytics/realtime")
        print(f"   Sentiment Analysis: http://localhost:5005/api/analytics/sentiment-analysis")
        print(f"   Topic Modeling: http://localhost:5005/api/analytics/topic-modeling")
        
        # Start server
        api.run_server(host='0.0.0.0', port=5005)
    else:
        print(f"\n⚠️ Flask not available - API running in mock mode")
        
        # Test mock responses
        print(f"\n🧪 Testing mock API responses:")
        health = api.get_mock_response('/api/analytics/health')
        print(f"   Health: {health['status']}")
        
        insights_response = api.get_mock_response('/api/analytics/ml-insights')
        print(f"   Insights: {len(insights_response['insights'])} generated")


if __name__ == "__main__":
    main()