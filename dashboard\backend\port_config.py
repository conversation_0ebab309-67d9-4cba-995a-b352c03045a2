"""
AI Policy Analyzer - Port Configuration
Standardizes port assignments across all services to prevent conflicts
"""

# API Service Port Configuration
PORTS = {
    'frontend': 8028,
    'visualization_api': 5001,
    'search_api': 5002,
    'historical_api': 5003,
    'analysis_api': 5004,
    'analytics_api': 5005,
    'batch_api': 5006,
    'enhanced_batch_api': 5007,
    'analysis_details_api': 5008
}

# Service Names
SERVICES = {
    'frontend': 'Frontend Server',
    'visualization_api': 'Visualization API',
    'search_api': 'Search API',
    'historical_api': 'Historical Data API',
    'analysis_api': 'Analysis Results API',
    'analytics_api': 'Advanced Analytics API',
    'batch_api': 'Batch Processing API',
    'enhanced_batch_api': 'Enhanced Batch Processor',
    'analysis_details_api': 'Analysis Details API'
}

def get_port(service_name):
    """Get port for a specific service"""
    return PORTS.get(service_name, 5000)

def get_service_name(service_name):
    """Get display name for a specific service"""
    return SERVICES.get(service_name, 'Unknown Service')

def validate_ports():
    """Check for port conflicts"""
    port_values = list(PORTS.values())
    if len(port_values) != len(set(port_values)):
        conflicts = []
        for port in port_values:
            if port_values.count(port) > 1:
                conflicts.append(port)
        raise ValueError(f"Port conflicts detected: {conflicts}")
    return True

if __name__ == "__main__":
    validate_ports()
    print("Port configuration is valid - no conflicts detected")
    print("Current port assignments:")
    for service, port in PORTS.items():
        print(f"  {service}: {port} ({SERVICES.get(service, 'Unknown')})")