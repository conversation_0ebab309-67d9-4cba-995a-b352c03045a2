#!/usr/bin/env python3
"""
重启后端服务器脚本
修复SQL语法错误后重新启动
"""

import subprocess
import sys
import time
import requests

def check_backend_running():
    """检查后端是否在运行"""
    try:
        response = requests.get("http://localhost:5001/api/system/status", timeout=2)
        return response.status_code == 200
    except:
        return False

def test_backend_functionality():
    """测试后端功能"""
    print("🧪 测试后端功能...")
    
    base_url = "http://localhost:5001"
    
    # 测试系统状态
    try:
        response = requests.get(f"{base_url}/api/system/status")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 系统状态: {data.get('total_documents', 0)} 个文档已索引")
        else:
            print(f"⚠️ 系统状态检查失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 系统状态检查错误: {e}")
    
    # 测试文档列表
    try:
        response = requests.get(f"{base_url}/api/documents/list?per_page=5")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 文档列表: 找到 {data.get('pagination', {}).get('total', 0)} 个文档")
        else:
            print(f"⚠️ 文档列表检查失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 文档列表检查错误: {e}")
    
    # 测试分析摘要
    try:
        response = requests.get(f"{base_url}/api/analytics/summary")
        if response.status_code == 200:
            data = response.json()
            orgs = len(data.get('top_organizations', []))
            print(f"✅ 分析摘要: {orgs} 个组织统计")
        else:
            print(f"⚠️ 分析摘要检查失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 分析摘要检查错误: {e}")
    
    # 测试处理启动 (小批量)
    try:
        response = requests.post(f"{base_url}/api/processing/start", 
                               json={"priority": 1, "limit": 5})
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 处理启动: {data.get('documents_added', 0)} 个文档已加入处理队列")
        else:
            print(f"⚠️ 处理启动失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
    except Exception as e:
        print(f"❌ 处理启动错误: {e}")

def main():
    """主函数"""
    print("🔄 后端重启和测试工具")
    print("=" * 50)
    
    # 检查当前状态
    if check_backend_running():
        print("✅ 后端服务器正在运行")
        print("📊 当前状态良好，进行功能测试...")
        test_backend_functionality()
    else:
        print("❌ 后端服务器未运行")
        print("💡 请在另一个终端运行: python simple_backend_start.py")
        return
    
    print("\n" + "=" * 50)
    print("🎉 后端测试完成!")
    print("\n💡 下一步操作:")
    print("1. 启动前端服务器:")
    print("   python -m http.server 8000")
    print("\n2. 访问Dashboard:")
    print("   🌐 主Dashboard: http://localhost:8000/dashboard/frontend/index.html")
    print("   🖥️ 后端管理: http://localhost:8000/backend_management_dashboard.html")
    print("   🧪 测试套件: http://localhost:8000/comprehensive_test_suite.html")
    print("\n3. 或者运行完整启动脚本:")
    print("   python start_unified_system.py --backend-only")

if __name__ == "__main__":
    main()
