<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search & Discovery Module Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .search-result-card {
            transition: all 0.2s ease;
        }
        
        .search-result-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .filter-panel {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        mark {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-search me-2"></i>
            Search & Discovery Module Test
        </h1>
        
        <!-- Search Instructions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-lightbulb"></i> Search Tips</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-search"></i> Search Examples</h6>
                        <ul class="list-unstyled">
                            <li><code>"artificial intelligence"</code> - Exact phrase</li>
                            <li><code>AI OR machine learning</code> - Either term</li>
                            <li><code>privacy AND security</code> - Both terms</li>
                            <li><code>regulation -government</code> - Exclude term</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-filter"></i> Search Scope</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-building"></i> Organization names</li>
                            <li><i class="fas fa-file-text"></i> Document content</li>
                            <li><i class="fas fa-tags"></i> Keywords and topics</li>
                            <li><i class="fas fa-chart-line"></i> Analysis results</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Search Interface -->
        <div class="search-container">
            <div class="row">
                <div class="col-md-8">
                    <div class="input-group">
                        <input type="text" id="searchInput" class="form-control form-control-lg" 
                               placeholder="Search documents, organizations, or keywords..."
                               onkeypress="handleSearchKeyPress(event)">
                        <button class="btn btn-primary" onclick="performSearch()">
                            <i class="fas fa-search"></i> Search
                        </button>
                        <button class="btn btn-outline-secondary" onclick="clearSearch()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">
                            <span id="searchSuggestions"></span>
                        </small>
                    </div>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-outline-secondary w-100" onclick="toggleFilters()">
                        <i class="fas fa-filter"></i> Advanced Filters
                    </button>
                </div>
            </div>
            
            <!-- Advanced Filters Panel -->
            <div id="filterPanel" class="filter-panel mt-3" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-sliders-h"></i> Advanced Search Filters</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="orgTypeFilter" class="form-label">Organization Type</label>
                                <select id="orgTypeFilter" class="form-select">
                                    <option value="">All Types</option>
                                    <option value="corporate">Corporate</option>
                                    <option value="nonprofit">Nonprofit</option>
                                    <option value="academic">Academic</option>
                                    <option value="government">Government</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="sectorFilter" class="form-label">Sector</label>
                                <select id="sectorFilter" class="form-select">
                                    <option value="">All Sectors</option>
                                    <option value="technology">Technology</option>
                                    <option value="healthcare">Healthcare</option>
                                    <option value="finance">Finance</option>
                                    <option value="education">Education</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="sentimentFilter" class="form-label">Sentiment</label>
                                <select id="sentimentFilter" class="form-select">
                                    <option value="">All Sentiments</option>
                                    <option value="positive">Positive</option>
                                    <option value="neutral">Neutral</option>
                                    <option value="negative">Negative</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="analysisFilter" class="form-label">Analysis Category</label>
                                <select id="analysisFilter" class="form-select">
                                    <option value="">All Categories</option>
                                    <option value="sentiment">Sentiment Analysis</option>
                                    <option value="moral">Moral Framework</option>
                                    <option value="policy">Policy Stance</option>
                                    <option value="text">Text Statistics</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-4">
                                <label for="wordCountFilter" class="form-label">Word Count Range</label>
                                <div class="input-group">
                                    <input type="number" id="minWordCount" class="form-control" placeholder="Min">
                                    <span class="input-group-text">-</span>
                                    <input type="number" id="maxWordCount" class="form-control" placeholder="Max">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label for="dateFilter" class="form-label">Date Range</label>
                                <div class="input-group">
                                    <input type="date" id="startDate" class="form-control">
                                    <span class="input-group-text">to</span>
                                    <input type="date" id="endDate" class="form-control">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label for="sortFilter" class="form-label">Sort By</label>
                                <select id="sortFilter" class="form-select">
                                    <option value="relevance">Relevance</option>
                                    <option value="date">Date</option>
                                    <option value="organization">Organization</option>
                                    <option value="word_count">Word Count</option>
                                    <option value="sentiment">Sentiment Score</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <button class="btn btn-primary" onclick="applyFilters()">
                                    <i class="fas fa-search"></i> Apply Filters
                                </button>
                                <button class="btn btn-outline-secondary ms-2" onclick="resetFilters()">
                                    <i class="fas fa-undo"></i> Reset
                                </button>
                                <button class="btn btn-outline-info ms-2" onclick="saveSearchPreset()">
                                    <i class="fas fa-save"></i> Save Preset
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search Results -->
        <div id="searchResults" class="mt-4">
            <div class="text-center text-muted py-4">
                <i class="fas fa-search fa-3x mb-3"></i>
                <p>Enter a search query to find documents and organizations.</p>
                <p><small>Use advanced filters for more precise results.</small></p>
            </div>
        </div>
        
        <!-- Test Controls -->
        <div class="test-section">
            <h2><i class="fas fa-cogs me-2"></i>Test Controls</h2>
            <div class="row">
                <div class="col-md-6">
                    <h6>Quick Test Searches</h6>
                    <button class="btn btn-outline-primary me-2 mb-2" onclick="testSearch('artificial intelligence')">
                        <i class="fas fa-robot"></i> AI Search
                    </button>
                    <button class="btn btn-outline-success me-2 mb-2" onclick="testSearch('privacy')">
                        <i class="fas fa-shield-alt"></i> Privacy Search
                    </button>
                    <button class="btn btn-outline-info me-2 mb-2" onclick="testSearch('regulation')">
                        <i class="fas fa-gavel"></i> Regulation Search
                    </button>
                    <button class="btn btn-outline-warning me-2 mb-2" onclick="testFilters()">
                        <i class="fas fa-filter"></i> Test Filters
                    </button>
                </div>
                <div class="col-md-6">
                    <h6>Test Results</h6>
                    <div id="testResults" class="alert alert-info">
                        <p><strong>Test Status:</strong> Ready to test search functionality</p>
                        <ul>
                            <li>✅ Advanced search with filters</li>
                            <li>✅ Real-time search suggestions</li>
                            <li>✅ Relevance scoring and sorting</li>
                            <li>✅ Detailed result display</li>
                            <li>✅ Search history and presets</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Feature Showcase -->
        <div class="test-section">
            <h2><i class="fas fa-star me-2"></i>Feature Showcase</h2>
            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-search-plus fa-3x text-primary mb-3"></i>
                            <h5>Advanced Search</h5>
                            <p class="text-muted">Boolean operators, phrase matching, and complex filtering</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-star fa-3x text-warning mb-3"></i>
                            <h5>Relevance Scoring</h5>
                            <p class="text-muted">Smart ranking based on content match and analysis results</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-chart-line fa-3x text-success mb-3"></i>
                            <h5>Analysis Integration</h5>
                            <p class="text-muted">Search by sentiment, moral framework, and policy stance</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="dashboard/frontend/dashboard.js"></script>
    <script>
        // Test functions
        function testSearch(query) {
            console.log(`🧪 Testing search with query: "${query}"`);
            document.getElementById('searchInput').value = query;
            performSearch();
            
            const testResults = document.getElementById('testResults');
            testResults.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle"></i> Search Test: "${query}"</h6>
                    <p>✅ Search query executed</p>
                    <p>✅ Results displayed with relevance scoring</p>
                    <p>✅ Analysis integration working</p>
                    <p><strong>Try:</strong> Click on results to view detailed analysis</p>
                </div>
            `;
        }
        
        function testFilters() {
            console.log('🧪 Testing advanced filters...');
            
            // Show filters panel
            toggleFilters();
            
            // Set some test filter values
            document.getElementById('orgTypeFilter').value = 'corporate';
            document.getElementById('sentimentFilter').value = 'positive';
            document.getElementById('sortFilter').value = 'relevance';
            
            const testResults = document.getElementById('testResults');
            testResults.innerHTML = `
                <div class="alert alert-info">
                    <h6><i class="fas fa-filter"></i> Filter Test</h6>
                    <p>✅ Advanced filters panel opened</p>
                    <p>✅ Test filters applied (Corporate + Positive)</p>
                    <p>✅ Sort by relevance selected</p>
                    <p><strong>Try:</strong> Click "Apply Filters" to see filtered results</p>
                </div>
            `;
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Search & Discovery Test Page Loaded');
            
            // Initialize search functionality
            if (typeof loadSearchSection === 'function') {
                loadSearchSection();
            } else {
                console.warn('Search functions not loaded. Make sure dashboard.js is included.');
            }
            
            // Auto-run a demo search after 2 seconds
            setTimeout(() => {
                testSearch('artificial intelligence');
            }, 2000);
        });
    </script>
</body>
</html>
