#!/usr/bin/env python3
"""
简化的后端启动脚本
解决启动问题并提供更好的错误诊断
"""

import os
import sys
import json
import sqlite3
import logging
from pathlib import Path
from flask import Flask, jsonify, request
from flask_cors import CORS
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
CORS(app)

# 全局变量
DB_PATH = "simple_backend.db"
DATA_DIR = Path("data/90_FR_9088_pdfs")

def initialize_database():
    """初始化数据库"""
    logger.info("初始化数据库...")
    
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # 创建简单的文档表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS documents (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                filename TEXT UNIQUE NOT NULL,
                organization TEXT,
                file_path TEXT,
                file_size INTEGER,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'pending'
            )
        ''')
        
        # 创建系统状态表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_status (
                id INTEGER PRIMARY KEY,
                total_documents INTEGER DEFAULT 0,
                processed_documents INTEGER DEFAULT 0,
                processing_documents INTEGER DEFAULT 0,
                queued_documents INTEGER DEFAULT 0,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 插入初始状态
        cursor.execute('''
            INSERT OR REPLACE INTO system_status (id, total_documents, processed_documents, processing_documents, queued_documents)
            VALUES (1, 0, 0, 0, 0)
        ''')
        
        conn.commit()
        conn.close()
        
        logger.info("✅ 数据库初始化完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库初始化失败: {e}")
        return False

def scan_documents():
    """扫描文档目录"""
    logger.info("扫描文档目录...")
    
    results = {
        'total_files': 0,
        'new_files': 0,
        'existing_files': 0,
        'errors': [],
        'scan_time': datetime.now().isoformat()
    }
    
    try:
        if not DATA_DIR.exists():
            logger.warning(f"数据目录不存在: {DATA_DIR}")
            results['errors'].append(f"数据目录不存在: {DATA_DIR}")
            return results
        
        pdf_files = list(DATA_DIR.glob('*.pdf'))
        results['total_files'] = len(pdf_files)
        
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        for pdf_file in pdf_files:
            try:
                # 检查文件是否已存在
                cursor.execute('SELECT id FROM documents WHERE filename = ?', (pdf_file.name,))
                existing = cursor.fetchone()
                
                if not existing:
                    # 解析组织名
                    org_name = pdf_file.name.split('-')[0] if '-' in pdf_file.name else 'Unknown'
                    
                    # 插入新文档
                    cursor.execute('''
                        INSERT INTO documents (filename, organization, file_path, file_size)
                        VALUES (?, ?, ?, ?)
                    ''', (
                        pdf_file.name,
                        org_name,
                        str(pdf_file),
                        pdf_file.stat().st_size
                    ))
                    
                    results['new_files'] += 1
                else:
                    results['existing_files'] += 1
                    
            except Exception as e:
                error_msg = f"处理文件 {pdf_file.name} 时出错: {e}"
                results['errors'].append(error_msg)
                logger.error(error_msg)
        
        # 更新系统状态
        cursor.execute('SELECT COUNT(*) FROM documents')
        total_docs = cursor.fetchone()[0]
        
        cursor.execute('''
            UPDATE system_status 
            SET total_documents = ?, last_updated = CURRENT_TIMESTAMP 
            WHERE id = 1
        ''', (total_docs,))
        
        conn.commit()
        conn.close()
        
        logger.info(f"✅ 扫描完成: {results['total_files']} 总文件, {results['new_files']} 新文件")
        
    except Exception as e:
        error_msg = f"扫描过程出错: {e}"
        results['errors'].append(error_msg)
        logger.error(error_msg)
    
    return results

# API路由
@app.route('/api/system/status', methods=['GET'])
def get_system_status():
    """获取系统状态"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM system_status WHERE id = 1')
        status_row = cursor.fetchone()
        
        if status_row:
            status = {
                'total_documents': status_row[1],
                'processed_documents': status_row[2],
                'processing_documents': status_row[3],
                'queued_documents': status_row[4],
                'processing_rate': round((status_row[2] / status_row[1] * 100), 2) if status_row[1] > 0 else 0,
                'system_status': 'running',
                'last_updated': status_row[5] or datetime.now().isoformat()
            }
        else:
            status = {
                'total_documents': 0,
                'processed_documents': 0,
                'processing_documents': 0,
                'queued_documents': 0,
                'processing_rate': 0,
                'system_status': 'running',
                'last_updated': datetime.now().isoformat()
            }
        
        conn.close()
        return jsonify(status)
        
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/documents/scan', methods=['POST'])
def scan_documents_api():
    """扫描文档API"""
    try:
        results = scan_documents()
        return jsonify(results)
    except Exception as e:
        logger.error(f"扫描文档失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/documents/list', methods=['GET'])
def list_documents():
    """列出文档"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # 获取总数
        cursor.execute('SELECT COUNT(*) FROM documents')
        total = cursor.fetchone()[0]
        
        # 获取分页数据
        offset = (page - 1) * per_page
        cursor.execute('''
            SELECT id, filename, organization, file_size, created_date, status
            FROM documents
            ORDER BY created_date DESC
            LIMIT ? OFFSET ?
        ''', (per_page, offset))
        
        documents = []
        for row in cursor.fetchall():
            documents.append({
                'id': row[0],
                'filename': row[1],
                'organization': row[2],
                'file_size': row[3],
                'created_date': row[4],
                'status': row[5]
            })
        
        conn.close()
        
        return jsonify({
            'documents': documents,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total,
                'pages': (total + per_page - 1) // per_page
            }
        })
        
    except Exception as e:
        logger.error(f"列出文档失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/analytics/summary', methods=['GET'])
def get_analytics_summary():
    """获取分析摘要"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # 获取组织统计
        cursor.execute('''
            SELECT organization, COUNT(*) as doc_count
            FROM documents 
            GROUP BY organization
            ORDER BY doc_count DESC
            LIMIT 10
        ''')
        top_organizations = [{'organization': row[0], 'document_count': row[1]} for row in cursor.fetchall()]
        
        # 获取状态统计
        cursor.execute('''
            SELECT status, COUNT(*) as count
            FROM documents
            GROUP BY status
        ''')
        status_stats = {row[0]: row[1] for row in cursor.fetchall()}
        
        conn.close()
        
        return jsonify({
            'top_organizations': top_organizations,
            'status_statistics': status_stats,
            'generated_at': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"获取分析摘要失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/processing/start', methods=['POST'])
def start_processing():
    """启动处理"""
    try:
        data = request.get_json() or {}
        priority = data.get('priority', 1)
        limit = data.get('limit', 10)
        
        # 模拟启动处理
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # 获取待处理文档数量
        cursor.execute('SELECT COUNT(*) FROM documents WHERE status = "pending"')
        pending_count = cursor.fetchone()[0]
        
        # 更新处理状态 (SQLite不支持UPDATE with LIMIT，需要使用子查询)
        actual_limit = min(limit, pending_count)
        cursor.execute('''
            UPDATE documents
            SET status = "processing"
            WHERE id IN (
                SELECT id FROM documents
                WHERE status = "pending"
                LIMIT ?
            )
        ''', (actual_limit,))
        
        # 更新系统状态
        cursor.execute('''
            UPDATE system_status 
            SET processing_documents = ?, last_updated = CURRENT_TIMESTAMP 
            WHERE id = 1
        ''', (actual_limit,))
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'message': f'已启动处理 {actual_limit} 个文档',
            'documents_added': actual_limit,
            'priority': priority,
            'started_at': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"启动处理失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/processing/queue', methods=['GET'])
def get_processing_queue():
    """获取处理队列"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, filename, organization, status, created_date
            FROM documents
            WHERE status IN ("processing", "pending")
            ORDER BY created_date ASC
            LIMIT 50
        ''')
        
        queue_items = []
        for row in cursor.fetchall():
            queue_items.append({
                'document_id': row[0],
                'filename': row[1],
                'organization': row[2],
                'status': row[3],
                'created_date': row[4]
            })
        
        conn.close()
        
        return jsonify({
            'queue_items': queue_items,
            'total_items': len(queue_items)
        })
        
    except Exception as e:
        logger.error(f"获取处理队列失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    })

def main():
    """主函数"""
    print("🚀 启动简化后端数据处理系统...")
    
    # 初始化数据库
    if not initialize_database():
        print("❌ 数据库初始化失败")
        sys.exit(1)
    
    # 检查数据目录
    if DATA_DIR.exists():
        pdf_count = len(list(DATA_DIR.glob('*.pdf')))
        print(f"📁 找到数据目录: {DATA_DIR} ({pdf_count} PDF文件)")
    else:
        print(f"⚠️ 数据目录不存在: {DATA_DIR}")
    
    print("✅ 系统初始化完成")
    print("🌐 启动Flask服务器...")
    print("📡 API端点:")
    print("   GET  /api/system/status")
    print("   POST /api/documents/scan")
    print("   GET  /api/documents/list")
    print("   GET  /api/analytics/summary")
    print("   POST /api/processing/start")
    print("   GET  /api/processing/queue")
    print("   GET  /health")
    print("\n🎯 服务器将在 http://localhost:5001 启动")
    
    # 启动Flask应用
    try:
        app.run(host='0.0.0.0', port=5001, debug=False)
    except Exception as e:
        logger.error(f"❌ 服务器启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
