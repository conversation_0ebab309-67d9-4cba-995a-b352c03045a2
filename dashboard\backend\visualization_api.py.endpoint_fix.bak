#!/usr/bin/env python3
"""
Data Visualization API for AI Policy Analyzer Dashboard
Transforms policy narrative analysis results into Chart.js compatible visualization data

Author: Claude Code
Date: July 31, 2025
"""

import json
from typing import Dict, List, Any, Optional
from flask import Flask, jsonify, request
from flask_cors import CORS
import sys
from pathlib import Path

# Add parent directories to path for imports using pathlib
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent
sys.path.append(str(project_root))
sys.path.append(str(current_dir.parent))

app = Flask(__name__)
CORS(app)

class VisualizationDataTransformer:
    """Transforms policy narrative analysis results into visualization-ready formats"""
    
    def __init__(self):
        self.color_schemes = {
            'sentiment': {
                'positive': '#4CAF50',
                'negative': '#F44336', 
                'neutral': '#9E9E9E'
            },
            'moral_framing': {
                'core_values': '#2196F3',
                'fairness_justice': '#FF9800',
                'responsibility_accountability': '#9C27B0',
                'transparency_trust': '#00BCD4',
                'harm_protection': '#4CAF50',
                'rights_freedoms': '#E91E63'
            },
            'policy_preferences': {
                'self_regulation': '#4CAF50',
                'government_oversight': '#F44336',
                'co_regulation': '#FF9800',
                'international_coordination': '#2196F3'
            }
        }
    
    def transform_sentiment_data(self, analysis_results: Dict) -> Dict:
        """Transform sentiment analysis data for Chart.js"""
        sentiment_data = analysis_results.get('sentiment_tone', {})
        sentiment_scores = sentiment_data.get('sentiment_scores', {})
        tone_analysis = sentiment_data.get('tone_analysis', {})
        
        return {
            'sentiment_distribution': {
                'type': 'pie',
                'data': {
                    'labels': list(sentiment_scores.keys()),
                    'datasets': [{
                        'data': list(sentiment_scores.values()),
                        'backgroundColor': [
                            self.color_schemes['sentiment'].get(key, '#757575') 
                            for key in sentiment_scores.keys()
                        ]
                    }]
                },
                'options': {
                    'responsive': True,
                    'plugins': {
                        'legend': {'position': 'bottom'},
                        'title': {'display': True, 'text': 'Sentiment Distribution'}
                    }
                }
            },
            'tone_analysis': {
                'type': 'bar',
                'data': {
                    'labels': list(tone_analysis.keys()),
                    'datasets': [{
                        'label': 'Tone Frequency',
                        'data': list(tone_analysis.values()),
                        'backgroundColor': '#2196F3',
                        'borderColor': '#1976D2',
                        'borderWidth': 1
                    }]
                },
                'options': {
                    'responsive': True,
                    'plugins': {
                        'title': {'display': True, 'text': 'Tone Analysis'}
                    },
                    'scales': {
                        'y': {'beginAtZero': True}
                    }
                }
            }
        }
    
    def transform_moral_framing_data(self, analysis_results: Dict) -> Dict:
        """Transform moral dimensions data for visualization"""
        moral_data = analysis_results.get('moral_dimensions', {})
        moral_categories = moral_data.get('moral_categories', {})
        
        # Filter out categories with zero mentions
        active_categories = {
            k: v for k, v in moral_categories.items() 
            if v.get('total_mentions', 0) > 0
        }
        
        category_names = [cat.replace('_', ' ').title() for cat in active_categories.keys()]
        category_values = [data.get('total_mentions', 0) for data in active_categories.values()]
        category_densities = [data.get('density', 0) for data in active_categories.values()]
        
        return {
            'moral_categories_distribution': {
                'type': 'doughnut',
                'data': {
                    'labels': category_names,
                    'datasets': [{
                        'data': category_values,
                        'backgroundColor': [
                            self.color_schemes['moral_framing'].get(key, '#757575')
                            for key in active_categories.keys()
                        ]
                    }]
                },
                'options': {
                    'responsive': True,
                    'plugins': {
                        'legend': {'position': 'right'},
                        'title': {'display': True, 'text': 'Moral Framing Categories'}
                    }
                }
            },
            'moral_density_comparison': {
                'type': 'radar',
                'data': {
                    'labels': category_names,
                    'datasets': [{
                        'label': 'Moral Density',
                        'data': category_densities,
                        'borderColor': '#4CAF50',
                        'backgroundColor': 'rgba(76, 175, 80, 0.2)',
                        'pointBackgroundColor': '#4CAF50'
                    }]
                },
                'options': {
                    'responsive': True,
                    'plugins': {
                        'title': {'display': True, 'text': 'Moral Framing Density'}
                    },
                    'scales': {
                        'r': {'beginAtZero': True}
                    }
                }
            }
        }
    
    def transform_policy_preferences_data(self, analysis_results: Dict) -> Dict:
        """Transform policy solutions data for visualization"""
        policy_data = analysis_results.get('policy_solutions', {})
        policy_preferences = policy_data.get('policy_preferences', {})
        solution_categories = policy_data.get('solution_categories', {})
        
        # Policy preferences chart
        pref_labels = [pref.replace('_', ' ').title() for pref in policy_preferences.keys()]
        pref_values = list(policy_preferences.values())
        
        # Solution categories analysis
        active_solutions = {
            k: v for k, v in solution_categories.items() 
            if v.get('total_mentions', 0) > 0
        }
        
        solution_labels = [cat.replace('_', ' ').title() for cat in active_solutions.keys()]
        solution_values = [data.get('total_mentions', 0) for data in active_solutions.values()]
        
        return {
            'policy_preferences': {
                'type': 'bar',
                'data': {
                    'labels': pref_labels,
                    'datasets': [{
                        'label': 'Policy Preference Frequency',
                        'data': pref_values,
                        'backgroundColor': [
                            self.color_schemes['policy_preferences'].get(key, '#757575')
                            for key in policy_preferences.keys()
                        ]
                    }]
                },
                'options': {
                    'responsive': True,
                    'plugins': {
                        'title': {'display': True, 'text': 'Policy Preferences'}
                    },
                    'scales': {
                        'y': {'beginAtZero': True}
                    }
                }
            },
            'solution_categories': {
                'type': 'horizontalBar',
                'data': {
                    'labels': solution_labels,
                    'datasets': [{
                        'label': 'Solution Mentions',
                        'data': solution_values,
                        'backgroundColor': '#FF9800',
                        'borderColor': '#F57C00',
                        'borderWidth': 1
                    }]
                },
                'options': {
                    'responsive': True,
                    'plugins': {
                        'title': {'display': True, 'text': 'Solution Categories'}
                    },
                    'scales': {
                        'x': {'beginAtZero': True}
                    }
                }
            }
        }
    
    def transform_comparative_data(self, analysis_results_list: List[Dict]) -> Dict:
        """Transform multiple analysis results for comparative visualization"""
        if not analysis_results_list:
            return {}
        
        # Extract organization names for comparison
        org_names = []
        sentiment_data = []
        moral_data = []
        policy_data = []
        
        for result in analysis_results_list:
            metadata = result.get('metadata', {})
            org_names.append(metadata.get('organization_name', 'Unknown'))
            
            # Sentiment comparison
            sentiment_scores = result.get('sentiment_tone', {}).get('sentiment_scores', {})
            sentiment_data.append(sentiment_scores.get('positive', 0))
            
            # Moral framing comparison
            moral_mentions = result.get('moral_dimensions', {}).get('total_moral_mentions', 0)
            moral_data.append(moral_mentions)
            
            # Policy solutions comparison
            solution_mentions = result.get('policy_solutions', {}).get('total_solution_mentions', 0)
            policy_data.append(solution_mentions)
        
        return {
            'organizational_comparison': {
                'type': 'line',
                'data': {
                    'labels': org_names,
                    'datasets': [
                        {
                            'label': 'Positive Sentiment',
                            'data': sentiment_data,
                            'borderColor': '#4CAF50',
                            'backgroundColor': 'rgba(76, 175, 80, 0.1)',
                            'tension': 0.1
                        },
                        {
                            'label': 'Moral Mentions',
                            'data': moral_data,
                            'borderColor': '#2196F3',
                            'backgroundColor': 'rgba(33, 150, 243, 0.1)',
                            'tension': 0.1
                        },
                        {
                            'label': 'Solution Mentions',
                            'data': policy_data,
                            'borderColor': '#FF9800',
                            'backgroundColor': 'rgba(255, 152, 0, 0.1)',
                            'tension': 0.1
                        }
                    ]
                },
                'options': {
                    'responsive': True,
                    'plugins': {
                        'title': {'display': True, 'text': 'Cross-Organization Comparison'}
                    },
                    'scales': {
                        'y': {'beginAtZero': True}
                    }
                }
            }
        }
    
    def create_dashboard_summary(self, analysis_results: Dict) -> Dict:
        """Create summary metrics for dashboard overview"""
        metadata = analysis_results.get('metadata', {})
        text_stats = analysis_results.get('text_stats', {})
        
        # Key metrics extraction
        problem_intensity = analysis_results.get('problem_definition', {}).get('problem_intensity', {})
        moral_framing = analysis_results.get('moral_dimensions', {}).get('moral_framing_type', 'None')
        policy_stance = analysis_results.get('narrative_summary', {}).get('policy_stance', 'Unknown')
        dominant_preference = analysis_results.get('policy_solutions', {}).get('dominant_preference', 'None')
        
        return {
            'summary_metrics': {
                'organization': metadata.get('organization_name', 'Unknown'),
                'document_type': metadata.get('document_type', 'Unknown'),
                'word_count': text_stats.get('word_count', 0),
                'problem_intensity': problem_intensity.get('level', 'Unknown'),
                'moral_framing': moral_framing,
                'policy_stance': policy_stance,
                'dominant_preference': dominant_preference.replace('_', ' ').title()
            }
        }

# Initialize transformer
transformer = VisualizationDataTransformer()

@app.route('/api/visualize/sentiment', methods=['GET', 'POST'])
def get_sentiment_visualization():
    """Generate sentiment visualization data"""
    try:
        if request.method == 'POST':
            analysis_results = request.json
        else:
            # Load from file for testing
            document_id = request.args.get('document_id', 'microsoft_policy_narrative_analysis')
            file_path = project_root / f'{document_id}.json'

            if not file_path.exists():
                # Return mock data instead of error for better user experience
                return jsonify({
                    'error': 'Analysis results not found',
                    'mock_data': True,
                    'sentiment_distribution': {'positive': 40, 'neutral': 35, 'negative': 25}
                }), 200

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    analysis_results = json.load(f)
            except (json.JSONDecodeError, UnicodeDecodeError) as e:
                return jsonify({'error': f'Failed to parse analysis file: {str(e)}'}), 400
        
        visualization_data = transformer.transform_sentiment_data(analysis_results)
        return jsonify(visualization_data)
        
    except Exception as e:
        return jsonify({'error': f'Failed to generate sentiment visualization: {str(e)}'}), 500

@app.route('/api/visualize/moral-framing', methods=['GET', 'POST'])
def get_moral_framing_visualization():
    """Generate moral framing visualization data"""
    try:
        if request.method == 'POST':
            analysis_results = request.json
        else:
            document_id = request.args.get('document_id', 'microsoft_policy_narrative_analysis')
            file_path = project_root / f'{document_id}.json'

            if not file_path.exists():
                return jsonify({'error': 'Analysis results not found'}), 404

            with open(file_path, 'r') as f:
                analysis_results = json.load(f)
        
        visualization_data = transformer.transform_moral_framing_data(analysis_results)
        return jsonify(visualization_data)
        
    except Exception as e:
        return jsonify({'error': f'Failed to generate moral framing visualization: {str(e)}'}), 500

@app.route('/api/visualize/policy-preferences', methods=['GET', 'POST'])
def get_policy_preferences_visualization():
    """Generate policy preferences visualization data"""
    try:
        if request.method == 'POST':
            analysis_results = request.json
        else:
            document_id = request.args.get('document_id', 'microsoft_policy_narrative_analysis')
            file_path = project_root / f'{document_id}.json'

            if not file_path.exists():
                return jsonify({'error': 'Analysis results not found'}), 404

            with open(file_path, 'r') as f:
                analysis_results = json.load(f)
        
        visualization_data = transformer.transform_policy_preferences_data(analysis_results)
        return jsonify(visualization_data)
        
    except Exception as e:
        return jsonify({'error': f'Failed to generate policy preferences visualization: {str(e)}'}), 500

@app.route('/api/visualize/comparative', methods=['POST'])
def get_comparative_visualization():
    """Generate comparative visualization data for multiple documents"""
    try:
        request_data = request.json
        analysis_results_list = request_data.get('analyses', [])
        
        if not analysis_results_list:
            return jsonify({'error': 'No analysis results provided for comparison'}), 400
        
        visualization_data = transformer.transform_comparative_data(analysis_results_list)
        return jsonify(visualization_data)
        
    except Exception as e:
        return jsonify({'error': f'Failed to generate comparative visualization: {str(e)}'}), 500

@app.route('/api/visualize/summary', methods=['GET', 'POST'])
def get_dashboard_summary():
    """Generate dashboard summary data"""
    try:
        if request.method == 'POST':
            analysis_results = request.json
        else:
            document_id = request.args.get('document_id', 'microsoft_policy_narrative_analysis')
            file_path = project_root / f'{document_id}.json'

            if not file_path.exists():
                return jsonify({'error': 'Analysis results not found'}), 404

            with open(file_path, 'r') as f:
                analysis_results = json.load(f)
        
        summary_data = transformer.create_dashboard_summary(analysis_results)
        return jsonify(summary_data)
        
    except Exception as e:
        return jsonify({'error': f'Failed to generate dashboard summary: {str(e)}'}), 500

@app.route('/api/visualize/all', methods=['GET', 'POST'])
def get_all_visualizations():
    """Generate all visualization data for a document"""
    try:
        if request.method == 'POST':
            analysis_results = request.json
        else:
            document_id = request.args.get('document_id', 'microsoft_policy_narrative_analysis')
            file_path = project_root / f'{document_id}.json'

            if not file_path.exists():
                return jsonify({'error': 'Analysis results not found'}), 404

            with open(file_path, 'r') as f:
                analysis_results = json.load(f)
        
        # Generate all visualization types
        all_visualizations = {
            'sentiment': transformer.transform_sentiment_data(analysis_results),
            'moral_framing': transformer.transform_moral_framing_data(analysis_results),
            'policy_preferences': transformer.transform_policy_preferences_data(analysis_results),
            'summary': transformer.create_dashboard_summary(analysis_results)
        }
        
        return jsonify(all_visualizations)
        
    except Exception as e:
        return jsonify({'error': f'Failed to generate visualizations: {str(e)}'}), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """API health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'AI Policy Analyzer Visualization API',
        'version': '1.0.0'
    })

if __name__ == '__main__':
    print("Starting AI Policy Analyzer Visualization API...")
    print("Available endpoints:")
    print("  GET /api/visualize/sentiment")
    print("  GET /api/visualize/moral-framing") 
    print("  GET /api/visualize/policy-preferences")
    print("  POST /api/visualize/comparative")
    print("  GET /api/visualize/summary")
    print("  GET /api/visualize/all")
    print("  GET /api/health")
    
    app.run(debug=True, host='0.0.0.0', port=5001)