<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard错误修复验证</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-tools me-2"></i>
            Dashboard错误修复验证
        </h1>
        
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">错误修复状态</h5>
                    </div>
                    <div class="card-body">
                        <div id="fixStatus">
                            <p><i class="fas fa-spinner fa-spin"></i> 检查修复状态...</p>
                        </div>
                        
                        <hr>
                        
                        <h6>测试详细分析功能:</h6>
                        <button class="btn btn-primary" onclick="testDetailedAnalysis()">
                            <i class="fas fa-chart-line me-1"></i>
                            测试详细分析
                        </button>
                        
                        <hr>
                        
                        <h6>测试刷新功能:</h6>
                        <button class="btn btn-success" onclick="testRefreshFunction()">
                            <i class="fas fa-sync me-1"></i>
                            测试刷新功能
                        </button>
                    </div>
                </div>
                
                <!-- 详细分析模态框测试区域 -->
                <div id="testResults" class="mt-4" style="display: none;">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0">测试结果</h6>
                        </div>
                        <div class="card-body" id="testContent">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 检查修复状态
        function checkFixStatus() {
            const statusDiv = document.getElementById('fixStatus');
            let status = '';
            
            // 检查1: JavaScript语法
            try {
                eval('function test() { return true; }');
                status += '<p class="text-success"><i class="fas fa-check"></i> JavaScript语法正常</p>';
            } catch (e) {
                status += '<p class="text-danger"><i class="fas fa-times"></i> JavaScript语法错误: ' + e.message + '</p>';
            }
            
            // 检查2: Bootstrap可用性
            if (typeof bootstrap !== 'undefined') {
                status += '<p class="text-success"><i class="fas fa-check"></i> Bootstrap已加载</p>';
            } else {
                status += '<p class="text-warning"><i class="fas fa-exclamation-triangle"></i> Bootstrap未加载</p>';
            }
            
            // 检查3: 详细分析函数
            status += '<p class="text-info"><i class="fas fa-info"></i> 详细分析功能已集成到主dashboard.js中</p>';
            
            statusDiv.innerHTML = status;
        }
        
        // 测试详细分析功能
        function testDetailedAnalysis() {
            console.log('🧪 测试详细分析功能...');
            
            // 模拟分析数据
            const testData = {
                metadata: {
                    organization_name: "测试组织",
                    organization_type: "corporate",
                    sector: "technology",
                    document_type: "AI RFI Response",
                    submission_date: "2025",
                    analysis_date: new Date().toISOString(),
                    processing_time: 1.5
                },
                text_analysis: {
                    statistics: {
                        character_count: 25000,
                        word_count: 4500,
                        sentence_count: 180,
                        paragraph_count: 45,
                        avg_sentence_length: 25.0
                    },
                    content_preview: "这是一个测试分析的内容预览..."
                },
                sentiment_analysis: {
                    overall_sentiment: "积极",
                    sentiment_distribution: {
                        positive: 0.6,
                        neutral: 0.3,
                        negative: 0.1
                    },
                    confidence: 0.85
                },
                moral_dimensions: {
                    moral_categories: {
                        harm_protection: { total_mentions: 12 },
                        fairness: { total_mentions: 8 },
                        autonomy: { total_mentions: 5 },
                        transparency: { total_mentions: 10 }
                    },
                    dominant_moral_framework: "harm_protection",
                    total_moral_mentions: 35
                },
                policy_analysis: {
                    policy_preferences: {
                        self_regulation: 0.4,
                        co_regulation: 0.3,
                        government_oversight: 0.2,
                        international: 0.1
                    },
                    dominant_preference: "self_regulation"
                }
            };
            
            // 显示测试结果
            showTestResults(testData);
        }
        
        // 显示测试结果
        function showTestResults(data) {
            const resultsDiv = document.getElementById('testResults');
            const contentDiv = document.getElementById('testContent');
            
            const content = `
                <h6>测试分析结果:</h6>
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">基本信息</h6>
                        <ul>
                            <li><strong>组织:</strong> ${data.metadata.organization_name}</li>
                            <li><strong>类型:</strong> ${data.metadata.organization_type}</li>
                            <li><strong>处理时间:</strong> ${data.metadata.processing_time}秒</li>
                        </ul>
                        
                        <h6 class="text-success">文本统计</h6>
                        <ul>
                            <li><strong>字符数:</strong> ${data.text_analysis.statistics.character_count.toLocaleString()}</li>
                            <li><strong>词数:</strong> ${data.text_analysis.statistics.word_count.toLocaleString()}</li>
                            <li><strong>句子数:</strong> ${data.text_analysis.statistics.sentence_count}</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-warning">情感分析</h6>
                        <ul>
                            <li><strong>总体情感:</strong> ${data.sentiment_analysis.overall_sentiment}</li>
                            <li><strong>置信度:</strong> ${(data.sentiment_analysis.confidence * 100).toFixed(1)}%</li>
                        </ul>
                        
                        <h6 class="text-info">道德框架</h6>
                        <ul>
                            <li><strong>主导框架:</strong> ${data.moral_dimensions.dominant_moral_framework}</li>
                            <li><strong>总提及:</strong> ${data.moral_dimensions.total_moral_mentions}次</li>
                        </ul>
                        
                        <h6 class="text-dark">政策立场</h6>
                        <ul>
                            <li><strong>主导偏好:</strong> ${data.policy_analysis.dominant_preference}</li>
                        </ul>
                    </div>
                </div>
                
                <div class="alert alert-success mt-3">
                    <i class="fas fa-check-circle"></i>
                    <strong>测试成功!</strong> 详细分析功能工作正常。
                </div>
            `;
            
            contentDiv.innerHTML = content;
            resultsDiv.style.display = 'block';
        }
        
        // 测试刷新功能
        function testRefreshFunction() {
            console.log('🧪 测试刷新功能...');
            
            const testContent = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>刷新功能测试:</strong> 
                    refreshDashboard函数应该在主Dashboard中可用。
                    如果在主Dashboard中点击刷新按钮出现错误，请检查函数是否正确暴露到全局作用域。
                </div>
                
                <p><strong>修复建议:</strong></p>
                <ol>
                    <li>确保dashboard.js文件没有语法错误</li>
                    <li>确保refreshDashboard函数已暴露: <code>window.refreshDashboard = refreshDashboard;</code></li>
                    <li>在浏览器中强制刷新页面 (Ctrl+F5)</li>
                </ol>
            `;
            
            document.getElementById('testContent').innerHTML = testContent;
            document.getElementById('testResults').style.display = 'block';
        }
        
        // 页面加载时检查状态
        document.addEventListener('DOMContentLoaded', function() {
            checkFixStatus();
        });
    </script>
</body>
</html>
