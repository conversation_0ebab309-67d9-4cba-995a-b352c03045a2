#!/usr/bin/env python3
"""
AI Policy Analyzer - 优化的系统启动器
解决服务退出问题，提供稳定的服务管理

主要优化：
1. 解决缓冲区问题 - 使用日志文件而非PIPE
2. 增强健康检查 - 持续监控和自动重启
3. 改进启动时序 - 确保服务完全启动
4. 详细错误日志 - 便于问题诊断
5. 优雅的服务管理 - 更好的启动/停止机制

Author: Claude Code
Date: 2025-08-28
"""

import os
import sys
import time
import subprocess
import threading
import signal
import json
import requests
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional

class OptimizedServiceManager:
    """优化的服务管理器"""
    
    def __init__(self):
        self.processes: Dict[str, Dict] = {}
        self.base_dir = Path(__file__).parent
        self.dashboard_dir = self.base_dir / "dashboard"
        self.backend_dir = self.dashboard_dir / "backend"
        self.frontend_dir = self.dashboard_dir / "frontend"
        self.logs_dir = self.base_dir / "logs"
        
        # 创建日志目录
        self.logs_dir.mkdir(exist_ok=True)
        
        # 配置日志
        self.setup_logging()
        
        # 服务配置
        self.services_config = {
            'visualization_api': {
                'script': 'visualization_api.py',
                'port': 5001,
                'health_endpoint': '/api/health',
                'startup_time': 10,
                'critical': True
            },
            'search_api': {
                'script': 'search_endpoints.py',
                'port': 5002,
                'health_endpoint': '/api/health',
                'startup_time': 8,
                'critical': True
            },
            'historical_api': {
                'script': 'historical_data_endpoints.py',
                'port': 5003,
                'health_endpoint': '/api/health',
                'startup_time': 12,
                'critical': True
            },
            'analytics_api': {
                'script': 'advanced_analytics_api.py',
                'port': 5005,
                'health_endpoint': '/api/health',
                'startup_time': 15,
                'critical': False
            },
            'batch_api': {
                'script': 'enhanced_batch_processor.py',
                'port': 5007,
                'health_endpoint': '/api/health',
                'startup_time': 10,
                'critical': False
            },
            'frontend': {
                'script': 'server.py',
                'port': 8028,
                'health_endpoint': '/',
                'startup_time': 5,
                'critical': True,
                'working_dir': self.frontend_dir
            }
        }
        
        self.running = True
        self.restart_attempts = {}
        
    def setup_logging(self):
        """设置日志系统"""
        log_file = self.logs_dir / f"system_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("🚀 优化的服务管理器启动")
        
    def start_service(self, service_name: str, config: Dict) -> bool:
        """启动单个服务"""
        try:
            script_path = (config.get('working_dir', self.backend_dir) / config['script'])
            
            if not script_path.exists():
                self.logger.error(f"❌ 脚本文件不存在: {script_path}")
                return False
            
            # 创建服务专用的日志文件
            service_log = self.logs_dir / f"{service_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
            
            self.logger.info(f"🚀 启动服务: {service_name} (端口 {config['port']})")
            
            # 使用日志文件而非PIPE，避免缓冲区问题
            with open(service_log, 'w') as log_file:
                process = subprocess.Popen(
                    [sys.executable, str(script_path)],
                    cwd=str(config.get('working_dir', self.backend_dir)),
                    stdout=log_file,
                    stderr=subprocess.STDOUT,  # 合并stderr到stdout
                    bufsize=0  # 无缓冲
                )
            
            self.processes[service_name] = {
                'process': process,
                'config': config,
                'log_file': service_log,
                'start_time': time.time(),
                'restart_count': 0
            }
            
            self.logger.info(f"✅ {service_name} 进程启动 (PID: {process.pid})")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 启动 {service_name} 失败: {e}")
            return False
    
    def check_service_health(self, service_name: str) -> bool:
        """检查服务健康状态"""
        if service_name not in self.processes:
            return False
            
        config = self.processes[service_name]['config']
        process = self.processes[service_name]['process']
        
        # 检查进程是否还在运行
        if process.poll() is not None:
            return False
        
        # 检查HTTP健康端点
        try:
            url = f"http://localhost:{config['port']}{config['health_endpoint']}"
            response = requests.get(url, timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def wait_for_service_ready(self, service_name: str) -> bool:
        """等待服务完全启动"""
        config = self.processes[service_name]['config']
        startup_time = config['startup_time']
        
        self.logger.info(f"⏳ 等待 {service_name} 启动完成 (最多 {startup_time} 秒)")
        
        for i in range(startup_time):
            if self.check_service_health(service_name):
                self.logger.info(f"✅ {service_name} 启动完成 ({i+1} 秒)")
                return True
            time.sleep(1)
        
        self.logger.warning(f"⚠️ {service_name} 启动超时")
        return False
    
    def restart_service(self, service_name: str) -> bool:
        """重启服务"""
        if service_name not in self.restart_attempts:
            self.restart_attempts[service_name] = 0
        
        self.restart_attempts[service_name] += 1
        max_restarts = 3
        
        if self.restart_attempts[service_name] > max_restarts:
            self.logger.error(f"❌ {service_name} 重启次数超限 ({max_restarts})")
            return False
        
        self.logger.info(f"🔄 重启服务: {service_name} (第 {self.restart_attempts[service_name]} 次)")
        
        # 停止现有进程
        if service_name in self.processes:
            self.stop_service(service_name)
        
        # 等待一段时间
        time.sleep(2)
        
        # 重新启动
        config = self.services_config[service_name]
        if self.start_service(service_name, config):
            return self.wait_for_service_ready(service_name)
        
        return False
    
    def stop_service(self, service_name: str):
        """停止服务"""
        if service_name not in self.processes:
            return
        
        process = self.processes[service_name]['process']
        
        try:
            if process.poll() is None:
                self.logger.info(f"🛑 停止服务: {service_name}")
                process.terminate()
                
                # 等待优雅退出
                try:
                    process.wait(timeout=5)
                    self.logger.info(f"✅ {service_name} 已停止")
                except subprocess.TimeoutExpired:
                    self.logger.warning(f"⚠️ 强制终止 {service_name}")
                    process.kill()
                    process.wait()
                    
        except Exception as e:
            self.logger.error(f"❌ 停止 {service_name} 时出错: {e}")
        
        finally:
            del self.processes[service_name]
    
    def start_all_services(self) -> bool:
        """启动所有服务"""
        self.logger.info("🚀 开始启动所有服务")
        
        # 按优先级启动服务
        critical_services = [name for name, config in self.services_config.items() if config.get('critical', False)]
        non_critical_services = [name for name, config in self.services_config.items() if not config.get('critical', False)]
        
        # 先启动关键服务
        for service_name in critical_services:
            config = self.services_config[service_name]
            if self.start_service(service_name, config):
                if not self.wait_for_service_ready(service_name):
                    self.logger.error(f"❌ 关键服务 {service_name} 启动失败")
                    return False
            else:
                self.logger.error(f"❌ 无法启动关键服务 {service_name}")
                return False
        
        # 再启动非关键服务
        for service_name in non_critical_services:
            config = self.services_config[service_name]
            self.start_service(service_name, config)
            self.wait_for_service_ready(service_name)
        
        self.logger.info("✅ 所有服务启动完成")
        return True
    
    def monitor_services(self):
        """监控服务状态"""
        self.logger.info("👁️ 开始监控服务状态")
        
        while self.running:
            try:
                time.sleep(10)  # 每10秒检查一次
                
                for service_name in list(self.processes.keys()):
                    if not self.check_service_health(service_name):
                        self.logger.warning(f"⚠️ 检测到 {service_name} 服务异常")
                        
                        # 尝试重启关键服务
                        config = self.services_config[service_name]
                        if config.get('critical', False):
                            self.restart_service(service_name)
                        else:
                            self.logger.info(f"ℹ️ 非关键服务 {service_name} 异常，跳过重启")
                            
            except Exception as e:
                self.logger.error(f"❌ 监控过程中出错: {e}")
    
    def stop_all_services(self):
        """停止所有服务"""
        self.logger.info("🛑 停止所有服务")
        self.running = False
        
        for service_name in list(self.processes.keys()):
            self.stop_service(service_name)
        
        self.logger.info("👋 所有服务已停止")
    
    def show_status(self):
        """显示服务状态"""
        print("\n" + "="*60)
        print("📊 AI Policy Analyzer - 服务状态")
        print("="*60)
        
        for service_name, config in self.services_config.items():
            if service_name in self.processes:
                if self.check_service_health(service_name):
                    status = "🟢 运行中"
                else:
                    status = "🔴 异常"
            else:
                status = "⚫ 未启动"
            
            print(f"{service_name:<20} - {status:<10} (端口 {config['port']})")
        
        print("\n🌐 访问地址:")
        print(f"   主控制台: http://localhost:{self.services_config['frontend']['port']}")
        print("   按 Ctrl+C 停止所有服务")
        print("="*60)

def main():
    """主函数"""
    manager = OptimizedServiceManager()
    
    def signal_handler(sig, frame):
        manager.stop_all_services()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 启动所有服务
        if manager.start_all_services():
            manager.show_status()
            
            # 开始监控
            monitor_thread = threading.Thread(target=manager.monitor_services)
            monitor_thread.daemon = True
            monitor_thread.start()
            
            # 主循环
            while True:
                time.sleep(1)
        else:
            manager.logger.error("❌ 系统启动失败")
            sys.exit(1)
            
    except KeyboardInterrupt:
        pass
    except Exception as e:
        manager.logger.error(f"❌ 系统错误: {e}")
    finally:
        manager.stop_all_services()

if __name__ == '__main__':
    main()
