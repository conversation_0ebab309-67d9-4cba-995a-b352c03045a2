/**
 * 导航功能修复脚本
 * 修复仪表盘侧边栏导航无法跳转的问题
 */

// 确保在页面完全加载后运行
document.addEventListener('DOMContentLoaded', function() {
    console.log("运行导航修复脚本...");
    
    // 修复侧边栏导航功能
    function fixNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        const sections = document.querySelectorAll('.content-section');
        
        console.log(`找到 ${navLinks.length} 个导航链接和 ${sections.length} 个内容区块`);
        
        // 检查所有区块是否正确命名
        sections.forEach(section => {
            console.log(`内容区块: ${section.id}`);
        });
        
        // 重新绑定导航事件
        navLinks.forEach(link => {
            // 移除可能存在的事件监听器
            const newLink = link.cloneNode(true);
            link.parentNode.replaceChild(newLink, link);
            
            const targetSection = newLink.getAttribute('data-section');
            console.log(`设置导航: ${newLink.textContent.trim()} -> ${targetSection}-section`);
            
            // 添加新的事件监听器
            newLink.addEventListener('click', function(e) {
                e.preventDefault();
                console.log(`点击了导航: ${targetSection}`);
                
                // 更新激活的导航链接
                document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                this.classList.add('active');
                
                // 隐藏所有区块
                sections.forEach(section => {
                    section.style.display = 'none';
                });
                
                // 显示目标区块
                const targetElement = document.getElementById(targetSection + '-section');
                if (targetElement) {
                    console.log(`显示区块: ${targetSection}-section`);
                    targetElement.style.display = 'block';
                    
                    // 调用原有的数据加载函数
                    if (typeof window.loadSectionData === 'function') {
                        window.loadSectionData(targetSection);
                    } else {
                        console.error('loadSectionData 函数不存在');
                    }
                } else {
                    console.error(`未找到目标区块: ${targetSection}-section`);
                }
            });
        });
        
        console.log("导航修复完成");
    }
    
    // 等待所有脚本加载完成
    setTimeout(fixNavigation, 1000);
});
