#!/usr/bin/env python3
"""
Enhanced Batch Document Analysis API
Provides sequential document processing with progress tracking and deduplication

Author: Claude Code
Date: August 1, 2025
"""

import json
import os
import sys
import sqlite3
import hashlib
from datetime import datetime
from typing import Dict, List, Any, Optional
from flask import Flask, jsonify, request
from flask_cors import CORS
import time
import threading

# Add path for imports
sys.path.append('.')
sys.path.append('../..')

try:
    from historical_data_integration import initialize_historical_data_system
    from search_api import SearchIndexManager
    # Import policy narrative analyzer
    from phase2_policy_narrative_analysis import PolicyNarrativeAnalyzer
except ImportError as e:
    print(f"❌ Import error: {e}")
    PolicyNarrativeAnalyzer = None

app = Flask(__name__)
CORS(app)

# Global state for batch processing
batch_state = {
    'is_running': False,
    'current_batch_id': None,
    'progress': {},
    'results': {}
}

class EnhancedBatchProcessor:
    """Enhanced batch processor with deduplication and progress tracking"""
    
    def __init__(self, search_index_manager):
        self.search_index = search_index_manager
        self.analyzer = PolicyNarrativeAnalyzer() if PolicyNarrativeAnalyzer else None
        self.processed_hashes = set()
        self.load_existing_hashes()
    
    def load_existing_hashes(self):
        """Load existing document hashes to avoid duplicates"""
        try:
            conn = sqlite3.connect(self.search_index.db_path)
            cursor = conn.cursor()
            cursor.execute('SELECT content_hash FROM documents WHERE content_hash IS NOT NULL')
            self.processed_hashes = {row[0] for row in cursor.fetchall()}
            conn.close()
            print(f"📝 Loaded {len(self.processed_hashes)} existing document hashes")
        except Exception as e:
            print(f"⚠️ Could not load existing hashes: {e}")
            self.processed_hashes = set()
    
    def generate_content_hash(self, file_path: str) -> str:
        """Generate content hash for a file"""
        try:
            with open(file_path, 'rb') as f:
                file_content = f.read()
            return hashlib.md5(file_content).hexdigest()
        except Exception:
            return hashlib.md5(file_path.encode()).hexdigest()
    
    def is_already_processed(self, file_path: str) -> bool:
        """Check if document is already processed"""
        content_hash = self.generate_content_hash(file_path)
        return content_hash in self.processed_hashes
    
    def process_document_batch(self, batch_id: str, file_paths: List[str], 
                              start_index: int = 0, batch_size: int = 10) -> Dict:
        """Process a batch of documents with progress tracking"""
        
        if not self.analyzer:
            return {'error': 'Policy analyzer not available'}
        
        batch_state['is_running'] = True
        batch_state['current_batch_id'] = batch_id
        batch_state['progress'][batch_id] = {
            'total_files': len(file_paths),
            'processed': start_index,
            'successful': 0,
            'skipped': 0,
            'failed': 0,
            'current_file': None,
            'start_time': datetime.now().isoformat(),
            'status': 'running'
        }
        
        results = []
        processed_count = start_index
        
        try:
            # Process files in batch
            end_index = min(start_index + batch_size, len(file_paths))
            current_batch = file_paths[start_index:end_index]
            
            for i, file_path in enumerate(current_batch):
                current_index = start_index + i
                progress = batch_state['progress'][batch_id]
                
                # Update progress
                progress['processed'] = current_index + 1
                progress['current_file'] = os.path.basename(file_path)
                
                try:
                    # Check if already processed
                    if self.is_already_processed(file_path):
                        print(f"⏭️  Skipping already processed: {os.path.basename(file_path)}")
                        progress['skipped'] += 1
                        continue
                    
                    # Process document
                    print(f"📄 Processing [{current_index + 1}/{len(file_paths)}]: {os.path.basename(file_path)}")
                    
                    # Analyze document
                    analysis_result = self.analyzer.analyze_document(file_path)
                    
                    if analysis_result:
                        # Add metadata
                        analysis_result['metadata'] = analysis_result.get('metadata', {})
                        analysis_result['metadata']['batch_id'] = batch_id
                        analysis_result['metadata']['processed_at'] = datetime.now().isoformat()
                        analysis_result['metadata']['file_path'] = file_path
                        
                        # Index in search system
                        doc_id = self.search_index.index_document(analysis_result, file_path)
                        
                        # Add to processed hashes
                        content_hash = self.generate_content_hash(file_path)
                        self.processed_hashes.add(content_hash)
                        
                        results.append({
                            'document_id': doc_id,
                            'file_path': file_path,
                            'status': 'success',
                            'organization': analysis_result['metadata'].get('organization_name', 'Unknown')
                        })
                        
                        progress['successful'] += 1
                        print(f"✅ Successfully processed: {doc_id}")
                    else:
                        progress['failed'] += 1
                        print(f"❌ Failed to analyze: {os.path.basename(file_path)}")
                        
                except Exception as e:
                    progress['failed'] += 1
                    print(f"❌ Error processing {os.path.basename(file_path)}: {e}")
                    results.append({
                        'file_path': file_path,
                        'status': 'error',
                        'error': str(e)
                    })
                
                # Small delay to prevent overwhelming the system
                time.sleep(0.1)
            
            # Update final status
            progress['status'] = 'completed'
            progress['end_time'] = datetime.now().isoformat()
            
            return {
                'batch_id': batch_id,
                'processed_range': f"{start_index + 1}-{end_index}",
                'total_processed': len(current_batch),
                'successful': progress['successful'],
                'skipped': progress['skipped'],
                'failed': progress['failed'],
                'results': results,
                'has_more': end_index < len(file_paths),
                'next_start_index': end_index if end_index < len(file_paths) else None
            }
            
        except Exception as e:
            batch_state['progress'][batch_id]['status'] = 'error'
            batch_state['progress'][batch_id]['error'] = str(e)
            return {'error': f'Batch processing failed: {e}'}
        
        finally:
            batch_state['is_running'] = False
    
    def get_processing_status(self) -> Dict[str, Any]:
        """Get current processing status"""
        global batch_state
        
        active_batches = []
        for batch_id, progress in batch_state['progress'].items():
            if progress['status'] == 'running':
                active_batches.append({
                    'batch_id': batch_id,
                    'progress': progress
                })
        
        return {
            'status': 'running' if batch_state['is_running'] else 'idle',
            'active_batches': active_batches,
            'total_processed_documents': len(self.processed_hashes),
            'available_analyzers': 1 if self.analyzer else 0
        }
    
    def get_queue_info(self) -> Dict[str, Any]:
        """Get processing queue information"""
        global batch_state
        
        return {
            'size': len(batch_state['progress']),
            'running_batches': len([p for p in batch_state['progress'].values() if p['status'] == 'running']),
            'completed_batches': len([p for p in batch_state['progress'].values() if p['status'] == 'completed']),
            'failed_batches': len([p for p in batch_state['progress'].values() if p['status'] == 'error'])
        }

# Initialize the enhanced processor
try:
    search_index = SearchIndexManager()
    batch_processor = EnhancedBatchProcessor(search_index)
    print("✅ Enhanced batch processor initialized")
except Exception as e:
    print(f"❌ Failed to initialize batch processor: {e}")
    batch_processor = None

@app.route('/api/batch/start', methods=['POST'])
def start_batch_processing():
    """Start sequential batch processing of documents"""
    try:
        if not batch_processor:
            return jsonify({'error': 'Batch processor not available'}), 503
        
        if batch_state['is_running']:
            return jsonify({'error': 'Another batch is already running'}), 409
        
        data = request.json or {}
        batch_size = min(data.get('batch_size', 10), 50)  # Limit batch size
        start_index = data.get('start_index', 0)
        
        # Generate batch ID
        batch_id = f"batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Get list of PDF files
        from historical_data_integration import HistoricalDataLoader
        loader = HistoricalDataLoader()
        
        pdf_files = []
        if os.path.exists(loader.pdf_path):
            for filename in sorted(os.listdir(loader.pdf_path)):
                if filename.lower().endswith('.pdf'):
                    pdf_files.append(os.path.join(loader.pdf_path, filename))
        
        if not pdf_files:
            return jsonify({'error': 'No PDF files found'}), 404
        
        if start_index >= len(pdf_files):
            return jsonify({'error': 'Start index exceeds available files'}), 400
        
        # Start processing in a separate thread
        def process_batch():
            result = batch_processor.process_document_batch(
                batch_id, pdf_files, start_index, batch_size
            )
            batch_state['results'][batch_id] = result
        
        thread = threading.Thread(target=process_batch)
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'batch_id': batch_id,
            'total_files': len(pdf_files),
            'batch_size': batch_size,
            'start_index': start_index,
            'status': 'started',
            'message': f'Started processing batch of {batch_size} documents'
        })
        
    except Exception as e:
        return jsonify({'error': f'Failed to start batch processing: {str(e)}'}), 500

@app.route('/api/batch/progress/<batch_id>', methods=['GET'])
def get_batch_progress(batch_id):
    """Get progress of a batch processing job"""
    try:
        if batch_id not in batch_state['progress']:
            return jsonify({'error': 'Batch ID not found'}), 404
        
        progress = batch_state['progress'][batch_id].copy()
        
        # Calculate completion percentage
        if progress['total_files'] > 0:
            progress['completion_percentage'] = round(
                (progress['processed'] / progress['total_files']) * 100, 2
            )
        else:
            progress['completion_percentage'] = 0
        
        # Add estimated time remaining
        if progress['status'] == 'running' and progress['processed'] > 0:
            start_time = datetime.fromisoformat(progress['start_time'])
            elapsed = (datetime.now() - start_time).total_seconds()
            avg_time_per_doc = elapsed / progress['processed']
            remaining_docs = progress['total_files'] - progress['processed']
            estimated_remaining = remaining_docs * avg_time_per_doc
            progress['estimated_remaining_seconds'] = round(estimated_remaining)
        
        return jsonify(progress)
        
    except Exception as e:
        return jsonify({'error': f'Failed to get progress: {str(e)}'}), 500

@app.route('/api/batch/results/<batch_id>', methods=['GET'])
def get_batch_results(batch_id):
    """Get results of a completed batch"""
    try:
        if batch_id not in batch_state['results']:
            return jsonify({'error': 'Batch results not found'}), 404
        
        return jsonify(batch_state['results'][batch_id])
        
    except Exception as e:
        return jsonify({'error': f'Failed to get results: {str(e)}'}), 500

@app.route('/api/batch/status', methods=['GET'])
def get_overall_status():
    """Get overall batch processing status"""
    try:
        # Get summary statistics
        conn = sqlite3.connect(batch_processor.search_index.db_path if batch_processor else 'search_index.db')
        cursor = conn.cursor()
        
        cursor.execute('SELECT COUNT(*) FROM documents')
        total_docs = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(DISTINCT batch_id) FROM documents WHERE batch_id IS NOT NULL')
        total_batches = cursor.fetchone()[0]
        
        conn.close()
        
        return jsonify({
            'is_running': batch_state['is_running'],
            'current_batch_id': batch_state['current_batch_id'],
            'total_documents_processed': total_docs,
            'total_batches_completed': total_batches,
            'active_batches': len([p for p in batch_state['progress'].values() if p['status'] == 'running']),
            'available_pdf_files': len([f for f in os.listdir(batch_processor.search_index.db_path.replace('search_index.db', '../../../90-fr-9088-combined-responses')) if f.endswith('.pdf')]) if batch_processor else 0
        })
        
    except Exception as e:
        return jsonify({'error': f'Failed to get status: {str(e)}'}), 500

@app.route('/api/batch/health', methods=['GET'])
def batch_health_check():
    """Health check for batch processing API"""
    
    health_status = {
        'status': 'healthy' if batch_processor else 'unavailable',
        'service': 'Enhanced Batch Document Processor',
        'version': '1.0.0',
        'components': {
            'batch_processor': 'operational' if batch_processor else 'failed',
            'policy_analyzer': 'operational' if batch_processor and batch_processor.analyzer else 'failed',
            'search_index': 'operational' if batch_processor and batch_processor.search_index else 'failed'
        }
    }
    
    if batch_processor:
        try:
            health_status['processor_info'] = {
                'processed_hashes_count': len(batch_processor.processed_hashes),
                'analyzer_available': batch_processor.analyzer is not None
            }
        except Exception as e:
            health_status['status'] = 'degraded'
            health_status['error'] = str(e)
    
    status_code = 200 if health_status['status'] == 'healthy' else 503
    return jsonify(health_status), status_code

if __name__ == '__main__':
    print("🚀 Starting Enhanced Batch Document Processor...")
    print("Available endpoints:")
    print("  POST /api/batch/start - Start batch processing")
    print("  GET /api/batch/progress/<batch_id> - Get batch progress")
    print("  GET /api/batch/results/<batch_id> - Get batch results")
    print("  GET /api/batch/status - Get overall status")
    print("  GET /api/batch/health - Health check")
    
    # Import port configuration
try:
    from port_config import get_port
    port = get_port('enhanced_batch_api')
except ImportError:
    port = 5007  # fallback port

app.run(debug=True, host='0.0.0.0', port=port)