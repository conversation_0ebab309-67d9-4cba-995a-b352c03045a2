<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Fixes Test</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4">Dashboard Fixes Test</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>JavaScript Function Tests</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary mb-2" onclick="testRefreshDashboard()">
                            Test refreshDashboard()
                        </button>
                        <br>
                        <button class="btn btn-success mb-2" onclick="testNavigation()">
                            Test Navigation
                        </button>
                        <br>
                        <button class="btn btn-info mb-2" onclick="testDocumentAnalysis()">
                            Test Document Analysis Fix
                        </button>
                        <div id="testResults" class="mt-3"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Console Output</h5>
                    </div>
                    <div class="card-body">
                        <div id="consoleOutput" style="height: 300px; overflow-y: auto; background: #f8f9fa; padding: 10px; font-family: monospace; font-size: 12px;">
                            Console output will appear here...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Test Scripts -->
    <script>
        // Override console.log to display in our test page
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const consoleOutput = document.getElementById('consoleOutput');
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'text-danger' : type === 'warn' ? 'text-warning' : 'text-dark';
            consoleOutput.innerHTML += `<div class="${color}">[${timestamp}] ${message}</div>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        // Test functions
        function testRefreshDashboard() {
            console.log('Testing refreshDashboard function...');
            try {
                if (typeof refreshDashboard === 'function') {
                    refreshDashboard();
                    showTestResult('refreshDashboard', 'success', 'Function exists and executed');
                } else {
                    showTestResult('refreshDashboard', 'error', 'Function not found');
                }
            } catch (error) {
                console.error('Error testing refreshDashboard:', error.message);
                showTestResult('refreshDashboard', 'error', error.message);
            }
        }
        
        function testNavigation() {
            console.log('Testing navigation functions...');
            try {
                // Test if navigation elements exist
                const navLinks = document.querySelectorAll('.nav-link');
                const sections = document.querySelectorAll('.content-section');
                
                console.log(`Found ${navLinks.length} navigation links`);
                console.log(`Found ${sections.length} content sections`);
                
                showTestResult('navigation', 'success', `Navigation elements found: ${navLinks.length} links, ${sections.length} sections`);
            } catch (error) {
                console.error('Error testing navigation:', error.message);
                showTestResult('navigation', 'error', error.message);
            }
        }
        
        function testDocumentAnalysis() {
            console.log('Testing document analysis fix...');
            try {
                // Test if document analysis functions exist
                const functions = ['startDocumentAnalysis', 'fixDocumentTitleValidation'];
                let foundFunctions = 0;
                
                functions.forEach(funcName => {
                    if (typeof window[funcName] === 'function') {
                        console.log(`✓ ${funcName} function found`);
                        foundFunctions++;
                    } else {
                        console.log(`✗ ${funcName} function not found`);
                    }
                });
                
                showTestResult('documentAnalysis', foundFunctions > 0 ? 'success' : 'warning', 
                    `Found ${foundFunctions}/${functions.length} document analysis functions`);
            } catch (error) {
                console.error('Error testing document analysis:', error.message);
                showTestResult('documentAnalysis', 'error', error.message);
            }
        }
        
        function showTestResult(testName, status, message) {
            const resultsDiv = document.getElementById('testResults');
            const alertClass = status === 'success' ? 'alert-success' : 
                              status === 'warning' ? 'alert-warning' : 'alert-danger';
            const icon = status === 'success' ? 'fa-check-circle' : 
                        status === 'warning' ? 'fa-exclamation-triangle' : 'fa-times-circle';
            
            resultsDiv.innerHTML += `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    <i class="fas ${icon} me-2"></i>
                    <strong>${testName}:</strong> ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }
        
        // Load the main dashboard scripts
        console.log('Loading dashboard scripts...');
        
        // Load scripts in order
        const scripts = [
            'frontend/dashboard.js',
            'frontend/fix_navigation.js',
            '../fix_document_analysis_issue.js'
        ];
        
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = () => {
                    console.log(`✓ Loaded: ${src}`);
                    resolve();
                };
                script.onerror = () => {
                    console.error(`✗ Failed to load: ${src}`);
                    reject(new Error(`Failed to load ${src}`));
                };
                document.head.appendChild(script);
            });
        }
        
        // Load scripts sequentially
        async function loadAllScripts() {
            for (const script of scripts) {
                try {
                    await loadScript(script);
                } catch (error) {
                    console.error(`Error loading ${script}:`, error.message);
                }
            }
            console.log('All scripts loaded. Ready for testing.');
        }
        
        // Start loading scripts when page is ready
        document.addEventListener('DOMContentLoaded', loadAllScripts);
    </script>
</body>
</html>
