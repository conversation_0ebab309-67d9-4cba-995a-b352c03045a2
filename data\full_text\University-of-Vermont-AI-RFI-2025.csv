﻿content_type,page_number,content,character_count,word_count,metadata_field,metadata_value
document_info,0,Document: University-of-Vermont-AI-RFI-2025.pdf,0,0,filename,University-of-Vermont-AI-RFI-2025.pdf
metadata,0,"Comments Received in Response To: Request for Information on the Development of an Artificial Intelligence (AI) Action Plan (""Plan"")",132,19,title,"Comments Received in Response To: Request for Information on the Development of an Artificial Intelligence (AI) Action Plan (""Plan"")"
metadata,0,Adobe Acrobat Pro (64-bit) 25.1.20432,37,5,creator,Adobe Acrobat Pro (64-bit) 25.1.20432
metadata,0,Adobe Acrobat Pro (64-bit) 25.1.20432,37,5,producer,Adobe Acrobat Pro (64-bit) 25.1.20432
metadata,0,D:202***********-04'00',23,1,creation_date,D:202***********-04'00'
metadata,0,D:202***********-04'00',23,1,modification_date,D:202***********-04'00'
document_stats,0,"Total pages: 3, Total characters: 8180, Total words: 1169",8180,1169,document_stats,"pages:3,chars:8180,words:1169"
full_text,0,"1 Submitted by the University of Vermont and State Agricultural College In response to the Request for Information on the Development of an Artificial Intelligence (AI) Action Plan April 1 4, 2025 This document is approved for public dissemination. It contains no business -proprietary or confidential information. The contents may be reused by the government in developing the AI Action Plan , and associated documents , without attribution. This document presents topical areas/themes we have identified and would like to propose around Artificial Intelligence (AI) in t wo broad categories: Foundational research in AI Applications of AI in addressing transdisciplinary research questions 1.Foundational Research in AI Develop AI algorithms which fuse and represent multiple data types for the purpose of improving human synthesis, reasoning and decision -making. Investig ate Snowflake AI , where each AI model generated by a training method is unique in form and function. This ensures that adversarial attacks cannot locate and exploit a common Achilles Heel vulnerability . Develop methods to reduce demographic and data -driven model biases , especially in applications like healthcare, where biased models can negatively affect patient outcomes. Develop robust AI m odels that can effectively handle real -world shifts in data perform symbolic regression (i.e., automate the distillation of data into equations). Expand research on models that can run efficiently on edge computing devices or in resource -constrained settings (i.e., TinyML, EdgeAI) , potentially opening access for rural health clinics , robotics applications, etc. Design t hermal management and energy efficiency of AI computing through federated computing and load sharing of package stacks. Cyber-physical solution s can combine hardware and AI for optimizing and managing resources in AI computing . 2.Applications of AI in Addressing Transdisciplinary Research Health and Human Wellness Integrate AI and smart sensors to perceive and interpret information beyond human capability, improving human understanding and decision -making. Develo p privacy -preserving AI models to leverage insights from wearable sensors and mobile phones to inform targeted interventions in health care . Potential privacy concerns would have to be identified and convincingly handled. Establish a national framework allowing researchers and clinicians to securely process personally identifiable information (PII) and protected health information (PHI) through advanced AI models, overcoming current HIPAA and IRB protocol constraints. 2 (Currently, many leading LLMs and AI tools must be run in the cloud, so researchers or clinicians must send data to the servers of an external company .) Use AI for d iagnostic support . Examples include: (i) Image analysis (use AI to detect pathologies in medical images, and to augment the capabilities of experienced clinicians ); and (ii) point-of-care diagnosis (deploy AI -based tools, possibly on handheld devices, offering capability augmentation in rural or underserved areas ). Develop AI -based tools for p rocedure guidance via real -time augmentation or robot - assisted surger ies. Enhance surgical precision and safety by providing real -time guidance during procedures (e.g., optimizing instrument control, providing informed decisions ). Clinical augmentation : Build t ools that can perform automatic summarization of patient histories or predictive tools that flag high -risk patient information for closer monitoring. Treatment personalization : AI-driven recommendation system can provide customized treatment plans based on genomic, clinical, and lifestyle data. Use AI -based tools to understand protein dynamics and function s at the molecular level to design small -molecule drugs targeting proteins whose mutations lead to human diseases. Increase transparency: c linical decision -making via AI -driven recommendations requires transparent models that can provide clinicians with clear rationales for decisions (e.g. diagnostic suggestions, recommended treatment s, surgical navigation decision -making). Improv e model interpretability : this fosters trust from stakeholders (patients, clinicians, regulators) and accelerates adoptions of AI tools. To that e nd, n ew techniques that offer both global (overall model) and local (instance -level) interpretability, along with standardized metrics to evaluate how interpretable a model is , are needed. Agriculture , Water Security and Public Safety Deploy AI to identify and bias -correct water models for more accurate predictions . Data- driven regional policies can be developed to improve our management of water resources , particularly during extreme events. Use AI, integrated with on -ground smart sensors and satellite data , to improve estimates of crop yield, and water budgets (e.g., evapotranspiration, soil moisture) at finer spatial and temporal resolution to support data -driven precision agriculture . Use AI for water quantity and quality monitoring, synthesis and forecasting of extreme events (e.g., droughts, floods, wildfires, landslides). Improve transportation by leveraging AI for a comprehensive analysis of road conditions and safety measures. AI can help e valuate street imagery to assess road quality, detect structural and infrastructure issues, and identify hazards, thereby improving maintenance efficiency and enhancing overall public safety. Build AI models that advance the integration of geospatial data from multiple modalities including satellite imagery, ground sensors, and drone footage to develop robust predictive tools and a comprehensive understanding of geospatial areas. As an applicati on to these models, they can accurately forecast drought conditions and anticipate flood events by analyzing diverse datasets. 3 Smart Organisms, Materials and Structural Systems AI can help design organisms , i.e., living machines that combine biology and AI (e.g., Xenobots , which are synthetic lifeforms designed to perform some desired function and are built by combining together different biological tissues ). AI can also help with the efficient and automatic design of classical robot ic structures . ML-Assisted Structural Optimization : AI can help d evelo p models for vibration control and vibrational energy harvesting , enhanc ing mechanical system performance and efficiency. ML-Accelerated First -Principles Calculations : AI can accelerate the computation of exciton -phonon and electron -phonon coupling in nanostructures and quantum materials , thus driving material discovery in e.g., solar energy material applications . ML-Assisted Polymer Dynamics and Chemistry Simulations : AI can help scientists gain a fundamental understanding of macroscopic mechanical behaviors from a molecular dynamics perspective. This can inform the experimental design of polymer composites with enhanced mechanical resilience, e.g., enabling them to withstand harsh environments. Explore peptide design: AI can explore vast sequence spaces and predict peptide structures with desir able functional properties. This can accelerate therapeutic discovery and enhance the efficiency and cost -effectiveness of drug development . Knowledge -Guided ML Research is needed to integrate more traditional physics -based analysis approaches with machine learning , to benefit from the strength of both approaches. A pplications are broad -based , including fields such as smart structure s, transport processes, etc . Energy and Grid Stability Verify the performance of ML models used for power grid management to safely operate modern power systems and help accelerate the renewable energy transition . Utilize deep learning architectures, particularly implicit neural network -based solvers, to address previously unsolvable grid optimization problems. Deploy graph -based, topology -aware AI models for anomaly detection on power grids using spatial -temporal data. Create data-driven control strategies by combining behavioral system theory with machine learning, developing efficient control algorithms without relying on explicit mathematical models of system dynamics.",8180,1169,full_document_text,
page_text,1,"1 Submitted by the University of Vermont and State Agricultural College In response to the Request for Information on the Development of an Artificial Intelligence (AI) Action Plan April 1 4, 2025 This document is approved for public dissemination. It contains no business -proprietary or confidential information. The contents may be reused by the government in developing the AI Action Plan , and associated documents , without attribution. This document presents topical areas/themes we have identified and would like to propose around Artificial Intelligence (AI) in t wo broad categories: Foundational research in AI Applications of AI in addressing transdisciplinary research questions 1.Foundational Research in AI Develop AI algorithms which fuse and represent multiple data types for the purpose of improving human synthesis, reasoning and decision -making. Investig ate Snowflake AI , where each AI model generated by a training method is unique in form and function. This ensures that adversarial attacks cannot locate and exploit a common Achilles Heel vulnerability . Develop methods to reduce demographic and data -driven model biases , especially in applications like healthcare, where biased models can negatively affect patient outcomes. Develop robust AI m odels that can effectively handle real -world shifts in data perform symbolic regression (i.e., automate the distillation of data into equations). Expand research on models that can run efficiently on edge computing devices or in resource -constrained settings (i.e., TinyML, EdgeAI) , potentially opening access for rural health clinics , robotics applications, etc. Design t hermal management and energy efficiency of AI computing through federated computing and load sharing of package stacks. Cyber-physical solution s can combine hardware and AI for optimizing and managing resources in AI computing . 2.Applications of AI in Addressing Transdisciplinary Research Health and Human Wellness Integrate AI and smart sensors to perceive and interpret information beyond human capability, improving human understanding and decision -making. Develo p privacy -preserving AI models to leverage insights from wearable sensors and mobile phones to inform targeted interventions in health care . Potential privacy concerns would have to be identified and convincingly handled. Establish a national framework allowing researchers and clinicians to securely process personally identifiable information (PII) and protected health information (PHI) through advanced AI models, overcoming current HIPAA and IRB protocol constraints.",2597,374,page_content,page_1
page_text,2,"2 (Currently, many leading LLMs and AI tools must be run in the cloud, so researchers or clinicians must send data to the servers of an external company .) Use AI for d iagnostic support . Examples include: (i) Image analysis (use AI to detect pathologies in medical images, and to augment the capabilities of experienced clinicians ); and (ii) point-of-care diagnosis (deploy AI -based tools, possibly on handheld devices, offering capability augmentation in rural or underserved areas ). Develop AI -based tools for p rocedure guidance via real -time augmentation or robot - assisted surger ies. Enhance surgical precision and safety by providing real -time guidance during procedures (e.g., optimizing instrument control, providing informed decisions ). Clinical augmentation : Build t ools that can perform automatic summarization of patient histories or predictive tools that flag high -risk patient information for closer monitoring. Treatment personalization : AI-driven recommendation system can provide customized treatment plans based on genomic, clinical, and lifestyle data. Use AI -based tools to understand protein dynamics and function s at the molecular level to design small -molecule drugs targeting proteins whose mutations lead to human diseases. Increase transparency: c linical decision -making via AI -driven recommendations requires transparent models that can provide clinicians with clear rationales for decisions (e.g. diagnostic suggestions, recommended treatment s, surgical navigation decision -making). Improv e model interpretability : this fosters trust from stakeholders (patients, clinicians, regulators) and accelerates adoptions of AI tools. To that e nd, n ew techniques that offer both global (overall model) and local (instance -level) interpretability, along with standardized metrics to evaluate how interpretable a model is , are needed. Agriculture , Water Security and Public Safety Deploy AI to identify and bias -correct water models for more accurate predictions . Data- driven regional policies can be developed to improve our management of water resources , particularly during extreme events. Use AI, integrated with on -ground smart sensors and satellite data , to improve estimates of crop yield, and water budgets (e.g., evapotranspiration, soil moisture) at finer spatial and temporal resolution to support data -driven precision agriculture . Use AI for water quantity and quality monitoring, synthesis and forecasting of extreme events (e.g., droughts, floods, wildfires, landslides). Improve transportation by leveraging AI for a comprehensive analysis of road conditions and safety measures. AI can help e valuate street imagery to assess road quality, detect structural and infrastructure issues, and identify hazards, thereby improving maintenance efficiency and enhancing overall public safety. Build AI models that advance the integration of geospatial data from multiple modalities including satellite imagery, ground sensors, and drone footage to develop robust predictive tools and a comprehensive understanding of geospatial areas. As an applicati on to these models, they can accurately forecast drought conditions and anticipate flood events by analyzing diverse datasets.",3241,466,page_content,page_2
page_text,3,"3 Smart Organisms, Materials and Structural Systems AI can help design organisms , i.e., living machines that combine biology and AI (e.g., Xenobots , which are synthetic lifeforms designed to perform some desired function and are built by combining together different biological tissues ). AI can also help with the efficient and automatic design of classical robot ic structures . ML-Assisted Structural Optimization : AI can help d evelo p models for vibration control and vibrational energy harvesting , enhanc ing mechanical system performance and efficiency. ML-Accelerated First -Principles Calculations : AI can accelerate the computation of exciton -phonon and electron -phonon coupling in nanostructures and quantum materials , thus driving material discovery in e.g., solar energy material applications . ML-Assisted Polymer Dynamics and Chemistry Simulations : AI can help scientists gain a fundamental understanding of macroscopic mechanical behaviors from a molecular dynamics perspective. This can inform the experimental design of polymer composites with enhanced mechanical resilience, e.g., enabling them to withstand harsh environments. Explore peptide design: AI can explore vast sequence spaces and predict peptide structures with desir able functional properties. This can accelerate therapeutic discovery and enhance the efficiency and cost -effectiveness of drug development . Knowledge -Guided ML Research is needed to integrate more traditional physics -based analysis approaches with machine learning , to benefit from the strength of both approaches. A pplications are broad -based , including fields such as smart structure s, transport processes, etc . Energy and Grid Stability Verify the performance of ML models used for power grid management to safely operate modern power systems and help accelerate the renewable energy transition . Utilize deep learning architectures, particularly implicit neural network -based solvers, to address previously unsolvable grid optimization problems. Deploy graph -based, topology -aware AI models for anomaly detection on power grids using spatial -temporal data. Create data-driven control strategies by combining behavioral system theory with machine learning, developing efficient control algorithms without relying on explicit mathematical models of system dynamics.",2340,329,page_content,page_3
