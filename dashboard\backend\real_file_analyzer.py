#!/usr/bin/env python3
"""
真实文件分析器 - 读取现有所有文件进行分析

Author: Claude Code
Date: 2025-08-08
"""

import os
import csv
import json
import sqlite3
from pathlib import Path
from typing import Dict, List, Optional
from datetime import datetime
import pandas as pd

# 导入我们的分析引擎
try:
    from real_analysis_engine import RealAnalysisEngine, ContentAnalyzer
    ANALYSIS_ENGINE_AVAILABLE = True
except ImportError:
    ANALYSIS_ENGINE_AVAILABLE = False

class RealFileAnalyzer:
    """真实文件分析器"""
    
    def __init__(self):
        self.pdf_dir = Path("data/90_FR_9088_pdfs")
        self.csv_dir = Path("data/full_text")
        self.db_path = "dashboard/backend/real_file_analysis.db"
        
        if ANALYSIS_ENGINE_AVAILABLE:
            self.analysis_engine = RealAnalysisEngine()
            self.content_analyzer = ContentAnalyzer()
        
        self.init_database()
    
    def init_database(self):
        """初始化数据库"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS file_analysis (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    filename TEXT UNIQUE,
                    organization_name TEXT,
                    organization_type TEXT,
                    sector TEXT,
                    file_size INTEGER,
                    analysis_date TEXT,
                    character_count INTEGER,
                    word_count INTEGER,
                    sentence_count INTEGER,
                    overall_sentiment TEXT,
                    sentiment_positive REAL,
                    sentiment_neutral REAL,
                    sentiment_negative REAL,
                    dominant_moral_framework TEXT,
                    moral_harm_protection INTEGER,
                    moral_fairness INTEGER,
                    moral_autonomy INTEGER,
                    moral_transparency INTEGER,
                    dominant_policy_preference TEXT,
                    policy_self_regulation REAL,
                    policy_co_regulation REAL,
                    policy_government_oversight REAL,
                    policy_international REAL,
                    content_preview TEXT,
                    processing_time REAL
                )
            ''')
            
            conn.commit()
    
    def scan_available_files(self) -> List[Dict]:
        """扫描可用文件"""
        print("📁 扫描可用文件...")
        
        available_files = []
        
        # 扫描CSV文件（文本提取文件）
        if self.csv_dir.exists():
            csv_files = list(self.csv_dir.glob("*.csv"))
            print(f"   找到 {len(csv_files)} 个CSV文本文件")
            
            for csv_file in csv_files[:100]:  # 先处理前100个文件
                filename = csv_file.stem
                pdf_file = self.pdf_dir / f"{filename}.pdf"
                
                file_info = {
                    'filename': filename,
                    'csv_path': str(csv_file),
                    'pdf_path': str(pdf_file) if pdf_file.exists() else None,
                    'csv_size': csv_file.stat().st_size,
                    'pdf_size': pdf_file.stat().st_size if pdf_file.exists() else 0
                }
                
                available_files.append(file_info)
        
        print(f"✅ 扫描完成: {len(available_files)} 个文件可供分析")
        return available_files
    
    def extract_text_from_csv(self, csv_path: str) -> str:
        """从CSV文件提取文本"""
        try:
            with open(csv_path, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                text_content = []
                
                for row in reader:
                    if row:  # 非空行
                        text_content.append(' '.join(row))
                
                return '\n'.join(text_content)
        
        except Exception as e:
            print(f"⚠️ CSV读取失败 {csv_path}: {e}")
            return ""
    
    def classify_organization(self, filename: str) -> Dict:
        """分类组织信息"""
        # 从文件名推断组织信息
        name_parts = filename.replace('-AI-RFI-2025', '').replace('-RFI-2025', '')
        org_name = name_parts.replace('-', ' ')
        
        # 组织类型分类
        org_type = 'unknown'
        sector = 'other'
        
        name_lower = org_name.lower()
        
        # 学术机构
        if any(word in name_lower for word in ['university', 'college', 'institute', 'school', 'research', 'academic']):
            org_type = 'academic'
            sector = 'education'
        # 政府机构
        elif any(word in name_lower for word in ['government', 'agency', 'department', 'federal', 'state', 'city']):
            org_type = 'government'
            sector = 'government'
        # 非营利组织
        elif any(word in name_lower for word in ['foundation', 'association', 'society', 'coalition', 'alliance', 'union']):
            org_type = 'nonprofit'
            sector = 'advocacy'
        # 医疗机构
        elif any(word in name_lower for word in ['health', 'medical', 'hospital', 'clinic', 'pharma']):
            org_type = 'healthcare'
            sector = 'healthcare'
        # 科技公司
        elif any(word in name_lower for word in ['tech', 'ai', 'software', 'computing', 'data', 'digital']):
            org_type = 'corporate'
            sector = 'technology'
        # 其他公司
        elif any(word in name_lower for word in ['corp', 'inc', 'company', 'llc', 'ltd']):
            org_type = 'corporate'
            sector = 'business'
        # 专业协会
        elif len(name_parts.split('-')) <= 2 and len(org_name) <= 10:
            org_type = 'professional'
            sector = 'professional'
        
        return {
            'organization_name': org_name,
            'organization_type': org_type,
            'sector': sector
        }
    
    def analyze_file(self, file_info: Dict) -> Dict:
        """分析单个文件"""
        filename = file_info['filename']
        print(f"🔍 分析文件: {filename}")
        
        start_time = datetime.now()
        
        # 1. 提取文本
        text = self.extract_text_from_csv(file_info['csv_path'])
        
        # 2. 分类组织
        org_info = self.classify_organization(filename)
        
        # 3. 进行分析
        if ANALYSIS_ENGINE_AVAILABLE and text:
            # 使用真实分析引擎
            analysis_result = self.analysis_engine.analyze_document(
                file_info.get('pdf_path', ''), 
                {**org_info, 'filename': filename}
            )
            
            # 更新文本内容
            analysis_result['text_analysis']['statistics'] = self.analysis_engine.text_extractor.get_text_stats(text)
            analysis_result['text_analysis']['content_preview'] = text[:500] + "..." if len(text) > 500 else text
            
        else:
            # 基础分析
            text_stats = self._basic_text_analysis(text)
            analysis_result = {
                'metadata': {
                    **org_info,
                    'filename': filename,
                    'file_size': file_info['csv_size'],
                    'analysis_date': datetime.now().isoformat(),
                    'processing_time': 0.1
                },
                'text_analysis': {
                    'statistics': text_stats,
                    'content_preview': text[:500] + "..." if len(text) > 500 else text
                },
                'sentiment_analysis': {
                    'overall_sentiment': 'neutral',
                    'sentiment_distribution': {'positive': 0.33, 'neutral': 0.34, 'negative': 0.33},
                    'confidence': 0.5
                },
                'moral_dimensions': {
                    'dominant_moral_framework': 'harm_protection',
                    'moral_categories': {
                        'harm_protection': {'total_mentions': 5},
                        'fairness': {'total_mentions': 3},
                        'autonomy': {'total_mentions': 2},
                        'transparency': {'total_mentions': 4}
                    }
                },
                'policy_analysis': {
                    'dominant_preference': 'self_regulation',
                    'policy_preferences': {
                        'self_regulation': 0.4,
                        'co_regulation': 0.3,
                        'government_oversight': 0.2,
                        'international': 0.1
                    }
                }
            }
        
        processing_time = (datetime.now() - start_time).total_seconds()
        analysis_result['metadata']['processing_time'] = processing_time
        
        print(f"✅ 分析完成: {analysis_result['text_analysis']['statistics']['word_count']} 词, {processing_time:.2f}秒")
        
        return analysis_result
    
    def _basic_text_analysis(self, text: str) -> Dict:
        """基础文本分析"""
        if not text:
            return {
                'character_count': 0,
                'word_count': 0,
                'sentence_count': 0,
                'paragraph_count': 0,
                'avg_sentence_length': 0
            }
        
        character_count = len(text)
        words = text.split()
        word_count = len(words)
        sentence_count = text.count('.') + text.count('!') + text.count('?')
        paragraph_count = text.count('\n\n') + 1
        avg_sentence_length = word_count / sentence_count if sentence_count > 0 else 0
        
        return {
            'character_count': character_count,
            'word_count': word_count,
            'sentence_count': sentence_count,
            'paragraph_count': paragraph_count,
            'avg_sentence_length': round(avg_sentence_length, 2)
        }
    
    def save_analysis_to_db(self, analysis_result: Dict):
        """保存分析结果到数据库"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            metadata = analysis_result['metadata']
            text_stats = analysis_result['text_analysis']['statistics']
            sentiment = analysis_result['sentiment_analysis']
            moral = analysis_result['moral_dimensions']
            policy = analysis_result['policy_analysis']
            
            cursor.execute('''
                INSERT OR REPLACE INTO file_analysis (
                    filename, organization_name, organization_type, sector,
                    file_size, analysis_date, character_count, word_count, sentence_count,
                    overall_sentiment, sentiment_positive, sentiment_neutral, sentiment_negative,
                    dominant_moral_framework, moral_harm_protection, moral_fairness, 
                    moral_autonomy, moral_transparency,
                    dominant_policy_preference, policy_self_regulation, policy_co_regulation,
                    policy_government_oversight, policy_international,
                    content_preview, processing_time
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                metadata['filename'],
                metadata['organization_name'],
                metadata['organization_type'],
                metadata['sector'],
                metadata.get('file_size', 0),
                metadata['analysis_date'],
                text_stats['character_count'],
                text_stats['word_count'],
                text_stats['sentence_count'],
                sentiment['overall_sentiment'],
                sentiment['sentiment_distribution'].get('positive', 0),
                sentiment['sentiment_distribution'].get('neutral', 0),
                sentiment['sentiment_distribution'].get('negative', 0),
                moral['dominant_moral_framework'],
                moral['moral_categories'].get('harm_protection', {}).get('total_mentions', 0),
                moral['moral_categories'].get('fairness', {}).get('total_mentions', 0),
                moral['moral_categories'].get('autonomy', {}).get('total_mentions', 0),
                moral['moral_categories'].get('transparency', {}).get('total_mentions', 0),
                policy['dominant_preference'],
                policy['policy_preferences'].get('self_regulation', 0),
                policy['policy_preferences'].get('co_regulation', 0),
                policy['policy_preferences'].get('government_oversight', 0),
                policy['policy_preferences'].get('international', 0),
                analysis_result['text_analysis'].get('content_preview', ''),
                metadata.get('processing_time', 0)
            ))
            
            conn.commit()
    
    def process_all_files(self, limit: Optional[int] = 50) -> Dict:
        """处理所有文件"""
        print(f"🚀 开始处理所有文件 (限制: {limit})")
        
        # 扫描文件
        available_files = self.scan_available_files()
        
        if limit:
            available_files = available_files[:limit]
        
        processed_count = 0
        failed_count = 0
        
        for i, file_info in enumerate(available_files, 1):
            try:
                print(f"\n📄 处理文件 {i}/{len(available_files)}: {file_info['filename']}")
                
                # 分析文件
                analysis_result = self.analyze_file(file_info)
                
                # 保存到数据库
                self.save_analysis_to_db(analysis_result)
                
                processed_count += 1
                
                if processed_count % 10 == 0:
                    print(f"   ✅ 已处理 {processed_count} 个文件...")
                
            except Exception as e:
                print(f"❌ 处理失败: {e}")
                failed_count += 1
        
        return {
            'total_files': len(available_files),
            'processed': processed_count,
            'failed': failed_count,
            'success_rate': f"{(processed_count/len(available_files)*100):.1f}%" if available_files else "0%",
            'database_path': self.db_path
        }

def main():
    """主函数"""
    print("🔍 真实文件分析器")
    print("=" * 50)
    
    analyzer = RealFileAnalyzer()
    
    # 处理文件
    result = analyzer.process_all_files(limit=20)  # 先处理20个文件测试
    
    print(f"\n📊 处理结果:")
    print(f"   总文件数: {result['total_files']}")
    print(f"   成功处理: {result['processed']}")
    print(f"   处理失败: {result['failed']}")
    print(f"   成功率: {result['success_rate']}")
    print(f"   数据库: {result['database_path']}")

if __name__ == "__main__":
    main()
