<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Modules Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-cogs me-2"></i>
            Enhanced Modules Test
        </h1>
        
        <!-- Analysis Results Test -->
        <div class="test-section">
            <h2><i class="fas fa-chart-line me-2"></i>Analysis Results Module Test</h2>
            
            <!-- Analysis Controls -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-filter"></i> Analysis Filters & Controls</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="analysisOrgType" class="form-label">Organization Type</label>
                            <select class="form-select" id="analysisOrgType">
                                <option value="">All Types</option>
                                <option value="corporate">Corporate</option>
                                <option value="academic">Academic</option>
                                <option value="nonprofit">Nonprofit</option>
                                <option value="government">Government</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="analysisSector" class="form-label">Sector</label>
                            <select class="form-select" id="analysisSector">
                                <option value="">All Sectors</option>
                                <option value="technology">Technology</option>
                                <option value="education">Education</option>
                                <option value="healthcare">Healthcare</option>
                                <option value="finance">Finance</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="analysisSentiment" class="form-label">Sentiment</label>
                            <select class="form-select" id="analysisSentiment">
                                <option value="">All Sentiments</option>
                                <option value="positive">Positive</option>
                                <option value="neutral">Neutral</option>
                                <option value="negative">Negative</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="analysisLimit" class="form-label">Results Limit</label>
                            <select class="form-select" id="analysisLimit">
                                <option value="25">25 Results</option>
                                <option value="50">50 Results</option>
                                <option value="100">100 Results</option>
                                <option value="200">200 Results</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <button class="btn btn-primary" onclick="testLoadAnalysisResults()">
                                <i class="fas fa-search"></i> Test Load Analysis Results
                            </button>
                            <button class="btn btn-info ms-2" onclick="testShowAnalysisStatistics()">
                                <i class="fas fa-chart-bar"></i> Test Show Statistics
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Analysis Results Display -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-table"></i> Analysis Results</h5>
                </div>
                <div class="card-body">
                    <div id="analysisContent">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-chart-line fa-3x mb-3"></i>
                            <p>Click "Test Load Analysis Results" to view detailed analysis data.</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Analysis Statistics -->
            <div id="analysisStatistics" class="card mt-4" style="display: none;">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-pie"></i> Analysis Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="row" id="analysisStatsContent">
                        <!-- Statistics will be populated here -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Visualizations Test -->
        <div class="test-section">
            <h2><i class="fas fa-chart-bar me-2"></i>Visualizations Module Test</h2>
            
            <!-- Visualization Controls -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Visualization Controls</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="vizDataSource" class="form-label">Data Source</label>
                            <select class="form-select" id="vizDataSource">
                                <option value="historical">Historical Data</option>
                                <option value="analysis">Analysis Results</option>
                                <option value="combined">Combined Data</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="vizOrgFilter" class="form-label">Organization Filter</label>
                            <select class="form-select" id="vizOrgFilter">
                                <option value="">All Organizations</option>
                                <option value="corporate">Corporate Only</option>
                                <option value="academic">Academic Only</option>
                                <option value="nonprofit">Nonprofit Only</option>
                                <option value="government">Government Only</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="vizChartType" class="form-label">Chart Type</label>
                            <select class="form-select" id="vizChartType">
                                <option value="all">Show All Charts</option>
                                <option value="sentiment">Sentiment Only</option>
                                <option value="moral">Moral Framework Only</option>
                                <option value="policy">Policy Stance Only</option>
                                <option value="distribution">Distribution Only</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-primary mt-4" onclick="testRefreshVisualizations()">
                                <i class="fas fa-sync"></i> Test Refresh Charts
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Charts -->
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-heart"></i> Sentiment Analysis</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="sentimentChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-balance-scale"></i> Moral Framework</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="moralChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-gavel"></i> Policy Stance</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="policyChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-building"></i> Organization Types</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="orgTypeChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-12 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-chart-line"></i> Sentiment Heatmap</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="sentimentHeatmapChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Results -->
        <div class="test-section">
            <h2><i class="fas fa-check-circle me-2"></i>Test Results</h2>
            <div id="testResults" class="alert alert-info">
                <p><strong>Test Status:</strong> Ready to test enhanced modules</p>
                <ul>
                    <li>✅ Analysis Results module with filtering and statistics</li>
                    <li>✅ Enhanced Visualizations with multiple chart types</li>
                    <li>✅ Interactive controls and real-time updates</li>
                    <li>✅ Responsive design and error handling</li>
                </ul>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="dashboard/frontend/dashboard.js"></script>
    <script>
        // Test wrapper functions
        function testLoadAnalysisResults() {
            console.log('🧪 Testing loadAnalysisResults...');
            loadAnalysisResults();
        }
        
        function testShowAnalysisStatistics() {
            console.log('🧪 Testing showAnalysisStatistics...');
            showAnalysisStatistics();
        }
        
        function testRefreshVisualizations() {
            console.log('🧪 Testing refreshVisualizations...');
            refreshVisualizations();
        }
        
        // Initialize test page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Enhanced Modules Test Page Loaded');
            
            // Auto-load visualizations for demo
            setTimeout(() => {
                testRefreshVisualizations();
            }, 1000);
        });
    </script>
</body>
</html>
