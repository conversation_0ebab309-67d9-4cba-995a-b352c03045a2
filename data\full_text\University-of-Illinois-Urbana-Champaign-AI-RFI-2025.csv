﻿content_type,page_number,content,character_count,word_count,metadata_field,metadata_value
document_info,0,Document: University-of-Illinois-Urbana-Champaign-AI-RFI-2025.pdf,0,0,filename,University-of-Illinois-Urbana-Champaign-AI-RFI-2025.pdf
metadata,0,"Comments Received in Response To: Request for Information on the Development of an Artificial Intelligence (AI) Action Plan (""Plan"")",132,19,title,"Comments Received in Response To: Request for Information on the Development of an Artificial Intelligence (AI) Action Plan (""Plan"")"
metadata,0,Adobe Acrobat Pro (64-bit) 25.1.20435,37,5,creator,Adobe Acrobat Pro (64-bit) 25.1.20435
metadata,0,Adobe Acrobat Pro (64-bit) 25.1.20435,37,5,producer,Adobe Acrobat Pro (64-bit) 25.1.20435
metadata,0,D:20250415130733-04'00',23,1,creation_date,D:20250415130733-04'00'
metadata,0,D:20250415130733-04'00',23,1,modification_date,D:20250415130733-04'00'
document_stats,0,"Total pages: 8, Total characters: 20348, Total words: 3043",20348,3043,document_stats,"pages:8,chars:20348,words:3043"
full_text,0,"1 Response to Request for Information on the Development of an Artificial Intelligence (AI) Action Plan Submitted by: Susan Martinis, Vice Chancellor for Research and Innovation, University of Illinois Urbana -Champaign Executive summary The University of Illinois Urbana -Champaign (UIUC) has always been on the pioneering edge of AI research and development and continues as a leader in research and innovation around AI, fundamental science, and STEM education in general. UIUC research focuses on advancing fundamental AI and developing applications of AI in areas such as agriculture, biotechnology, cybersecurity, defense, education, engineering, health, information sciences, manufacturing, social sciences, and workforce development. UIUC has an advanced infrastructure for AI work, expertise , units on campus devoted to research related to AI , and computational resources . UIUC produces thousands of graduates in the STEM and AI space who help form the backbone of the kind of workforce needed for the US to continue to lead the world in AI . The US is the birthplace and leader of AI, but our national leadership is vulnerable due to comparably insufficient production of STEM graduates and limited funding for fundamental AI research . Nearly 100% of high -profile AI engineers at large and small companies emerge from US -based PhD programs; graduate level study in the US serves as a catalyst for innovation, creativity, and economic growth. Increasing production of AI talent requires a multi -pronged approach . Invest in STEM higher education programs to increase capacity, reduce the cost of enrollment for US citizens, and provide compute infrastructure to support teaching AI . Invest in training and professional development of early , secondary , and university educators to provide high -quality education to the next generation of US citizens in the STEM pipeline , ensuring they emerge as universally computing -literate and well -prepared for STEM careers . UIUC College of Education s Illinois Secondary Teacher Education and Computer Science Initiative (I -STECS) has established the Teaching Endorsem ent in Computer Science ( CSTEd) which provides core knowledge and skills in CS to teachers . 2 Maintain and increase incoming international talent to meet the current shortage of US -based talent . Leadership in innovation requires increased investment in research and startups . Additional streamlined funding to initiatives that focus on national priorities . Invest in industrial scale AI-ready computing infrastructure at academic institutions to create opportunit ies for large -scale innovations and their commercialization . The University of Illinois is a leader in AI research and infrastructure and in generating STEM talent University of Illinois Urbana -Champaign (UIUC) is one of the premier engines for basic science and AI research and one of the top universities for US Federal agencies funding (NSF, DOD, DOE, NIH, USDA, etc.). UIUC is a leading provider of computing resources for the National Artificial Intelligence Research Resource (NAIRR) pilot and provides the majority of GPU resources in the NSF ACCESS program, which supplies computing resources to researchers across the country. We have a long history leading national cyberinfrastructure programs such as XSEDE and ACCES S which highlight UIUC s ability to lead and coordinate national cyberinfrastructure initiatives. Recently we received the DOE INCITE award, one of two recent DOE a wards to fund research in AI and high -energy physics. UIUC has eleven Top 10 engineering programs and 8,500 engineering students , including 5,300 students in computer science. Our Applied Machine Learning course tops 1,000 students. Our CS+X programs integrate AI and computing across Chemistry, Crop Sciences, Advertising, Bioengineering, and other disciplines. Similarly, our new X + DS program has 850 students from Astronomy, Business, Accountancy, Finance, and Information Sciences. We are proud of our contribution to America s economy and technology dominance by providing among the largest number and highest quality STEM graduates o f any university in the U nited States . We are home to the Center for AI Innovation that bridges AI research and application, and three National AI Research Institutes focusing o n Molecular Discovery, Education, and Agriculture funded in partnership between NSF, USDA, and the US Department of Education . Additionally, UIUC has AI Centers on Autonomous Construction and 3 Manufacturing, Health Data Systems and Analytics, Scientific Modeling, and Advanced Electronics Through Machine Learning, among others. Our campus has strong partnerships with industry NVIDIA and IBM are partners on a recent INCITE award to cite one example. As well as driving innovation through new use - inspired research , these partnerships support the exchange of faculty and students and help ensure the supply of the US future workforce. UIUC also ha s strong connections and longstanding relationships with national DOE laboratories such as Argonne, Sandia, Oak Ridge, and Fermi National Accelerator. UIUC drives research and innovation across the US STEM landscape and our researchers are well -positioned to continue this leadership in the AI space. Our people are already engaged in helping shape future policies and directions for the AI landscape. Our faculty serve national leadership roles in AI and computing , such as Syed Bahauddin Alam : National Academies of Sciences, Engineering, and Medicine (NASEM) committee on AI foundation models and their impact on scientific discovery and innovation ; Nancy Amato : Board Chair , Computing Research Association ( CRA ) and President, Institute of Electrical and Electronics Engineers (IEEE ) Robotics and Automation Society ; Bill Gropp : American Association for the Advancement of Science (AAAS) Council, Section on Information, Computing, and Communication ; Klara Nahrstedt : Advisory Board , NSF Computer and Information Science and Engineering (CISE ); and Anita Nikolich , NSF Advisory Committee for Cyberinfrastructure. Our alumni include many founders and leaders in major tech firms, including Netflix, PayPal, IBM, Siebel Systems, C3.ai, YouTube, AMD, Tesla, and Microsoft to name a few. The U nited States has a strong but vulnerable position in AI America is the birthplace of AI , from the founding Dartmouth Summer Research Project on AI to the inventions of the first neural network by Dr. Rosenblat and the convolutional network by Dr. Yann LeCun. It is also the home of the early great AI and computing startups that grew from universities into giant tech companies. US early dominance is a product of heavy investment in education and research, an entrepreneurial spirit, and a competition - driven business environment where the best ideas are invented, de veloped, and commercialized. 4 America s AI leadership requires increased investments . China has surpassed the US in the number of AI research publications.1 The US is falling behind China in AI development by large margins.2 Why are we falling behind? A major study of national positioning for AI dominance finds that, while the US is the leader in technology infrastructure, we are way behind China and many other countries in generating the human capital needed for AI success.3 China produces more than 4 times as many STEM graduates as the US and it is now introducing AI to children in elementary schoo l4. Not enough US students are sufficiently prepared or have interest in STEM. Only 20% of US graduates are in STEM vs. 41% of Chinese graduates.5 We are not graduating enough of an AI workforce, and we rely heavily on foreign -born individuals for our high -tech workforce, with roughly 20% of the total workforce and STEM graduates coming from other countries. Falling behind on AI leadership not only impedes our technological advancement, but it also threatens our national security. China has developed AI -driven tools for cyber espionage activities, such as Volt Typhoon, which specifically targets essential US infrastructure6. Cyber activities sponsored by nations for cyber espionage, hacking, and digital attacks on critical infrastructure are expected to cause $27 trillion worth of financial losses by 20277. This massive financial and societal impact highlights the need for the US to develop strong AI -driven cybersecurity measures to protect against such threats. Our campus is a partner in the NSF AI Institute for Agent -based Cyber Threat Intelligence and Operation (ACTION) at the University of California, Santa Barbara. UIUC faculty are providing expertise on large -scale AI powered systems for cybersecurity tasks that involve 1 Acharya, A., & Dunn, B. (2022 -01). Comparing U.S. and Chinese contributions to high -impact AI research. Center for Security and Emerging Technology. https://cset.georgetown.edu/publication/comparing -u-s-and-chinese -contributions -to-high-impact -ai- research/ . 2 Dawson, Gregory S., Kevin C. Desouza, and James S. Denford (2022 -09-22). Understanding Artificial Intelligence Spending by the U.S. Federal Government. Brookings. https://www.brookings.edu/articles/understanding -artificial -intelligence -spending -by-the-u-s-federal - government/ . 3 Dawson, Gregory S., and Kevin C. Desouza (2022 -02-03). How the U.S. Can Dominate in the Race to National AI Supremacy. Brookings. https://www.brookings.edu/articles/how -the-u-s-can-dominate -in-the- race-to-national -ai-supremacy/ . 4 Burleigh , B. (2020, March 10). China s six -year-olds are already being offered AI classes in school in a bid to train the next generation of DeepSeek founders. Fortune . https://fortune.com/2025/03/10/china - school -children -ai-deepseek -liang -wengfeng -estonia -uk-america -south -korea/ 5 Brendan Oliss, B., Cole McFaul, C, & Riddick, J.C. (2023 -11-27). The Global Distribution of STEM Graduates: Which Countries Lead the Way? Center for Security and Emerging Technology. https://cset.georgetown.edu/article/the -global -distribution -of-stem -graduates -which -countries -lead-the- way/. 6 Willett, M. (2024). The strategic utility of cyber operations. Adelphi Series, 64(511 513), 125 170. https://doi.org/10.1080/19445571.2024.2417542 7 Embroker. (2025). Cyberattack statistics 2025. Embroker. https://www.embroker.com/blog/cyber -attack - statistics/ 5 data -driven and logic reasoning models to protect against cyberattacks. Furthermore, our faculty are using their expertise to identify solutions to improve the cyber -defense lifecycle. With attacks becoming more sophisticated and dynamic, AI systems that can adapt and reason in real time are crucial for staying ahead of malicious actors8. Investing in developing a high -tech workforce with relevant skills and competencies , and with critical thinking and creative problem -solving abilities will be crucial for the US to stay ahead of this continually evolving and dynamic technology and be a world leader. Further, threat s to US AI leadership can affect our response to technical and operational challenges such as our ability to integrate AI into cybersecurity frameworks. Lack of human resources with the requisite skills can hinder the speed at which we can build these integrations , which are critical for the security of existing national systems . Bouramdane (202 3)9 noted that 65% of cybersecurity teams struggle to align AI systems with existing legacy infrastructure, with insufficient expertise being one of the main obstacles. This further emphasizes the need to invest in education to sustain a strong AI workforce. Our experience suggests a multi -part strategy: investing in education, AI research and startups, and compute infrastructure. To maintain AI leadership , the US urgently must invest in all levels of education . The US must grow an AI -ready workforce, a generation of leaders and doers that are proficient in math, computing, and digital communication, and have the drive to invent, create, and found the great AI companies of the future. Direct investments to incre ase interest and capacity of STEM programs at the two -year and four -year level are needed, including providing additional fellowships for students attending colleges and unive rsities in STEM disciplines. The US should invest in partner ships with states and universities that already possess and are working to expand upon world class facilities and computing infrastructure for teaching and enabling students to have first -hand experience in developing and using the latest AI methods. Of particular importance are investments by agencies like DoE , NSF , NIH in funding graduate fellowships and early career research programs in STEM disciplines. 8 Siebel School of Computing and Data Science. (2023). NSF Supports New AI Institute for Agent -based Cyber Threat Intelligence and Operation. University of Illinois. https://siebelschool.illinois.edu/news/nsf - supports -new-ai-institute -for-agent -based -cyber -threat -intelligence -and-operation 9 Bouramdane, A. -A. (2023). Cyberattacks in Smart Grids: Challenges and Solving the Multi -Criteria Decision -Making for Cybersecurity Options, Including Ones That Incorporate Artificial Intelligence, Using an Analytical Hierarchy Process. Journal of Cybersecu rity and Privacy, 3(4), 662 705. https://doi.org/10.3390/jcp3040031 6 The need for STEM investments includes all areas of science and engineering . While the first wave of AI is in information retrieval and processing, the greater transformative impact of AI is in its integration with infrastructure and physical systems , and in future currently unknown areas for applied AI research . This will require new materials, computer designs, bioengineering, crop science, transportation systems, building technologies in short, innovation and economic development across sectors. UIUC leads in these areas. Some examples are the ACE Center for Evolvable Computing , with goal s that include devising novel technologies for scalable computing that will substantially improve the performance and energy efficiency of distributed computing in the next decade , ARPA -E fund ing for investigators in the Grainger College of Engine ering to develop an innovative cooling paradigm capable of both minimal energy use and maximum cooling power for future servers , and The Center for Advanced Semiconductor Chips with Accelerated Performance (ASAP) that is working to strengthen US leadership in critical technologies including high -performance computing, advanced manufacturing, 5G and beyond . Federal investments in these kinds of research will help maintain US leadership in this space. Educational investment is also needed outside of STEM degrees. Our nation s greatest talent strength is broadly capable leaders with a combination of domain knowledge, technical skill, and social intelligence. Investments in elementary and secondary education are needed to provide more high school graduates with mast ery of math, critical thinking, and language. This will lead to growth of US applicants to STEM programs, and, just as importantly, a generation of American students that complement domain expertise in thei r major with proficiency in computing and data science. AI is now a fundamental technology, and all students in all majors in US two -year and four -year college programs should have a general education in AI, creating a robust industrial ecosystem and marke t for AI products and services. Immigration is a critical talent resource. As stated, we do not have enough qualified and interested students graduating from US high schools to meet the need for STEM talent. While we must invest in early and secondary education to strengthen the domestic talent pipeline, we should also encourage incoming talent to learn at our universities and stay to strengthen our economy . Along many different measures, the US Higher Education system is the best in the world, by a very large margin. This is a significan t global advantage that is necessary to maintain. The US must invest in AI innovation. All breakthrough ideas in AI (e.g., convnets, deep networks, diffusion, transformers, ImageNet) have come from university, non -profit, and industry research labs by scientists aiming to crack the fundamental problems in machine 7 learning and general intelligence. Today, a few tech companies focused on scaling existing approaches dominate spending on AI development. The US must invest in ecosystems that will spawn the innovations that lead to the next generation of AI to ensure it remains the global leader in AI . To maximize innovation potential , it is crucial to support leading researchers through federal funding and independence to drive new areas of innovation and technology . Increase and streamline funding for fundamental research and startups. The investments in research by the federal government have not kept pace with the full economic cost of research. To ensure innovation is supported across all regions of the US to benefit all Americans, overall funding for AI and other computational science research must be increased. For instance, NSF programs, such as CAREER, Robust Intelligence, and DOD programs, such as MURIs and Long Range grants, are crucial for supporting fundamental research and training graduate students. However, funding for fundamental AI research is more difficult to obtain for successful researchers at top -ranked universities today than 20 years ago. Further, onerous grant preparation and review processe s, combined with low award rates, sometimes mean that the total cost to propose and review often nearly ma tches the total amount awarded. Expand these programs, but streamline the proposal and review process, strip extraneous requirements, and increase funding, concentrating on successful researchers, bold proposals, and research that addresses national priori ties. For example, an impact -focused program could implement fast decisions on short proposals for short -term awards ( e.g., 2 years) with potential for result -based renewals. Other DoD programs, such as DARPA, that focus on translational research are successful when they specify an audacious problem and evaluation framework and leave the solution to investigators. A notable example is the DARPA Grand Challenge for autonomous vehicles. The US should continue these pr ograms with an emphasis on well -defined objectives , independent solutions , and efficient and effective reporting processes . SBIRs are a valuable resource for startups, supporting early stage commercialization of AI breakthroughs. However, spending constraints and performance requirements unrelated to commercial success reduce the SBIR s effectiveness for explosive startup grow th. SBIR programs should be expanded in funding with fewer restrictions and obligations and follow -on funding based on commercial traction. 8 New infrastructure is needed to support innovation. The comput ing costs of AI research have grown tremendously, far outstripping grant support. Despite recent investments in the computational infrastructure for training complex AI models, e.g., through programs such as the NAIRR Pilot, academic research labs have much less computational power than some privately funded facilities that are only available for commercial users. This imbalance precludes the academic researchers from committing to ambitious projects even if they have the groundbreaking ideas. Access to industr ial-scale AI infrastructure is also essential for training students at academic institutions to think ambitiously as future researchers and entrepreneurs. Funding the deployment and operation of AI -ready computational infrastructure at academic research institutions and providing researchers with access to the industrial -scale facilities, such as those to be built by the Stargate initiative, is crucial for research and for successfully training the future STEM workforce. This will also foster innovative AI research and its commercialization in the country . _____________________ This document is approved for public dissemination. The document contains no business - proprietary or confidential information. Document contents may be reused by the government in developing the AI Action Plan and associated documents without attribution.",20348,3043,full_document_text,
page_text,1,"1 Response to Request for Information on the Development of an Artificial Intelligence (AI) Action Plan Submitted by: Susan Martinis, Vice Chancellor for Research and Innovation, University of Illinois Urbana -Champaign Executive summary The University of Illinois Urbana -Champaign (UIUC) has always been on the pioneering edge of AI research and development and continues as a leader in research and innovation around AI, fundamental science, and STEM education in general. UIUC research focuses on advancing fundamental AI and developing applications of AI in areas such as agriculture, biotechnology, cybersecurity, defense, education, engineering, health, information sciences, manufacturing, social sciences, and workforce development. UIUC has an advanced infrastructure for AI work, expertise , units on campus devoted to research related to AI , and computational resources . UIUC produces thousands of graduates in the STEM and AI space who help form the backbone of the kind of workforce needed for the US to continue to lead the world in AI . The US is the birthplace and leader of AI, but our national leadership is vulnerable due to comparably insufficient production of STEM graduates and limited funding for fundamental AI research . Nearly 100% of high -profile AI engineers at large and small companies emerge from US -based PhD programs; graduate level study in the US serves as a catalyst for innovation, creativity, and economic growth. Increasing production of AI talent requires a multi -pronged approach . Invest in STEM higher education programs to increase capacity, reduce the cost of enrollment for US citizens, and provide compute infrastructure to support teaching AI . Invest in training and professional development of early , secondary , and university educators to provide high -quality education to the next generation of US citizens in the STEM pipeline , ensuring they emerge as universally computing -literate and well -prepared for STEM careers . UIUC College of Education s Illinois Secondary Teacher Education and Computer Science Initiative (I -STECS) has established the Teaching Endorsem ent in Computer Science ( CSTEd) which provides core knowledge and skills in CS to teachers .",2225,343,page_content,page_1
page_text,2,"2 Maintain and increase incoming international talent to meet the current shortage of US -based talent . Leadership in innovation requires increased investment in research and startups . Additional streamlined funding to initiatives that focus on national priorities . Invest in industrial scale AI-ready computing infrastructure at academic institutions to create opportunit ies for large -scale innovations and their commercialization . The University of Illinois is a leader in AI research and infrastructure and in generating STEM talent University of Illinois Urbana -Champaign (UIUC) is one of the premier engines for basic science and AI research and one of the top universities for US Federal agencies funding (NSF, DOD, DOE, NIH, USDA, etc.). UIUC is a leading provider of computing resources for the National Artificial Intelligence Research Resource (NAIRR) pilot and provides the majority of GPU resources in the NSF ACCESS program, which supplies computing resources to researchers across the country. We have a long history leading national cyberinfrastructure programs such as XSEDE and ACCES S which highlight UIUC s ability to lead and coordinate national cyberinfrastructure initiatives. Recently we received the DOE INCITE award, one of two recent DOE a wards to fund research in AI and high -energy physics. UIUC has eleven Top 10 engineering programs and 8,500 engineering students , including 5,300 students in computer science. Our Applied Machine Learning course tops 1,000 students. Our CS+X programs integrate AI and computing across Chemistry, Crop Sciences, Advertising, Bioengineering, and other disciplines. Similarly, our new X + DS program has 850 students from Astronomy, Business, Accountancy, Finance, and Information Sciences. We are proud of our contribution to America s economy and technology dominance by providing among the largest number and highest quality STEM graduates o f any university in the U nited States . We are home to the Center for AI Innovation that bridges AI research and application, and three National AI Research Institutes focusing o n Molecular Discovery, Education, and Agriculture funded in partnership between NSF, USDA, and the US Department of Education . Additionally, UIUC has AI Centers on Autonomous Construction and",2289,346,page_content,page_2
page_text,3,"3 Manufacturing, Health Data Systems and Analytics, Scientific Modeling, and Advanced Electronics Through Machine Learning, among others. Our campus has strong partnerships with industry NVIDIA and IBM are partners on a recent INCITE award to cite one example. As well as driving innovation through new use - inspired research , these partnerships support the exchange of faculty and students and help ensure the supply of the US future workforce. UIUC also ha s strong connections and longstanding relationships with national DOE laboratories such as Argonne, Sandia, Oak Ridge, and Fermi National Accelerator. UIUC drives research and innovation across the US STEM landscape and our researchers are well -positioned to continue this leadership in the AI space. Our people are already engaged in helping shape future policies and directions for the AI landscape. Our faculty serve national leadership roles in AI and computing , such as Syed Bahauddin Alam : National Academies of Sciences, Engineering, and Medicine (NASEM) committee on AI foundation models and their impact on scientific discovery and innovation ; Nancy Amato : Board Chair , Computing Research Association ( CRA ) and President, Institute of Electrical and Electronics Engineers (IEEE ) Robotics and Automation Society ; Bill Gropp : American Association for the Advancement of Science (AAAS) Council, Section on Information, Computing, and Communication ; Klara Nahrstedt : Advisory Board , NSF Computer and Information Science and Engineering (CISE ); and Anita Nikolich , NSF Advisory Committee for Cyberinfrastructure. Our alumni include many founders and leaders in major tech firms, including Netflix, PayPal, IBM, Siebel Systems, C3.ai, YouTube, AMD, Tesla, and Microsoft to name a few. The U nited States has a strong but vulnerable position in AI America is the birthplace of AI , from the founding Dartmouth Summer Research Project on AI to the inventions of the first neural network by Dr. Rosenblat and the convolutional network by Dr. Yann LeCun. It is also the home of the early great AI and computing startups that grew from universities into giant tech companies. US early dominance is a product of heavy investment in education and research, an entrepreneurial spirit, and a competition - driven business environment where the best ideas are invented, de veloped, and commercialized.",2371,369,page_content,page_3
page_text,4,"4 America s AI leadership requires increased investments . China has surpassed the US in the number of AI research publications.1 The US is falling behind China in AI development by large margins.2 Why are we falling behind? A major study of national positioning for AI dominance finds that, while the US is the leader in technology infrastructure, we are way behind China and many other countries in generating the human capital needed for AI success.3 China produces more than 4 times as many STEM graduates as the US and it is now introducing AI to children in elementary schoo l4. Not enough US students are sufficiently prepared or have interest in STEM. Only 20% of US graduates are in STEM vs. 41% of Chinese graduates.5 We are not graduating enough of an AI workforce, and we rely heavily on foreign -born individuals for our high -tech workforce, with roughly 20% of the total workforce and STEM graduates coming from other countries. Falling behind on AI leadership not only impedes our technological advancement, but it also threatens our national security. China has developed AI -driven tools for cyber espionage activities, such as Volt Typhoon, which specifically targets essential US infrastructure6. Cyber activities sponsored by nations for cyber espionage, hacking, and digital attacks on critical infrastructure are expected to cause $27 trillion worth of financial losses by 20277. This massive financial and societal impact highlights the need for the US to develop strong AI -driven cybersecurity measures to protect against such threats. Our campus is a partner in the NSF AI Institute for Agent -based Cyber Threat Intelligence and Operation (ACTION) at the University of California, Santa Barbara. UIUC faculty are providing expertise on large -scale AI powered systems for cybersecurity tasks that involve 1 Acharya, A., & Dunn, B. (2022 -01). Comparing U.S. and Chinese contributions to high -impact AI research. Center for Security and Emerging Technology. https://cset.georgetown.edu/publication/comparing -u-s-and-chinese -contributions -to-high-impact -ai- research/ . 2 Dawson, Gregory S., Kevin C. Desouza, and James S. Denford (2022 -09-22). Understanding Artificial Intelligence Spending by the U.S. Federal Government. Brookings. https://www.brookings.edu/articles/understanding -artificial -intelligence -spending -by-the-u-s-federal - government/ . 3 Dawson, Gregory S., and Kevin C. Desouza (2022 -02-03). How the U.S. Can Dominate in the Race to National AI Supremacy. Brookings. https://www.brookings.edu/articles/how -the-u-s-can-dominate -in-the- race-to-national -ai-supremacy/ . 4 Burleigh , B. (2020, March 10). China s six -year-olds are already being offered AI classes in school in a bid to train the next generation of DeepSeek founders. Fortune . https://fortune.com/2025/03/10/china - school -children -ai-deepseek -liang -wengfeng -estonia -uk-america -south -korea/ 5 Brendan Oliss, B., Cole McFaul, C, & Riddick, J.C. (2023 -11-27). The Global Distribution of STEM Graduates: Which Countries Lead the Way? Center for Security and Emerging Technology. https://cset.georgetown.edu/article/the -global -distribution -of-stem -graduates -which -countries -lead-the- way/. 6 Willett, M. (2024). The strategic utility of cyber operations. Adelphi Series, 64(511 513), 125 170. https://doi.org/10.1080/19445571.2024.2417542 7 Embroker. (2025). Cyberattack statistics 2025. Embroker. https://www.embroker.com/blog/cyber -attack - statistics/",3489,493,page_content,page_4
page_text,5,"5 data -driven and logic reasoning models to protect against cyberattacks. Furthermore, our faculty are using their expertise to identify solutions to improve the cyber -defense lifecycle. With attacks becoming more sophisticated and dynamic, AI systems that can adapt and reason in real time are crucial for staying ahead of malicious actors8. Investing in developing a high -tech workforce with relevant skills and competencies , and with critical thinking and creative problem -solving abilities will be crucial for the US to stay ahead of this continually evolving and dynamic technology and be a world leader. Further, threat s to US AI leadership can affect our response to technical and operational challenges such as our ability to integrate AI into cybersecurity frameworks. Lack of human resources with the requisite skills can hinder the speed at which we can build these integrations , which are critical for the security of existing national systems . Bouramdane (202 3)9 noted that 65% of cybersecurity teams struggle to align AI systems with existing legacy infrastructure, with insufficient expertise being one of the main obstacles. This further emphasizes the need to invest in education to sustain a strong AI workforce. Our experience suggests a multi -part strategy: investing in education, AI research and startups, and compute infrastructure. To maintain AI leadership , the US urgently must invest in all levels of education . The US must grow an AI -ready workforce, a generation of leaders and doers that are proficient in math, computing, and digital communication, and have the drive to invent, create, and found the great AI companies of the future. Direct investments to incre ase interest and capacity of STEM programs at the two -year and four -year level are needed, including providing additional fellowships for students attending colleges and unive rsities in STEM disciplines. The US should invest in partner ships with states and universities that already possess and are working to expand upon world class facilities and computing infrastructure for teaching and enabling students to have first -hand experience in developing and using the latest AI methods. Of particular importance are investments by agencies like DoE , NSF , NIH in funding graduate fellowships and early career research programs in STEM disciplines. 8 Siebel School of Computing and Data Science. (2023). NSF Supports New AI Institute for Agent -based Cyber Threat Intelligence and Operation. University of Illinois. https://siebelschool.illinois.edu/news/nsf - supports -new-ai-institute -for-agent -based -cyber -threat -intelligence -and-operation 9 Bouramdane, A. -A. (2023). Cyberattacks in Smart Grids: Challenges and Solving the Multi -Criteria Decision -Making for Cybersecurity Options, Including Ones That Incorporate Artificial Intelligence, Using an Analytical Hierarchy Process. Journal of Cybersecu rity and Privacy, 3(4), 662 705. https://doi.org/10.3390/jcp3040031",2990,446,page_content,page_5
page_text,6,"6 The need for STEM investments includes all areas of science and engineering . While the first wave of AI is in information retrieval and processing, the greater transformative impact of AI is in its integration with infrastructure and physical systems , and in future currently unknown areas for applied AI research . This will require new materials, computer designs, bioengineering, crop science, transportation systems, building technologies in short, innovation and economic development across sectors. UIUC leads in these areas. Some examples are the ACE Center for Evolvable Computing , with goal s that include devising novel technologies for scalable computing that will substantially improve the performance and energy efficiency of distributed computing in the next decade , ARPA -E fund ing for investigators in the Grainger College of Engine ering to develop an innovative cooling paradigm capable of both minimal energy use and maximum cooling power for future servers , and The Center for Advanced Semiconductor Chips with Accelerated Performance (ASAP) that is working to strengthen US leadership in critical technologies including high -performance computing, advanced manufacturing, 5G and beyond . Federal investments in these kinds of research will help maintain US leadership in this space. Educational investment is also needed outside of STEM degrees. Our nation s greatest talent strength is broadly capable leaders with a combination of domain knowledge, technical skill, and social intelligence. Investments in elementary and secondary education are needed to provide more high school graduates with mast ery of math, critical thinking, and language. This will lead to growth of US applicants to STEM programs, and, just as importantly, a generation of American students that complement domain expertise in thei r major with proficiency in computing and data science. AI is now a fundamental technology, and all students in all majors in US two -year and four -year college programs should have a general education in AI, creating a robust industrial ecosystem and marke t for AI products and services. Immigration is a critical talent resource. As stated, we do not have enough qualified and interested students graduating from US high schools to meet the need for STEM talent. While we must invest in early and secondary education to strengthen the domestic talent pipeline, we should also encourage incoming talent to learn at our universities and stay to strengthen our economy . Along many different measures, the US Higher Education system is the best in the world, by a very large margin. This is a significan t global advantage that is necessary to maintain. The US must invest in AI innovation. All breakthrough ideas in AI (e.g., convnets, deep networks, diffusion, transformers, ImageNet) have come from university, non -profit, and industry research labs by scientists aiming to crack the fundamental problems in machine",2959,459,page_content,page_6
page_text,7,"7 learning and general intelligence. Today, a few tech companies focused on scaling existing approaches dominate spending on AI development. The US must invest in ecosystems that will spawn the innovations that lead to the next generation of AI to ensure it remains the global leader in AI . To maximize innovation potential , it is crucial to support leading researchers through federal funding and independence to drive new areas of innovation and technology . Increase and streamline funding for fundamental research and startups. The investments in research by the federal government have not kept pace with the full economic cost of research. To ensure innovation is supported across all regions of the US to benefit all Americans, overall funding for AI and other computational science research must be increased. For instance, NSF programs, such as CAREER, Robust Intelligence, and DOD programs, such as MURIs and Long Range grants, are crucial for supporting fundamental research and training graduate students. However, funding for fundamental AI research is more difficult to obtain for successful researchers at top -ranked universities today than 20 years ago. Further, onerous grant preparation and review processe s, combined with low award rates, sometimes mean that the total cost to propose and review often nearly ma tches the total amount awarded. Expand these programs, but streamline the proposal and review process, strip extraneous requirements, and increase funding, concentrating on successful researchers, bold proposals, and research that addresses national priori ties. For example, an impact -focused program could implement fast decisions on short proposals for short -term awards ( e.g., 2 years) with potential for result -based renewals. Other DoD programs, such as DARPA, that focus on translational research are successful when they specify an audacious problem and evaluation framework and leave the solution to investigators. A notable example is the DARPA Grand Challenge for autonomous vehicles. The US should continue these pr ograms with an emphasis on well -defined objectives , independent solutions , and efficient and effective reporting processes . SBIRs are a valuable resource for startups, supporting early stage commercialization of AI breakthroughs. However, spending constraints and performance requirements unrelated to commercial success reduce the SBIR s effectiveness for explosive startup grow th. SBIR programs should be expanded in funding with fewer restrictions and obligations and follow -on funding based on commercial traction.",2591,387,page_content,page_7
page_text,8,"8 New infrastructure is needed to support innovation. The comput ing costs of AI research have grown tremendously, far outstripping grant support. Despite recent investments in the computational infrastructure for training complex AI models, e.g., through programs such as the NAIRR Pilot, academic research labs have much less computational power than some privately funded facilities that are only available for commercial users. This imbalance precludes the academic researchers from committing to ambitious projects even if they have the groundbreaking ideas. Access to industr ial-scale AI infrastructure is also essential for training students at academic institutions to think ambitiously as future researchers and entrepreneurs. Funding the deployment and operation of AI -ready computational infrastructure at academic research institutions and providing researchers with access to the industrial -scale facilities, such as those to be built by the Stargate initiative, is crucial for research and for successfully training the future STEM workforce. This will also foster innovative AI research and its commercialization in the country . _____________________ This document is approved for public dissemination. The document contains no business - proprietary or confidential information. Document contents may be reused by the government in developing the AI Action Plan and associated documents without attribution.",1427,200,page_content,page_8
