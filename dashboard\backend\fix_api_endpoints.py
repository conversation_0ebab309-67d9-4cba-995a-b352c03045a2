#!/usr/bin/env python3
"""
API端点修复工具
统一所有API的健康检查端点格式，使其与前端期望的格式一致

Author: Claude Code
Date: August 13, 2025
"""

import os
import sys
import re
import glob

# API端点映射
API_ENDPOINTS = {
    "visualization_api.py": {
        "old_route": "/api/health",
        "new_route": "/api/visualize/health",
        "name": "Visualization API",
        "port": 5001
    },
    "analysis_results_api.py": {
        "old_route": "/api/health", 
        "new_route": "/api/historical/health",
        "name": "Historical Data API",
        "port": 5003
    },
    "batch_processing_api.py": {
        "old_route": None,  # 新创建的API已有正确端点
        "new_route": "/api/batch/health",
        "name": "Batch Processing API",
        "port": 5007
    },
    "advanced_analytics_api.py": {
        "old_route": "/api/analytics/health",
        "new_route": "/api/analytics/health",  # 已经正确
        "name": "Analytics API",
        "port": 5005
    },
    "search_api.py": {
        "old_route": "/api/health",
        "new_route": "/api/search/health",
        "name": "Search API",
        "port": 5002
    }
}

def find_api_files(directory):
    """查找后端目录中的所有API文件"""
    all_api_files = glob.glob(os.path.join(directory, "*api*.py"))
    api_files = {}
    
    # 匹配已知API文件
    for api_name in API_ENDPOINTS.keys():
        for file_path in all_api_files:
            if os.path.basename(file_path) == api_name:
                api_files[api_name] = file_path
                break
    
    return api_files

def fix_health_endpoint(file_path, api_info):
    """修复API的健康检查端点"""
    if not os.path.exists(file_path):
        print(f"错误: 找不到文件 {file_path}")
        return False
        
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 创建备份
    backup_path = file_path + '.endpoint_fix.bak'
    if not os.path.exists(backup_path):
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
            
    # 如果没有旧路由，则不需要修改
    if api_info["old_route"] is None:
        print(f"跳过 {os.path.basename(file_path)}: 没有需要修改的旧路由")
        return True
        
    # 查找路由定义
    old_route_pattern = re.escape(api_info["old_route"])
    route_def_pattern = f"@app.route\\(['\\\"]({old_route_pattern})['\\\"]"
    
    # Flask应用可能使用app或self.app
    alt_route_def_pattern = f"@self.app.route\\(['\\\"]({old_route_pattern})['\\\"]"
    
    # 查找并替换路由
    replaced = False
    if re.search(route_def_pattern, content):
        modified_content = re.sub(
            route_def_pattern,
            f"@app.route('{api_info['new_route']}'",
            content
        )
        replaced = True
    elif re.search(alt_route_def_pattern, content):
        modified_content = re.sub(
            alt_route_def_pattern,
            f"@self.app.route('{api_info['new_route']}'",
            content
        )
        replaced = True
    else:
        # 健康检查端点可能不存在，添加一个新的
        if "app = Flask(__name__)" in content:
            # 为标准Flask应用添加健康检查端点
            endpoint_code = f'''
@app.route('{api_info["new_route"]}', methods=['GET'])
def health_check():
    return jsonify({{
        'status': 'healthy',
        'service': '{api_info["name"]}',
        'version': '1.0',
        'port': {api_info["port"]}
    }})
'''
            # 在app定义后插入端点
            modified_content = content.replace(
                "app = Flask(__name__)",
                f"app = Flask(__name__){endpoint_code}"
            )
            replaced = True
        else:
            print(f"警告: 在 {os.path.basename(file_path)} 中找不到合适的位置添加健康检查端点")
            return False
            
    if replaced:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        print(f"已修复 {os.path.basename(file_path)} 的健康检查端点: {api_info['new_route']}")
        return True
    else:
        print(f"警告: 无法修复 {os.path.basename(file_path)} 的健康检查端点")
        return False

def create_api_restart_script(api_files):
    """创建重启所有API服务的批处理脚本"""
    script_path = os.path.join(os.path.dirname(os.path.dirname(api_files[next(iter(api_files))])), "restart_apis.bat")
    
    script_content = '''@echo off
echo Restarting API services for AI Policy Analyzer...

REM 停止现有服务
echo Stopping existing services...
taskkill /F /FI "WINDOWTITLE eq Analytics API*" 2>NUL
taskkill /F /FI "WINDOWTITLE eq Visualization API*" 2>NUL
taskkill /F /FI "WINDOWTITLE eq Historical Data API*" 2>NUL
taskkill /F /FI "WINDOWTITLE eq Search API*" 2>NUL
taskkill /F /FI "WINDOWTITLE eq Batch Processing API*" 2>NUL
timeout /t 2 /nobreak > NUL

REM 启动API服务
cd /d "%~dp0"
'''
    
    for api_name, file_path in api_files.items():
        api_info = API_ENDPOINTS.get(api_name)
        if api_info:
            script_content += f'\necho Starting {api_info["name"]} on port {api_info["port"]}...\n'
            script_content += f'start "{api_info["name"]}" cmd /k python "{os.path.basename(file_path)}"\n'
            script_content += 'timeout /t 1 /nobreak > NUL\n'
    
    script_content += '''
echo All API services have been restarted.
echo Dashboard should now be able to connect to all API endpoints.
'''
    
    with open(script_path, 'w') as f:
        f.write(script_content)
        
    print(f"已创建API重启脚本: {script_path}")
    return script_path

def update_dashboard_api_connectivity(dashboard_dir):
    """确保仪表盘的API连接检查与后端API端点匹配"""
    js_file = os.path.join(dashboard_dir, 'frontend', 'dashboard_enhancements.js')
    
    if not os.path.exists(js_file):
        print(f"错误: 找不到文件 {js_file}")
        return False
        
    with open(js_file, 'r', encoding='utf-8') as f:
        content = f.read()
        
    # 确保API端点配置正确
    # 这段代码主要检查逻辑，不做实际修改，因为我们已经更新了后端API端点
    apis = {
        'visualization': 5001,
        'search': 5002,
        'historical': 5003,
        'analytics': 5005,
        'batch': 5007
    }
    
    for api, port in apis.items():
        endpoint_pattern = f"{api}ApiEndpoint\\s*=\\s*['\\\"](http://[^/]*:{port})['\\\"]"
        if not re.search(endpoint_pattern, content):
            print(f"警告: 仪表盘中的{api}ApiEndpoint可能配置不正确")
    
    print("已验证仪表盘API连接配置")
    return True

def main():
    """主函数"""
    print("API端点修复工具")
    print("="*40)
    
    # 获取脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    dashboard_dir = os.path.dirname(current_dir)
    
    # 查找API文件
    api_files = find_api_files(current_dir)
    if not api_files:
        print("错误: 找不到任何API文件")
        return 1
        
    print(f"找到 {len(api_files)} 个API文件:")
    for api_name in api_files:
        print(f"- {api_name}")
        
    # 修复API端点
    fixed_apis = []
    for api_name, file_path in api_files.items():
        if api_name in API_ENDPOINTS:
            if fix_health_endpoint(file_path, API_ENDPOINTS[api_name]):
                fixed_apis.append(api_name)
                
    print(f"\n已修复 {len(fixed_apis)} 个API的健康检查端点:")
    for api in fixed_apis:
        print(f"- {api} -> {API_ENDPOINTS[api]['new_route']}")
        
    # 创建重启脚本
    restart_script = create_api_restart_script(api_files)
    
    # 验证仪表盘API连接配置
    update_dashboard_api_connectivity(dashboard_dir)
    
    print("\n修复完成!")
    print("请运行重启脚本重新启动所有API服务:")
    print(f"  {restart_script}")
    print("="*40)
    
    # 自动运行重启脚本
    os.system(f'start {restart_script}')
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
