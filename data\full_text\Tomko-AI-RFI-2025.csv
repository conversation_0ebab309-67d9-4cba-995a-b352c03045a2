﻿content_type,page_number,content,character_count,word_count,metadata_field,metadata_value
document_info,0,Document: Tomko-AI-RFI-2025.pdf,0,0,filename,Tomko-AI-RFI-2025.pdf
metadata,0,"Comments Received in Response To: Request for Information on the Development of an Artificial Intelligence (AI) Action Plan (""Plan"")",132,19,title,"Comments Received in Response To: Request for Information on the Development of an Artificial Intelligence (AI) Action Plan (""Plan"")"
metadata,0,Adobe Acrobat Pro (64-bit) 25.1.20435,37,5,creator,Adobe Acrobat Pro (64-bit) 25.1.20435
metadata,0,Adobe Acrobat Pro (64-bit) 25.1.20435,37,5,producer,Adobe Acrobat Pro (64-bit) 25.1.20435
metadata,0,D:20250415130611-04'00',23,1,creation_date,D:20250415130611-04'00'
metadata,0,D:20250415130611-04'00',23,1,modification_date,D:20250415130611-04'00'
document_stats,0,"Total pages: 1, Total characters: 1978, Total words: 338",1978,338,document_stats,"pages:1,chars:1978,words:338"
full_text,0,"3/13/2025 via FDMS Lorinda Tomko, GenAI is not only a massive drain on our already-tenuous power grid, it runs only by allowing copyright infringement and is still in such a state as to make dangerous, obvious errors. AI data centers should not be approved in places where they would overburden existing energy needs. Additionally, there is no good reason given to grant genAI companies a sweeping exemption from data privacy laws or copyright infringement; it has proven to have security weakness in handling private data, and by utilizing copyr ighted works it steals from the livelihoods of actual citizens in a variety of fields while polluting its own well of data. These protections are in place for a reason, and there is no economic emergency occurring (at least not related to AI innovation..) to justify making these exceptions now. GenAI, particularly LLMs, are being used in a slapdash way and without understanding of their actual function. This is not a system that can fact check itself. It will create and spread misinformation, as it already has. It's simply a program that tries to give the most- expected response in a grammatically correct way. I am strongly opposed to prioritizing AI data centers in energy usage or regulatory exemptions, not only as a US citizen concerned about the security o f my private data, but also as someone who works with PHI at one job and as a creative visual artist in the other. GenAI currently cannot even accurately do what its salespeople claim it can. Residents in my area are already alarmed at the news coming out this year on potential failures of the power grid due to demand. Recall the blockchain hype. Recall NFTs. Do we forget this quickly not to throw necessary resources away? While I find some forms of predictive AI to be an exciting tool in the medical field, in other applications it needs to come a long way before it's worthwhile to prioritize over known return on value. Thank you for your consideration.",1978,338,full_document_text,
page_text,1,"3/13/2025 via FDMS Lorinda Tomko, GenAI is not only a massive drain on our already-tenuous power grid, it runs only by allowing copyright infringement and is still in such a state as to make dangerous, obvious errors. AI data centers should not be approved in places where they would overburden existing energy needs. Additionally, there is no good reason given to grant genAI companies a sweeping exemption from data privacy laws or copyright infringement; it has proven to have security weakness in handling private data, and by utilizing copyr ighted works it steals from the livelihoods of actual citizens in a variety of fields while polluting its own well of data. These protections are in place for a reason, and there is no economic emergency occurring (at least not related to AI innovation..) to justify making these exceptions now. GenAI, particularly LLMs, are being used in a slapdash way and without understanding of their actual function. This is not a system that can fact check itself. It will create and spread misinformation, as it already has. It's simply a program that tries to give the most- expected response in a grammatically correct way. I am strongly opposed to prioritizing AI data centers in energy usage or regulatory exemptions, not only as a US citizen concerned about the security o f my private data, but also as someone who works with PHI at one job and as a creative visual artist in the other. GenAI currently cannot even accurately do what its salespeople claim it can. Residents in my area are already alarmed at the news coming out this year on potential failures of the power grid due to demand. Recall the blockchain hype. Recall NFTs. Do we forget this quickly not to throw necessary resources away? While I find some forms of predictive AI to be an exciting tool in the medical field, in other applications it needs to come a long way before it's worthwhile to prioritize over known return on value. Thank you for your consideration.",1978,338,page_content,page_1
