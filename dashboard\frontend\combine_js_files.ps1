# PowerShell script to combine dashboard JavaScript files
# Navigate to the directory where the files are located
cd $PSScriptRoot

# Combine the files in order into the final complete JS file
Get-Content "dashboard_complete.js", "dashboard_complete_part2.js", "dashboard_complete_part3.js", "dashboard_complete_part4.js", "dashboard_complete_part5.js", "dashboard_complete_part6.js", "dashboard_complete_part7.js", "dashboard_complete_part8.js" | Set-Content "dashboard_complete_final.js"

Write-Output "JavaScript files have been successfully combined into dashboard_complete_final.js"
