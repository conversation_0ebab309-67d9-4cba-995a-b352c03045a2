#!/usr/bin/env python3
"""
Historical Data API Endpoints for AI Policy Analyzer Dashboard
Flask API wrapper for historical dataset browsing and management

Author: Claude Code
Date: July 31, 2025
"""

import json
import os
import sys
from typing import Dict, List, Any, Optional
from datetime import datetime
from flask import Flask, jsonify, request
from flask_cors import CORS

# Add path for imports
sys.path.append('.')

app = Flask(__name__)
CORS(app)

# Initialize historical data system with enhanced error handling
try:
    from historical_data_integration import initialize_historical_data_system
    historical_api = initialize_historical_data_system()
    print("[OK] Historical data API initialized")
except ImportError as e:
    print(f"[WARN] Import error for historical data API: {e}")
    print("[INFO] Running in limited mode without historical data integration")
    historical_api = None
except Exception as e:
    print(f"[ERROR] Failed to initialize historical data API: {e}")
    print("[INFO] Running in limited mode - some features may not be available")
    historical_api = None

@app.route('/api/historical/load', methods=['POST'])
def load_historical_dataset():
    """Load and index historical dataset"""
    try:
        if not historical_api:
            return jsonify({'error': 'Historical data system not available'}), 503
        
        data = request.json or {}
        limit = data.get('limit', 50)
        
        if limit > 500:
            return jsonify({'error': 'Limit cannot exceed 500 documents'}), 400
        
        result = historical_api.load_historical_dataset(limit)
        
        return jsonify({
            'message': 'Historical dataset loaded successfully',
            'statistics': result
        })
        
    except Exception as e:
        return jsonify({'error': f'Failed to load historical dataset: {str(e)}'}), 500

@app.route('/api/historical/browse', methods=['GET'])
def browse_historical_data():
    """Browse historical dataset with pagination and filters"""
    try:
        if not historical_api:
            return jsonify({'error': 'Historical data system not available'}), 503
        
        # Get query parameters
        limit = int(request.args.get('limit', 20))
        offset = int(request.args.get('offset', 0))
        
        # Build filters
        filters = {}
        if request.args.get('organization_type'):
            filters['organization_type'] = request.args.get('organization_type')
        if request.args.get('sector'):
            filters['sector'] = request.args.get('sector')
        
        # Validate parameters
        if limit > 100:
            return jsonify({'error': 'Limit cannot exceed 100'}), 400
        
        if offset < 0:
            return jsonify({'error': 'Offset cannot be negative'}), 400
        
        result = historical_api.browse_historical_data(filters, limit, offset)
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'error': f'Failed to browse historical data: {str(e)}'}), 500

@app.route('/api/historical/statistics', methods=['GET'])
def get_historical_statistics():
    """Get statistics about the historical dataset"""
    try:
        if not historical_api:
            return jsonify({'error': 'Historical data system not available'}), 503
        
        stats = historical_api.get_historical_statistics()
        
        return jsonify(stats)
        
    except Exception as e:
        return jsonify({'error': f'Failed to get historical statistics: {str(e)}'}), 500

@app.route('/api/historical/organizations', methods=['GET'])
def get_historical_organizations():
    """Get list of organizations in historical dataset"""
    try:
        if not historical_api:
            return jsonify({'error': 'Historical data system not available'}), 503
        
        import sqlite3
        conn = sqlite3.connect(historical_api.search_index.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT DISTINCT organization_name, organization_type, sector, COUNT(*) as document_count
            FROM documents
            WHERE organization_name != '' AND organization_name IS NOT NULL
            GROUP BY organization_name, organization_type, sector
            ORDER BY organization_name
            LIMIT 500
        ''')
        
        results = cursor.fetchall()
        conn.close()
        
        organizations = []
        for row in results:
            organizations.append({
                'organization_name': row[0],
                'organization_type': row[1],
                'sector': row[2],
                'document_count': row[3]
            })
        
        return jsonify({
            'organizations': organizations,
            'total': len(organizations)
        })
        
    except Exception as e:
        return jsonify({'error': f'Failed to get organizations: {str(e)}'}), 500

@app.route('/api/historical/compare', methods=['POST'])
def compare_historical_organizations():
    """Compare multiple organizations from historical dataset"""
    try:
        if not historical_api:
            return jsonify({'error': 'Historical data system not available'}), 503
        
        data = request.json or {}
        organization_names = data.get('organizations', [])
        
        if not organization_names:
            return jsonify({'error': 'Organization names are required'}), 400
        
        if len(organization_names) > 10:
            return jsonify({'error': 'Cannot compare more than 10 organizations'}), 400
        
        import sqlite3
        conn = sqlite3.connect(historical_api.search_index.db_path)
        cursor = conn.cursor()
        
        comparison_data = {}
        
        for org_name in organization_names:
            # Get organization documents
            cursor.execute('''
                SELECT id, organization_type, sector, submission_date
                FROM documents
                WHERE organization_name = ?
            ''', (org_name,))
            
            org_docs = cursor.fetchall()
            
            if org_docs:
                # Get analysis data for this organization
                cursor.execute('''
                    SELECT analysis_category, COUNT(*) as count, AVG(frequency) as avg_frequency
                    FROM analysis_index
                    WHERE document_id IN ({})
                    GROUP BY analysis_category
                '''.format(','.join(['?' for _ in org_docs])), [doc[0] for doc in org_docs])
                
                analysis_stats = cursor.fetchall()
                
                comparison_data[org_name] = {
                    'organization_type': org_docs[0][1],
                    'sector': org_docs[0][2],
                    'document_count': len(org_docs),
                    'analysis_categories': {
                        cat: {'count': count, 'avg_frequency': round(avg_freq, 2)}
                        for cat, count, avg_freq in analysis_stats
                    }
                }
            else:
                comparison_data[org_name] = {
                    'error': 'Organization not found in dataset'
                }
        
        conn.close()
        
        return jsonify({
            'comparison': comparison_data,
            'organizations_compared': len(organization_names)
        })
        
    except Exception as e:
        return jsonify({'error': f'Failed to compare organizations: {str(e)}'}), 500

@app.route('/api/historical/timeline', methods=['GET'])
def get_historical_timeline():
    """Get timeline of historical document submissions"""
    try:
        if not historical_api:
            return jsonify({'error': 'Historical data system not available'}), 503
        
        import sqlite3
        conn = sqlite3.connect(historical_api.search_index.db_path)
        cursor = conn.cursor()
        
        # Get submission timeline (most will be from 2025)
        cursor.execute('''
            SELECT submission_date, COUNT(*) as count,
                   GROUP_CONCAT(DISTINCT organization_type) as org_types
            FROM documents
            WHERE submission_date != '' AND submission_date IS NOT NULL
            GROUP BY submission_date
            ORDER BY submission_date
        ''')
        
        timeline_data = cursor.fetchall()
        
        timeline = []
        for row in timeline_data:
            timeline.append({
                'date': row[0],
                'document_count': row[1],
                'organization_types': row[2].split(',') if row[2] else []
            })
        
        # Get organization type trends
        cursor.execute('''
            SELECT organization_type, COUNT(*) as count
            FROM documents
            GROUP BY organization_type
            ORDER BY count DESC
        ''')
        
        org_type_trends = dict(cursor.fetchall())
        
        conn.close()
        
        return jsonify({
            'timeline': timeline,
            'organization_type_trends': org_type_trends,
            'total_periods': len(timeline)
        })
        
    except Exception as e:
        return jsonify({'error': 'Failed to get timeline: ' + str(e)}), 500

@app.route('/api/historical/export', methods=['POST'])
def export_historical_data():
    """Export historical dataset in various formats"""
    try:
        if not historical_api:
            return jsonify({'error': 'Historical data system not available'}), 503
        
        data = request.json or {}
        export_format = data.get('format', 'json')
        filters = data.get('filters', {})
        limit = data.get('limit', 100)
        
        if export_format not in ['json', 'csv']:
            return jsonify({'error': 'Supported formats: json, csv'}), 400
        
        if limit > 1000:
            return jsonify({'error': 'Export limit cannot exceed 1000 documents'}), 400
        
        # Get filtered data  
        browse_result = historical_api.browse_historical_data(filters, limit, 0)
        documents = browse_result['documents']
        
        if export_format == 'json':
            return jsonify({
                'export_format': 'json',
                'total_documents': len(documents),
                'filters': filters,
                'data': documents,
                'exported_at': datetime.now().isoformat()
            })
        
        elif export_format == 'csv':
            # Convert to CSV format
            if not documents:
                return jsonify({'error': 'No documents found for export'}), 404
            
            csv_headers = list(documents[0].keys())
            csv_rows = []
            
            # Add header
            csv_rows.append(','.join(csv_headers))
            
            # Add data rows
            for doc in documents:
                row = []
                for header in csv_headers:
                    value = str(doc.get(header, ''))
                    # Escape commas and quotes
                    if ',' in value or '"' in value:
                        value = f'"{value.replace('"', '""')}"'
                    row.append(value)
                csv_rows.append(','.join(row))
            
            csv_content = '\n'.join(csv_rows)
            
            return jsonify({
                'export_format': 'csv',
                'total_documents': len(documents),
                'filters': filters,
                'csv_content': csv_content,
                'exported_at': '2025-07-31'
            })
        
    except Exception as e:
        return jsonify({'error': f'Failed to export data: {str(e)}'}), 500

@app.route('/api/historical/health', methods=['GET'])
def historical_health_check():
    """Historical data API health check"""
    
    health_status = {
        'status': 'healthy' if historical_api else 'unavailable',
        'service': 'AI Policy Analyzer Historical Data API',
        'version': '1.0.0',
        'components': {
            'historical_loader': 'operational' if historical_api else 'failed',
            'search_integration': 'operational' if historical_api and historical_api.search_index else 'failed'
        }
    }
    
    if historical_api:
        try:
            # Test database connection
            stats = historical_api.get_historical_statistics()
            health_status['dataset_info'] = {
                'total_documents': stats.get('total_historical_documents', 0),
                'data_source': stats.get('data_source', 'Unknown')
            }
        except Exception as e:
            health_status['status'] = 'degraded'
            health_status['error'] = str(e)
    
    status_code = 200 if health_status['status'] == 'healthy' else 503
    return jsonify(health_status), status_code

if __name__ == '__main__':
    print("Starting AI Policy Analyzer Historical Data API...")
    print("Available endpoints:")
    print("  POST /api/historical/load - Load and index historical dataset")
    print("  GET /api/historical/browse - Browse historical data with pagination")
    print("  GET /api/historical/statistics - Get dataset statistics")
    print("  GET /api/historical/organizations - List organizations")
    print("  POST /api/historical/compare - Compare multiple organizations")
    print("  GET /api/historical/timeline - Get submission timeline")
    print("  POST /api/historical/export - Export data in JSON/CSV format")
    print("  GET /api/historical/health - Health check")
    
    # Import port configuration
try:
    from port_config import get_port
    port = get_port('historical_api')
except ImportError:
    port = 5003  # fallback port

app.run(debug=True, host='0.0.0.0', port=port)