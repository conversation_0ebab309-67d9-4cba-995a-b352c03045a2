<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Policy Dashboard - Comprehensive Test Suite</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 0;
            margin-bottom: 30px;
        }
        
        .module-card {
            transition: all 0.3s ease;
            border-left: 4px solid #007bff;
            margin-bottom: 20px;
        }
        
        .module-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .module-card.tested {
            border-left-color: #28a745;
            background-color: #f8fff9;
        }
        
        .module-card.testing {
            border-left-color: #ffc107;
            background-color: #fffdf5;
        }
        
        .module-card.error {
            border-left-color: #dc3545;
            background-color: #fff5f5;
        }
        
        .test-status {
            position: absolute;
            top: 10px;
            right: 10px;
            font-size: 1.2em;
        }
        
        .test-progress {
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .test-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.5s ease;
        }
        
        .test-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-radius: 4px;
        }
        
        .log-success {
            background-color: #d4edda;
            color: #155724;
        }
        
        .log-error {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .log-info {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        
        .log-warning {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .integration-flow {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
        }
        
        .flow-step {
            display: inline-block;
            background: rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 20px;
            margin: 5px;
            backdrop-filter: blur(10px);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .testing-animation {
            animation: pulse 2s infinite;
        }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateX(-20px); }
            to { opacity: 1; transform: translateX(0); }
        }
        
        .slide-in {
            animation: slideIn 0.5s ease-out;
        }
    </style>
</head>
<body>
    <!-- Test Header -->
    <div class="test-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-vial me-3"></i>AI Policy Dashboard - Comprehensive Test Suite</h1>
                    <p class="lead mb-0">Testing all enhanced modules and their integration capabilities</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="test-progress">
                        <div class="test-progress-bar" id="overallProgress" style="width: 0%"></div>
                    </div>
                    <small>Overall Progress: <span id="progressText">0%</span></small>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Test Control Panel -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-control-panel"></i> Test Control Panel</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Test Configuration</h6>
                        <div class="mb-3">
                            <label for="testMode" class="form-label">Test Mode</label>
                            <select class="form-select" id="testMode">
                                <option value="comprehensive">Comprehensive Test</option>
                                <option value="quick">Quick Test</option>
                                <option value="integration">Integration Test</option>
                                <option value="performance">Performance Test</option>
                                <option value="custom">Custom Test</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="testDepth" class="form-label">Test Depth</label>
                            <select class="form-select" id="testDepth">
                                <option value="surface">Surface Level</option>
                                <option value="standard" selected>Standard</option>
                                <option value="deep">Deep Testing</option>
                                <option value="stress">Stress Testing</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>Test Actions</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary btn-lg" onclick="startComprehensiveTest()">
                                <i class="fas fa-rocket"></i> Start Comprehensive Test
                            </button>
                            <div class="row">
                                <div class="col-6">
                                    <button class="btn btn-outline-success" onclick="runQuickTest()">
                                        <i class="fas fa-bolt"></i> Quick Test
                                    </button>
                                </div>
                                <div class="col-6">
                                    <button class="btn btn-outline-info" onclick="runIntegrationTest()">
                                        <i class="fas fa-link"></i> Integration Test
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Integration Flow Visualization -->
        <div class="integration-flow">
            <h5><i class="fas fa-project-diagram me-2"></i>Module Integration Flow</h5>
            <div class="text-center">
                <div class="flow-step">📄 Document Analysis</div>
                <i class="fas fa-arrow-right mx-2"></i>
                <div class="flow-step">💭 Sentiment Lab</div>
                <i class="fas fa-arrow-right mx-2"></i>
                <div class="flow-step">🔍 Predictive Analytics</div>
                <i class="fas fa-arrow-right mx-2"></i>
                <div class="flow-step">🌐 Network Analysis</div>
                <br><br>
                <div class="flow-step">📊 Comparative Analysis</div>
                <i class="fas fa-arrow-left mx-2"></i>
                <div class="flow-step">⚙️ Policy Simulator</div>
                <i class="fas fa-arrow-left mx-2"></i>
                <div class="flow-step">📡 Realtime Monitor</div>
            </div>
        </div>

        <!-- Test Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <i class="fas fa-check-circle fa-2x text-success"></i>
                <div class="stat-number text-success" id="passedTests">0</div>
                <div>Tests Passed</div>
            </div>
            <div class="stat-card">
                <i class="fas fa-times-circle fa-2x text-danger"></i>
                <div class="stat-number text-danger" id="failedTests">0</div>
                <div>Tests Failed</div>
            </div>
            <div class="stat-card">
                <i class="fas fa-clock fa-2x text-info"></i>
                <div class="stat-number text-info" id="testDuration">0s</div>
                <div>Test Duration</div>
            </div>
            <div class="stat-card">
                <i class="fas fa-percentage fa-2x text-warning"></i>
                <div class="stat-number text-warning" id="coveragePercent">0%</div>
                <div>Test Coverage</div>
            </div>
        </div>

        <!-- Module Test Cards -->
        <div class="row" id="moduleTestCards">
            <!-- Module cards will be populated by JavaScript -->
        </div>

        <!-- Test Results -->
        <div class="row mt-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0"><i class="fas fa-chart-line"></i> Test Results Visualization</h6>
                        <button class="btn btn-outline-secondary btn-sm" onclick="exportTestResults()">
                            <i class="fas fa-download"></i> Export Results
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="testResultsChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-list-alt"></i> Test Log</h6>
                    </div>
                    <div class="card-body p-0">
                        <div class="test-log" id="testLog">
                            <div class="log-entry log-info">
                                <strong>[INFO]</strong> Test suite initialized
                            </div>
                            <div class="log-entry log-info">
                                <strong>[INFO]</strong> Ready to start comprehensive testing
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Integration Test Results -->
        <div class="card mt-4" id="integrationResults" style="display: none;">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-network-wired"></i> Integration Test Results</h5>
            </div>
            <div class="card-body">
                <div id="integrationResultsContent">
                    <!-- Integration results will be populated by JavaScript -->
                </div>
            </div>
        </div>

        <!-- Performance Metrics -->
        <div class="card mt-4" id="performanceMetrics" style="display: none;">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-tachometer-alt"></i> Performance Metrics</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="chart-container">
                            <canvas id="performanceChart"></canvas>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div id="performanceDetails">
                            <!-- Performance details will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="dashboard/frontend/dashboard.js"></script>
    <script src="comprehensive_test_suite.js"></script>
    <script>
        // Initialize test suite when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Comprehensive Test Suite Loaded');
            initializeTestSuite();
        });
    </script>
</body>
</html>
