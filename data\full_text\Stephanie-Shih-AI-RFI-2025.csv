﻿content_type,page_number,content,character_count,word_count,metadata_field,metadata_value
document_info,0,Document: <PERSON><PERSON><PERSON><PERSON>-AI-RFI-2025.pdf,0,0,filename,<PERSON>-<PERSON><PERSON>-AI-RFI-2025.pdf
metadata,0,"Comments Received in Response To: Request for Information on the Development of an Artificial Intelligence (AI) Action Plan (""Plan"")",132,19,title,"Comments Received in Response To: Request for Information on the Development of an Artificial Intelligence (AI) Action Plan (""Plan"")"
metadata,0,Adobe Acrobat Pro (64-bit) 25.1.20432,37,5,creator,Adobe Acrobat Pro (64-bit) 25.1.20432
metadata,0,Adobe Acrobat Pro (64-bit) 25.1.20432,37,5,producer,Adobe Acrobat Pro (64-bit) 25.1.20432
metadata,0,D:20250415130935-04'00',23,1,creation_date,D:20250415130935-04'00'
metadata,0,D:20250415130935-04'00',23,1,modification_date,D:20250415130935-04'00'
document_stats,0,"Total pages: 2, Total characters: 4168, Total words: 720",4168,720,document_stats,"pages:2,chars:4168,words:720"
full_text,0,"From: Stephanie Shih To: ostp-ai-rfi Subject: [External] AI Action Plan Date: Sunday, March 16, 2025 12:37:22 PM CAUTION: This email originated from outside your organization. Exercise caution when opening attachments or clicking links, especially from unknown senders. Hello, I am adding my voice to those who feel Trump's Executive Order 14179 is unnecessary and an excuse to legally steal from the work of countless Americans for a technology that is overhyped and honestly less needed than those promoting it tout it to be. ""Artificial Intelligence"" - at its core, essentially machine learning by finding the most common patterns in the datasets is given and then replicating those patterns - for the use cases that have been given for services such as generative AI only steal work AND jobs from hardworking Americans for clunky, regurgitated slop. After all, AI - and especially generative AI - is about finding common patterns. The common use cases you see from the general public is that it reduces the barriers for art and the work of creation (visual, written, auditory, etc) - but art is as much about the process as it is the end result. Using generative AI has in fact shown to dumb down the brains of countless students using it to write their essays by typing in keywords and letting the machine work, because they're no longer actually writing themselves to showcase they understand the subject matter. You are never going to get anything brilliant from it, by dint of exactly how the technology works. It's hyped amongst the general public precisely because most people don't understand what it actually entails, to their detriment. Hell, we've seen how s & % the film industry has gotten with ""playing it safe"" by leaning hard into countless remakes and using the same reliable formula. If they're not gonna even invest in actual humans going forward? F & % lmao at ever reviving a Hollywood Golden Age. And this would be true of all sectors. Ideas are a dime a dozen. It's how those ideas are executed that make it valuable. And let me remind you, again, that AI can only regurgitate the most generic s& % precisely because of how the technology works. And even if, say, this technology and its service is so valuable, then all the moreso that the data being input in to develop the technology should be PROPERLY VALUED AND COMPENSATED FOR. If this technology can not work without other people's labor and original work, then one must acknowledge that this labor and work is not only necessary but valuable and worthwhile. It is moreover NOT fair-use of another person's labor especially when taken without consent. If you think a movie you can make is ~so great~ but you don't have the skills? Then pitch it to the folks who do! Drive people to invest in it! Compensate the people who would work on it properly! People are not entitled to something just because they want it. I have found few use cases where AI does contribute value to the world, mostly in the cases of scientific research. Monterey Bay Aquarium's FathomNet, for example, is one of them. They use crowd-sourced volunteer work to help train their datasets to better identify marine life to further scientific research, but this is the key thing. The images they use are their own intellectual property, the work training it is volunteered and therefore also consented to, and thus, there is no theft involved. And the purpose itself actually is valuable, unlike the s& % OpenAI hypes itself up with. *& % Trump and *& % this Executive Order. Obviously Trump sees no wrong in stealing the labor of thousands of people - he had a history of not paying contractors and others who have done work for him in his long history as a businessman after all - but I for one don't want my own labor and the labor of countless others free for the taking without my consent. I refuse to allow my intellectual property to be stolen. Signed, A Disgruntled Programmer and Artist All e-mails to and from this account are for NITRD official use only and subject to certain disclosure requirements.If you have received this e-mail in error, we ask that you notify the sender and delete it immediately.",4168,720,full_document_text,
page_text,1,"From: Stephanie Shih To: ostp-ai-rfi Subject: [External] AI Action Plan Date: Sunday, March 16, 2025 12:37:22 PM CAUTION: This email originated from outside your organization. Exercise caution when opening attachments or clicking links, especially from unknown senders. Hello, I am adding my voice to those who feel Trump's Executive Order 14179 is unnecessary and an excuse to legally steal from the work of countless Americans for a technology that is overhyped and honestly less needed than those promoting it tout it to be. ""Artificial Intelligence"" - at its core, essentially machine learning by finding the most common patterns in the datasets is given and then replicating those patterns - for the use cases that have been given for services such as generative AI only steal work AND jobs from hardworking Americans for clunky, regurgitated slop. After all, AI - and especially generative AI - is about finding common patterns. The common use cases you see from the general public is that it reduces the barriers for art and the work of creation (visual, written, auditory, etc) - but art is as much about the process as it is the end result. Using generative AI has in fact shown to dumb down the brains of countless students using it to write their essays by typing in keywords and letting the machine work, because they're no longer actually writing themselves to showcase they understand the subject matter. You are never going to get anything brilliant from it, by dint of exactly how the technology works. It's hyped amongst the general public precisely because most people don't understand what it actually entails, to their detriment. Hell, we've seen how s & % the film industry has gotten with ""playing it safe"" by leaning hard into countless remakes and using the same reliable formula. If they're not gonna even invest in actual humans going forward? F & % lmao at ever reviving a Hollywood Golden Age. And this would be true of all sectors. Ideas are a dime a dozen. It's how those ideas are executed that make it valuable. And let me remind you, again, that AI can only regurgitate the most generic s& % precisely because of how the technology works. And even if, say, this technology and its service is so valuable, then all the moreso that the data being input in to develop the technology should be PROPERLY VALUED AND COMPENSATED FOR. If this technology can not work without other people's labor and original work, then one must acknowledge that this labor and work is not only necessary but valuable and worthwhile. It is moreover NOT fair-use of another person's labor especially when taken without consent. If you think a movie you can make is ~so great~ but you don't have the skills? Then pitch it to the folks who do! Drive people to invest in it! Compensate the people who would work on it properly! People are not entitled to something just because they want it. I have found few use cases where AI does contribute value to the world, mostly in the cases of scientific research. Monterey Bay Aquarium's FathomNet, for example, is one of them. They use crowd-sourced volunteer work to help train their datasets to better identify marine life to further scientific research, but this is the key thing. The images they use are their own intellectual property, the work training it is volunteered and therefore also consented to, and thus, there is no theft involved. And the purpose itself actually is valuable, unlike the s& %",3457,591,page_content,page_1
page_text,2,"OpenAI hypes itself up with. *& % Trump and *& % this Executive Order. Obviously Trump sees no wrong in stealing the labor of thousands of people - he had a history of not paying contractors and others who have done work for him in his long history as a businessman after all - but I for one don't want my own labor and the labor of countless others free for the taking without my consent. I refuse to allow my intellectual property to be stolen. Signed, A Disgruntled Programmer and Artist All e-mails to and from this account are for NITRD official use only and subject to certain disclosure requirements.If you have received this e-mail in error, we ask that you notify the sender and delete it immediately.",710,129,page_content,page_2
