/**
 * Display search results
 */
function displaySearchResults(data) {
    const resultsContainer = document.getElementById('searchResults');
    if (!resultsContainer) return;
    
    // Fix bug: Check if data is properly formatted
    if (!data || (!data.results && !Array.isArray(data))) {
        displaySampleResults();
        return;
    }

    // Support both data formats: object with results array or direct array
    const results = Array.isArray(data) ? data : (data.results || []);
    
    if (results.length === 0) {
        // Show empty state if no results found
        resultsContainer.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> 
                No results found. Please try a different search query or filters.
            </div>
        `;
        return;
    }
    
    let resultsHTML = `
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5>Search Results (${data.total_results || results.length})</h5>
            <small class="text-muted">Query: "${data.query || document.getElementById('searchInput')?.value || 'filtered search'}"</small>
        </div>
    `;
    
    resultsHTML += createResultsTable(results);
    
    resultsContainer.innerHTML = resultsHTML;
    dashboardState.searchResults = results;
}

/**
 * Display sample search results
 */
function displaySampleResults() {
    const sampleResults = [
        {
            document_id: 'google_llc_analysis',
            organization_name: 'Google LLC',
            organization_type: 'Corporate',
            sector: 'Technology',  
            document_type: 'AI Policy Statement',
            relevance_score: '95%'
        },
        {
            document_id: '1day_sooner_analysis',
            organization_name: '1Day Sooner',
            organization_type: 'Nonprofit',
            sector: 'Research',
            document_type: 'AI RFI Response',
            relevance_score: '92%'
        },
        {
            document_id: 'microsoft_corporation_analysis',
            organization_name: 'Microsoft Corporation',
            organization_type: 'Corporate',
            sector: 'Technology',
            document_type: 'Policy Framework',
            relevance_score: '88%'
        },
        {
            document_id: '3c_analysis',
            organization_name: '3C',
            organization_type: 'Academic',
            sector: 'Research',
            document_type: 'Position Paper',
            relevance_score: '85%'
        },
        {
            document_id: 'openai_analysis',
            organization_name: 'OpenAI',
            organization_type: 'Corporate',
            sector: 'AI Research',
            document_type: 'Safety Guidelines',
            relevance_score: '90%'
        }
    ];
    
    const resultsContainer = document.getElementById('searchResults');
    if (!resultsContainer) return;
    
    let resultsHTML = `
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5>Sample Analysis Results (${sampleResults.length})</h5>
            <small class="text-muted">Click "View" to see detailed analysis</small>
        </div>
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i> 
            These are sample results from the analysis database. Search functionality requires API connections.
        </div>
    `;
    
    resultsHTML += createResultsTable(sampleResults);
    
    resultsContainer.innerHTML = resultsHTML;
    dashboardState.searchResults = sampleResults;
}

/**
 * Create a results table from data
 */
function createResultsTable(results) {
    // Fix bug: Handle case when results is not an array
    if (!Array.isArray(results)) {
        console.error("Expected results to be an array but got:", typeof results);
        return '<div class="alert alert-danger">Invalid results format</div>';
    }

    let tableHTML = `
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Organization</th>
                        <th>Type</th>
                        <th>Sector</th>
                        <th>Document Type</th>
                        <th>Relevance</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    results.forEach(result => {
        // Fix bug: Handle potential missing fields with default values
        const orgName = result.organization_name || 'Unknown';
        const orgType = result.organization_type || 'N/A';
        const sector = result.sector || 'N/A';
        const docType = result.document_type || 'Document';
        const relevance = result.relevance_score || 'N/A';
        const docId = result.document_id || `doc_${Math.random().toString(36).substr(2, 9)}`;
        
        tableHTML += `
            <tr>
                <td><strong>${orgName}</strong></td>
                <td><span class="badge bg-secondary">${orgType}</span></td>
                <td>${sector}</td>
                <td>${docType}</td>
                <td><span class="badge bg-primary">${relevance}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewAnalysis('${docId}')">
                        <i class="fas fa-eye me-1"></i> View
                    </button>
                </td>
            </tr>
        `;
    });
    
    tableHTML += `
                </tbody>
            </table>
        </div>
    `;
    
    return tableHTML;
}

/**
 * Toggle filter panel
 */
function toggleFilters() {
    const filterPanel = document.getElementById('filterPanel');
    if (filterPanel) {
        filterPanel.style.display = filterPanel.style.display === 'none' ? 'block' : 'none';
    }
}

/**
 * View analysis for a document
 */
function viewAnalysis(documentId) {
    // Navigate to analysis section
    navigateToSection('analysis');
    
    // Fix bug: Handle case when searchResults is not initialized
    if (!dashboardState.searchResults || !Array.isArray(dashboardState.searchResults)) {
        dashboardState.searchResults = [];
    }
    
    // Find the document in search results
    const document = dashboardState.searchResults.find(doc => doc.document_id === documentId);
    
    if (!document) {
        console.warn(`Document not found: ${documentId}`);
        // Generate a generic document with this ID for display purposes
        const genericDocument = {
            document_id: documentId,
            organization_name: documentId.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' '),
            organization_type: 'Unknown',
            sector: 'Unknown',
            document_type: 'Document',
            relevance_score: 'N/A'
        };
        
        // Show loading while fetching analysis
        showLoading(true);
        
        // Simulate API call delay
        setTimeout(() => {
            // Generate sample analysis data
            const analysisData = generateSampleAnalysis(genericDocument);
            
            // Display analysis
            displayAnalysisResults(analysisData);
            
            // Hide loading
            showLoading(false);
        }, 1000);
        
        return;
    }
    
    // Show loading while fetching analysis
    showLoading(true);
    
    // Try to fetch from API first
    fetch(`${API_CONFIG.analytics}/analysis/${documentId}`)
        .then(response => {
            if (!response.ok) throw new Error('API unavailable');
            return response.json();
        })
        .then(analysisData => {
            // Display analysis from API
            displayAnalysisResults(analysisData);
        })
        .catch(error => {
            console.warn(`Failed to fetch analysis for document ${documentId}, using sample data`);
            
            // Generate sample analysis data as fallback
            const analysisData = generateSampleAnalysis(document);
            
            // Display analysis
            displayAnalysisResults(analysisData);
        })
        .finally(() => {
            // Hide loading
            showLoading(false);
        });
}

/**
 * Generate sample analysis data for a document
 */
function generateSampleAnalysis(document) {
    return {
        document_id: document.document_id,
        organization_name: document.organization_name,
        organization_type: document.organization_type,
        sector: document.sector,
        document_type: document.document_type,
        analysis_timestamp: new Date().toISOString(),
        sentiment_analysis: {
            overall_sentiment: Math.random() > 0.6 ? 'Positive' : (Math.random() > 0.5 ? 'Neutral' : 'Negative'),
            sentiment_scores: {
                positive: (Math.random() * 0.6 + 0.3).toFixed(2),
                neutral: (Math.random() * 0.3).toFixed(2),
                negative: (Math.random() * 0.3).toFixed(2)
            },
            key_phrases: [
                'artificial intelligence',
                'policy regulation',
                'ethics framework',
                'responsible development',
                'governance mechanisms'
            ]
        },
        policy_analysis: {
            dominant_preference: Math.random() > 0.5 ? 'Self-Regulation' : (Math.random() > 0.5 ? 'Co-Regulation' : 'Government Oversight'),
            regulatory_stance: Math.random() > 0.6 ? 'Supportive' : (Math.random() > 0.5 ? 'Neutral' : 'Cautious'),
            key_concerns: [
                'Safety',
                'Privacy',
                'Transparency',
                'Accountability',
                'Fairness'
            ]
        }
    };
}

/**
 * Load Sentiment Lab section
 */
function loadSentimentLab() {
    const sentimentLabSection = document.getElementById('sentiment-lab-section');
    if (!sentimentLabSection) return;
    
    sentimentLabSection.innerHTML = `
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="h4 mb-0">Sentiment Analysis Lab</h2>
            <button class="btn btn-sm btn-outline-primary" onclick="runSentimentAnalysis()">
                <i class="fas fa-play me-1"></i> Run Analysis
            </button>
        </div>
        
        <div class="row g-3">
            <div class="col-md-6">
                <div class="card shadow-sm h-100">
                    <div class="card-header bg-white">
                        <h5 class="card-title mb-0">Input Text</h5>
                    </div>
                    <div class="card-body">
                        <textarea id="sentimentInput" class="form-control" rows="10" placeholder="Paste AI policy text here for sentiment analysis..."></textarea>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card shadow-sm h-100">
                    <div class="card-header bg-white">
                        <h5 class="card-title mb-0">Analysis Results</h5>
                    </div>
                    <div class="card-body">
                        <div id="sentimentResults">
                            <div class="text-center text-muted py-5">
                                <i class="fas fa-chart-bar fa-3x mb-3"></i>
                                <p>Enter text and click "Run Analysis" to see sentiment results</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="card-title mb-0">Key Phrases & Entities</h5>
                    </div>
                    <div class="card-body">
                        <div id="keyPhrases">
                            <div class="text-center text-muted py-5">
                                <p>Key phrases will appear here after analysis</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Initialize sentiment lab
    initializeSentimentLab();
}

/**
 * Initialize Sentiment Lab
 */
function initializeSentimentLab() {
    console.log('Initializing Sentiment Lab...');
    
    // Fix bug: Add sample text button for easy testing
    const sentimentInput = document.getElementById('sentimentInput');
    if (sentimentInput) {
        // Create sample text button
        const sampleButton = document.createElement('button');
        sampleButton.className = 'btn btn-sm btn-outline-secondary mt-2';
        sampleButton.innerHTML = '<i class="fas fa-file-alt me-1"></i> Load Sample Text';
        sampleButton.onclick = function() {
            sentimentInput.value = "Our AI policy framework emphasizes responsible innovation and ethical development. We believe in a balanced approach to AI governance that combines industry self-regulation with appropriate government oversight. Safety and transparency are our primary concerns, while ensuring AI systems remain beneficial and fair to all stakeholders. We commit to regular auditing and continuous improvement of our AI systems.";
        };
        
        // Add button after the textarea
        sentimentInput.parentNode.appendChild(sampleButton);
    }
}
