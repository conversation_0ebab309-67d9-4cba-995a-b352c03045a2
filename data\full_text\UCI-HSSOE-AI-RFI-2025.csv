﻿content_type,page_number,content,character_count,word_count,metadata_field,metadata_value
document_info,0,Document: UCI-HSSOE-AI-RFI-2025.pdf,0,0,filename,UCI-HSSOE-AI-RFI-2025.pdf
metadata,0,"Comments Received in Response To: Request for Information on the Development of an Artificial Intelligence (AI) Action Plan (""Plan"")",132,19,title,"Comments Received in Response To: Request for Information on the Development of an Artificial Intelligence (AI) Action Plan (""Plan"")"
metadata,0,Adobe Acrobat Pro (64-bit) 25.1.20432,37,5,creator,Adobe Acrobat Pro (64-bit) 25.1.20432
metadata,0,Adobe Acrobat Pro (64-bit) 25.1.20432,37,5,producer,Adobe Acrobat Pro (64-bit) 25.1.20432
metadata,0,D:20250415130940-04'00',23,1,creation_date,D:20250415130940-04'00'
metadata,0,D:20250415130940-04'00',23,1,modification_date,D:20250415130940-04'00'
document_stats,0,"Total pages: 3, Total characters: 7277, Total words: 988",7277,988,document_stats,"pages:3,chars:7277,words:988"
full_text,0,"1 Request for Information on the Development of an Artificial Intelligence (AI) Action Plan Name of person filing the response: Prof. Andrei Shkel, Associate Dean of Research and Innovation Organization filing the response: Henry Samueli School of Engineering, University of California, Irvine Recommended Policy Action #1: Establish Data Centers that Standardize Data Input and Output for Training AI Models Description High -quality, standardized data is critical for the accuracy, reliability, and effectiveness of AI models. Many scientific and technological fields currently lack well -curated, domain - specific data sources, leading to inconsistencies and reduced effectiveness of trained AI models . Establishing dedicated data centers that provide verifiable, high -quality datasets is essential for advancing AI applications across industries. These datasets should be publicly accessible to promote transparency and innovation. Beyond standardized input data, ensuring that AI model outputs are verifiable is equally important. Recent efforts, such as DeepSeek outputs being benchmarked against ChatGPT, demonstrate the need for domain -specific verification methods. Developing etalo ns or reference benchmarks tailored to specific scientific and engineering domains will enable robust AI performance assessment. Key Considerations 1. Data Quality Control & Standardization : Implementing standardized protocols for data collection, validation, and curation to ensure consistency, accuracy, and reliability in AI training. 2. Strategic Data Collection : Addressing gaps in experimental datasets through improved methodologies for data acquisition, ensuring AI models receive comprehensive and representativ e training inputs. Use Cases 1. AI-Driven Materials Discovery : AI can accelerate the synthesis of new materials by computationally predicting viable candidates before physical validation, significantly reducing costs and development time. As the U.S. faces increasing global competition in materials science innovatio n, access to high -quality experimental datasets is crucial for maintaining technological leadership and national security. 2. AI for Biomolecular and Cellular Engineering : AI-driven design of biomolecules, especially sequence -encoded biopolymers like nucleic acids and proteins, requires access to extensive, high -quality datasets to accurately predict and 2 engineer desired functionalities. Extensive s tandardized datasets , including novel experimental approaches to generate such datasets, will improve model performance and accelerate advancements in biotechnology, pharmaceuticals, and healthcare. AI can also be used to program cells , enabling them to precisely respond to medical treatment targets. Beyond advancing healthcare, AI-driven cell engineering will be instrumental for improving food production, by optimizing the quality and quantity of crops and livestock. Recommended Policy Action #2: Establish Policies that Facilitate Synergy Among Human s, Cyber -Physical System s, and AI Description Time series data for machine learning at the edge of cloud computing is essential to understanding the dynamic interaction between human and cy ber-physical system s as well as to accelerating the adoption of edge AI for improving individual quality of life and socioeconomic impacts. High-quality, standardized time-series data is critical for the accuracy, reliability, and effectiveness of edge AI models. Many scientific and technological fields currently lack well -curated, domain -specific data sources , due to the proprietary and privacy nature of the data. Integrating AI with human expertise and advanced hardware systems is essential for optimizing manufacturing, national security, and autonomous s ystems. AI -driven automation, combined with human oversight, enhances efficiency, accuracy, and adaptability in complex environments. Key Considerations 1.Manufacturing Technologies : Policies should facilitate collaboration between equipment manufacturers, software developers, and foundry companies to enable AI-driven decision -making at the edge . AI -enhanced automation should complement human expertise to develop new technologies or to improve productivity, reduce contamination risks, and enhance cost -efficiency in existing ones . 2.Human -Machine Teaming : Trustworthy AI systems can serve as decision aids in critical applications that involve, e.g., warfighters where situational awareness and rapid response capabilities are needed . Policies should promote the integrati on of data from both humans and autonomous machines (e.g., robots with autonomous sensors), so that AI can be used to enhance decision -making and real-time adaptation to adversarial threats. Use Cases 1.AI-Enhanced Semiconductor Manufacturing : Real -time AI-driven robotic systems, combined with human expertise, can optimize wafer process flow for enhancing productivity , improve equipment utilization , and process defect 3 detection, and reduce material waste and ensure energy -efficient AI , strengthening U.S. leadership in semiconductor manufacturing. 2.Autonomous Systems in National Security : AI-powered autonomous systems can support military operations by analyzing real -time sensor data, predicting adversarial movements, and assisting warfighters in dynamic combat environments. By prioritizing high -quality data standardization and fostering AI -human collaboration, the U.S. can drive innovation, maintain technological leadership, and enhance national security. The following is a list of individuals engaged in developing this response, which is an outcome of the Exploratory Workshop: AI Action Plan held by UC Irvine s Samueli School of Engineering on February 24, 2025 . Prof. Mohammad Al Faruque, Electrical Engineering & Computer Science Prof. Plamen Atanassov, Chemical & Biomolecular Engineering Prof. Ramin Bostanabad, Mechanical & Aerospace Engineering Prof. Stacy Copp, Materials Science & Engineering Prof. Russell Detwiler, Civil & Environmental Engineering Prof. and Dean Magnus Egerstedt, Electrical Engineering & Computer Science, Stacey Nicholas Dean of Engineering Prof. Sitao Huang , Electrical Engineering & Computer Science Prof. Solmaz Kia, Mechanical & Aerospace Engineering Prof. Hyoukjun Kwon, Electrical Engineering & Computer Science Prof. Abraham Lee, Biomedical Engineering Dr. Helen Lee, Director of Research Development Prof. Guann Pyng ( G.P.) Li, Electrical Engineering & Computer Science Prof. Zhou Li, Electrical Engineering & Computer Science Prof. Chang Liu, Biomedical Engineering Prof. Mohammad Javad Abdolhosseini Qomi, Civil & Environmental Engineering Prof. Maxim Shcherbakov, Electrical Engineering & Computer Science Prof. Andrei Shkel, Mechanical & Aerospace Engineering, Associate Dean for Research & Innovation Prof. Yasser Shoukry, Electrical Engineering & Computer Science Prof. Camilo V élez Cuervo , Mechanical & Aerospace Engineering Prof. Farzin Zareian, Civil & Environmental Engineering This document is approved for public dissemination. The document contains no business - proprietary or confidential information. Document contents may be reused by the government in developing the AI Action Plan and associated documents without attribution .",7277,988,full_document_text,
page_text,1,"1 Request for Information on the Development of an Artificial Intelligence (AI) Action Plan Name of person filing the response: Prof. Andrei Shkel, Associate Dean of Research and Innovation Organization filing the response: Henry Samueli School of Engineering, University of California, Irvine Recommended Policy Action #1: Establish Data Centers that Standardize Data Input and Output for Training AI Models Description High -quality, standardized data is critical for the accuracy, reliability, and effectiveness of AI models. Many scientific and technological fields currently lack well -curated, domain - specific data sources, leading to inconsistencies and reduced effectiveness of trained AI models . Establishing dedicated data centers that provide verifiable, high -quality datasets is essential for advancing AI applications across industries. These datasets should be publicly accessible to promote transparency and innovation. Beyond standardized input data, ensuring that AI model outputs are verifiable is equally important. Recent efforts, such as DeepSeek outputs being benchmarked against ChatGPT, demonstrate the need for domain -specific verification methods. Developing etalo ns or reference benchmarks tailored to specific scientific and engineering domains will enable robust AI performance assessment. Key Considerations 1. Data Quality Control & Standardization : Implementing standardized protocols for data collection, validation, and curation to ensure consistency, accuracy, and reliability in AI training. 2. Strategic Data Collection : Addressing gaps in experimental datasets through improved methodologies for data acquisition, ensuring AI models receive comprehensive and representativ e training inputs. Use Cases 1. AI-Driven Materials Discovery : AI can accelerate the synthesis of new materials by computationally predicting viable candidates before physical validation, significantly reducing costs and development time. As the U.S. faces increasing global competition in materials science innovatio n, access to high -quality experimental datasets is crucial for maintaining technological leadership and national security. 2. AI for Biomolecular and Cellular Engineering : AI-driven design of biomolecules, especially sequence -encoded biopolymers like nucleic acids and proteins, requires access to extensive, high -quality datasets to accurately predict and",2398,321,page_content,page_1
page_text,2,"2 engineer desired functionalities. Extensive s tandardized datasets , including novel experimental approaches to generate such datasets, will improve model performance and accelerate advancements in biotechnology, pharmaceuticals, and healthcare. AI can also be used to program cells , enabling them to precisely respond to medical treatment targets. Beyond advancing healthcare, AI-driven cell engineering will be instrumental for improving food production, by optimizing the quality and quantity of crops and livestock. Recommended Policy Action #2: Establish Policies that Facilitate Synergy Among Human s, Cyber -Physical System s, and AI Description Time series data for machine learning at the edge of cloud computing is essential to understanding the dynamic interaction between human and cy ber-physical system s as well as to accelerating the adoption of edge AI for improving individual quality of life and socioeconomic impacts. High-quality, standardized time-series data is critical for the accuracy, reliability, and effectiveness of edge AI models. Many scientific and technological fields currently lack well -curated, domain -specific data sources , due to the proprietary and privacy nature of the data. Integrating AI with human expertise and advanced hardware systems is essential for optimizing manufacturing, national security, and autonomous s ystems. AI -driven automation, combined with human oversight, enhances efficiency, accuracy, and adaptability in complex environments. Key Considerations 1.Manufacturing Technologies : Policies should facilitate collaboration between equipment manufacturers, software developers, and foundry companies to enable AI-driven decision -making at the edge . AI -enhanced automation should complement human expertise to develop new technologies or to improve productivity, reduce contamination risks, and enhance cost -efficiency in existing ones . 2.Human -Machine Teaming : Trustworthy AI systems can serve as decision aids in critical applications that involve, e.g., warfighters where situational awareness and rapid response capabilities are needed . Policies should promote the integrati on of data from both humans and autonomous machines (e.g., robots with autonomous sensors), so that AI can be used to enhance decision -making and real-time adaptation to adversarial threats. Use Cases 1.AI-Enhanced Semiconductor Manufacturing : Real -time AI-driven robotic systems, combined with human expertise, can optimize wafer process flow for enhancing productivity , improve equipment utilization , and process defect",2582,356,page_content,page_2
page_text,3,"3 detection, and reduce material waste and ensure energy -efficient AI , strengthening U.S. leadership in semiconductor manufacturing. 2.Autonomous Systems in National Security : AI-powered autonomous systems can support military operations by analyzing real -time sensor data, predicting adversarial movements, and assisting warfighters in dynamic combat environments. By prioritizing high -quality data standardization and fostering AI -human collaboration, the U.S. can drive innovation, maintain technological leadership, and enhance national security. The following is a list of individuals engaged in developing this response, which is an outcome of the Exploratory Workshop: AI Action Plan held by UC Irvine s Samueli School of Engineering on February 24, 2025 . Prof. Mohammad Al Faruque, Electrical Engineering & Computer Science Prof. Plamen Atanassov, Chemical & Biomolecular Engineering Prof. Ramin Bostanabad, Mechanical & Aerospace Engineering Prof. Stacy Copp, Materials Science & Engineering Prof. Russell Detwiler, Civil & Environmental Engineering Prof. and Dean Magnus Egerstedt, Electrical Engineering & Computer Science, Stacey Nicholas Dean of Engineering Prof. Sitao Huang , Electrical Engineering & Computer Science Prof. Solmaz Kia, Mechanical & Aerospace Engineering Prof. Hyoukjun Kwon, Electrical Engineering & Computer Science Prof. Abraham Lee, Biomedical Engineering Dr. Helen Lee, Director of Research Development Prof. Guann Pyng ( G.P.) Li, Electrical Engineering & Computer Science Prof. Zhou Li, Electrical Engineering & Computer Science Prof. Chang Liu, Biomedical Engineering Prof. Mohammad Javad Abdolhosseini Qomi, Civil & Environmental Engineering Prof. Maxim Shcherbakov, Electrical Engineering & Computer Science Prof. Andrei Shkel, Mechanical & Aerospace Engineering, Associate Dean for Research & Innovation Prof. Yasser Shoukry, Electrical Engineering & Computer Science Prof. Camilo V élez Cuervo , Mechanical & Aerospace Engineering Prof. Farzin Zareian, Civil & Environmental Engineering This document is approved for public dissemination. The document contains no business - proprietary or confidential information. Document contents may be reused by the government in developing the AI Action Plan and associated documents without attribution .",2295,311,page_content,page_3
