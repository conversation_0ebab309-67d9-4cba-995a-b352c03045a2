#!/usr/bin/env python3
"""
验证ML分析修复效果
快速检查关键修复点是否已正确应用

Author: Claude Code
Date: 2025-08-10
"""

import os
import sys
from pathlib import Path

def check_critical_fixes():
    """检查关键修复点"""
    print("🔍 检查关键修复点...")
    
    dashboard_js = Path("dashboard/frontend/dashboard.js")
    
    if not dashboard_js.exists():
        print("❌ dashboard.js文件不存在")
        return False
    
    try:
        with open(dashboard_js, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 关键修复点检查
        critical_fixes = {
            'safe_model_access': '(mlModels && mlModels.' in content,
            'input_validation': 'if (!modelInfo || typeof modelInfo' in content,
            'results_validation': 'if (!results || typeof results' in content,
            'fallback_definitions': 'modelInfo = (mlModels' in content,
            'error_logging': 'console.log(`✅ Generated results:`' in content,
            'parameter_validation': 'if (!modelType || !dataSource' in content
        }
        
        print("关键修复点检查:")
        all_fixed = True
        for fix_name, found in critical_fixes.items():
            status = "✅" if found else "❌"
            print(f"  {status} {fix_name.replace('_', ' ').title()}")
            if not found:
                all_fixed = False
        
        return all_fixed
        
    except Exception as e:
        print(f"❌ 检查时出错: {e}")
        return False

def check_function_signatures():
    """检查函数签名和结构"""
    print("\n🔧 检查函数签名和结构...")
    
    dashboard_js = Path("dashboard/frontend/dashboard.js")
    
    try:
        with open(dashboard_js, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键函数是否存在且结构正确
        functions_to_check = [
            'function loadMLModels()',
            'function updateModelPerformance(',
            'function displayMLResults(',
            'function generateSentimentAnalysisResults(',
            'function generateClassificationResults('
        ]
        
        print("函数结构检查:")
        all_functions_ok = True
        for func_signature in functions_to_check:
            found = func_signature in content
            status = "✅" if found else "❌"
            func_name = func_signature.split('(')[0].replace('function ', '')
            print(f"  {status} {func_name}")
            if not found:
                all_functions_ok = False
        
        return all_functions_ok
        
    except Exception as e:
        print(f"❌ 检查函数时出错: {e}")
        return False

def check_error_patterns():
    """检查是否移除了可能导致错误的模式"""
    print("\n🚨 检查错误模式...")
    
    dashboard_js = Path("dashboard/frontend/dashboard.js")
    
    try:
        with open(dashboard_js, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查危险模式（应该被修复的）
        dangerous_patterns = [
            'mlModels.sentiment,',  # 直接访问，没有安全检查
            'mlModels.classification,',
            'modelInfo.accuracy',  # 没有验证的属性访问
            'results.model_type'   # 没有验证的结果访问
        ]
        
        print("危险模式检查:")
        safe_patterns_found = 0
        for pattern in dangerous_patterns:
            # 检查是否有安全的替代模式
            if pattern == 'mlModels.sentiment,':
                safe_pattern = '(mlModels && mlModels.sentiment) ? mlModels.sentiment :'
                has_safe = safe_pattern in content
            elif pattern == 'mlModels.classification,':
                safe_pattern = '(mlModels && mlModels.classification) ? mlModels.classification :'
                has_safe = safe_pattern in content
            elif pattern == 'modelInfo.accuracy':
                safe_pattern = 'safeModelInfo.accuracy'
                has_safe = safe_pattern in content
            elif pattern == 'results.model_type':
                safe_pattern = 'results.model_type || '
                has_safe = safe_pattern in content
            else:
                has_safe = False
            
            status = "✅" if has_safe else "⚠️"
            print(f"  {status} {pattern} -> {'Safe pattern found' if has_safe else 'May need attention'}")
            if has_safe:
                safe_patterns_found += 1
        
        return safe_patterns_found >= len(dangerous_patterns) * 0.75  # 75%的模式应该是安全的
        
    except Exception as e:
        print(f"❌ 检查错误模式时出错: {e}")
        return False

def main():
    """主函数"""
    print("🧪 ML分析修复验证")
    print("="*50)
    
    # 执行检查
    checks = [
        ("关键修复点", check_critical_fixes()),
        ("函数结构", check_function_signatures()),
        ("错误模式", check_error_patterns())
    ]
    
    # 统计结果
    passed_checks = sum(1 for _, result in checks if result)
    total_checks = len(checks)
    
    print(f"\n📊 检查结果:")
    print(f"通过: {passed_checks}/{total_checks}")
    print(f"成功率: {(passed_checks/total_checks)*100:.1f}%")
    
    print(f"\n📋 详细结果:")
    for check_name, result in checks:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {check_name}")
    
    if passed_checks == total_checks:
        print(f"\n🎉 所有检查通过！修复应该已经生效。")
        print(f"💡 建议：")
        print(f"  1. 启动dashboard测试ML功能")
        print(f"  2. 打开 quick_ml_test.html 进行功能测试")
        print(f"  3. 检查浏览器控制台是否还有错误")
    else:
        print(f"\n⚠️ 部分检查未通过，可能仍有问题。")
        print(f"💡 建议：")
        print(f"  1. 检查失败的项目")
        print(f"  2. 重新应用相关修复")
        print(f"  3. 测试具体的ML功能")
    
    return passed_checks == total_checks

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
