# PowerShell script to find the exact syntax error in dashboard_complete_final.js

# Common patterns that cause "Invalid left-hand side in assignment" errors
$errorPatterns = @(
    # Assignment to function call result
    '\b(\w+\([^)]*\))\s*=\s*[^=]',
    # Assignment to comparison expression
    '(\w+\s*(==|===|!=|!==|>|<|>=|<=)\s*\w+)\s*=\s*[^=]',
    # Assignment to parenthesized expression
    '\([^)]+\)\s*=\s*[^=]',
    # Assignment to a literal
    '\b(true|false|null|undefined|NaN|Infinity|\d+\.\d+|\d+)\s*=\s*[^=]',
    # Assignment to object property with dot notation on left side of equals without variable
    '\.\w+\s*=\s*[^=]',
    # Assignment to a string or template literal
    '([\"\'].*[\"\']|`.*`)\s*=\s*[^=]',
    # Arrow function with invalid assignment 
    '=>\s*{[^}]*\b(\w+\([^)]*\))\s*=\s*[^=]'
)

$filePath = "dashboard_complete_final.js"

if (Test-Path $filePath) {
    Write-Output "Checking $filePath for exact syntax errors..."
    
    # Read the file content
    $content = Get-Content $filePath -Raw
    
    # Split into lines for context
    $lines = $content -split "`n"
    
    # Keep track if we found any potential issues
    $foundIssue = $false
    
    # Check each error pattern
    foreach ($pattern in $errorPatterns) {
        $matches = [regex]::Matches($content, $pattern)
        
        if ($matches.Count -gt 0) {
            Write-Output "`nFound potentially problematic pattern: $pattern"
            
            foreach ($match in $matches) {
                $foundIssue = $true
                $lineNumber = ($content.Substring(0, $match.Index) -split "`n").Length
                $errorContext = @()
                
                # Get context (5 lines before and after)
                $startLine = [Math]::Max(0, $lineNumber - 5)
                $endLine = [Math]::Min($lines.Length - 1, $lineNumber + 5)
                
                for ($i = $startLine; $i -le $endLine; $i++) {
                    $prefix = if ($i -eq $lineNumber - 1) { ">>> " } else { "    " }
                    $errorContext += "$prefix$($i + 1): $($lines[$i])"
                }
                
                Write-Output "Potential syntax error at line $lineNumber:"
                Write-Output $errorContext
                Write-Output "Matched text: $($match.Value)"
                Write-Output "--------------------------------------------------"
            }
        }
    }
    
    if (-not $foundIssue) {
        Write-Output "No common syntax error patterns found. The error may be more complex or in a different pattern."
    }
} else {
    Write-Output "File not found: $filePath"
}

Write-Output "`nDone checking for syntax errors."
