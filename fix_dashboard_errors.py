#!/usr/bin/env python3
"""
修复Dashboard JavaScript错误的脚本
解决变量冲突和函数未定义问题
"""

import re
from pathlib import Path

def fix_dashboard_js():
    """修复dashboard.js中的错误"""
    print("🔧 修复dashboard.js中的JavaScript错误...")
    
    js_file = Path("dashboard/frontend/dashboard.js")
    
    if not js_file.exists():
        print(f"❌ 文件不存在: {js_file}")
        return False
    
    # 读取文件内容
    with open(js_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复1: 在Document Analysis模块中将selectedFiles替换为documentAnalysisFiles
    # 但保留Upload模块中的uploadSelectedFiles
    
    # 找到Document Analysis模块的开始和结束
    doc_analysis_start = content.find("// DOCUMENT ANALYSIS MODULE")
    if doc_analysis_start == -1:
        doc_analysis_start = content.find("let documentAnalysisData = {};")
    
    if doc_analysis_start != -1:
        # 在Document Analysis模块中替换selectedFiles
        before_doc_analysis = content[:doc_analysis_start]
        doc_analysis_section = content[doc_analysis_start:]
        
        # 在Document Analysis部分替换selectedFiles为documentAnalysisFiles
        # 但要小心不要替换函数名
        doc_analysis_section = re.sub(
            r'\bselectedFiles\b(?!\s*=\s*\[\])',  # 不替换初始化语句
            'documentAnalysisFiles',
            doc_analysis_section
        )
        
        content = before_doc_analysis + doc_analysis_section
    
    # 修复2: 确保有refreshDashboard函数
    if 'function refreshDashboard()' not in content:
        refresh_function = '''
/**
 * Refresh dashboard data
 */
function refreshDashboard() {
    console.log('🔄 Refreshing dashboard...');
    
    // Show loading indicator
    const refreshBtn = document.querySelector('button[onclick="refreshDashboard()"]');
    if (refreshBtn) {
        refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
        refreshBtn.disabled = true;
    }
    
    // Reload dashboard data
    loadSectionData('dashboard');
    
    // If backend integration is available, refresh backend data
    if (typeof loadBackendDataToDashboard === 'function') {
        loadBackendDataToDashboard();
    }
    
    // Hide loading indicator and show notification
    setTimeout(() => {
        if (refreshBtn) {
            refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Refresh';
            refreshBtn.disabled = false;
        }
        
        // Show notification
        const notification = document.createElement('div');
        notification.className = 'alert alert-success alert-dismissible fade show position-fixed';
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            Dashboard refreshed successfully
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);
    }, 1000);
}
'''
        # 在文件末尾添加函数
        content += refresh_function
    
    # 写回文件
    with open(js_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ dashboard.js修复完成")
    return True

def check_html_file():
    """检查HTML文件中的引用"""
    print("🔍 检查HTML文件...")
    
    html_file = Path("dashboard/frontend/index.html")
    
    if not html_file.exists():
        print(f"❌ 文件不存在: {html_file}")
        return False
    
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否有refreshDashboard调用
    if 'refreshDashboard()' in content:
        print("✅ HTML文件中找到refreshDashboard()调用")
    else:
        print("⚠️ HTML文件中未找到refreshDashboard()调用")
    
    return True

def create_test_page():
    """创建测试页面验证修复"""
    print("📄 创建测试页面...")
    
    test_html = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Error Fix Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Dashboard Error Fix Test</h1>
        
        <div class="alert alert-info">
            <h5>测试JavaScript修复</h5>
            <p>这个页面用于测试Dashboard JavaScript错误的修复情况。</p>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6>函数测试</h6>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt"></i> Test Refresh Dashboard
                        </button>
                        <button class="btn btn-secondary ms-2" onclick="testVariables()">
                            <i class="fas fa-vial"></i> Test Variables
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6>测试结果</h6>
                    </div>
                    <div class="card-body">
                        <div id="testResults">
                            <p class="text-muted">点击按钮开始测试...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="dashboard/frontend/dashboard.js"></script>
    <script>
        function testVariables() {
            const results = document.getElementById('testResults');
            let html = '<h6>变量测试结果:</h6><ul>';
            
            // 测试变量是否存在
            try {
                if (typeof documentAnalysisFiles !== 'undefined') {
                    html += '<li class="text-success">✅ documentAnalysisFiles 变量存在</li>';
                } else {
                    html += '<li class="text-warning">⚠️ documentAnalysisFiles 变量未定义</li>';
                }
                
                if (typeof uploadSelectedFiles !== 'undefined') {
                    html += '<li class="text-success">✅ uploadSelectedFiles 变量存在</li>';
                } else {
                    html += '<li class="text-warning">⚠️ uploadSelectedFiles 变量未定义</li>';
                }
                
                if (typeof refreshDashboard === 'function') {
                    html += '<li class="text-success">✅ refreshDashboard 函数存在</li>';
                } else {
                    html += '<li class="text-danger">❌ refreshDashboard 函数不存在</li>';
                }
                
            } catch (error) {
                html += `<li class="text-danger">❌ 测试出错: ${error.message}</li>`;
            }
            
            html += '</ul>';
            results.innerHTML = html;
        }
        
        // 页面加载时自动测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Dashboard Error Fix Test Page Loaded');
            setTimeout(testVariables, 1000);
        });
    </script>
</body>
</html>'''
    
    with open('dashboard_error_fix_test.html', 'w', encoding='utf-8') as f:
        f.write(test_html)
    
    print("✅ 测试页面创建完成: dashboard_error_fix_test.html")

def main():
    """主函数"""
    print("🔧 Dashboard JavaScript错误修复工具")
    print("=" * 50)
    
    # 修复JavaScript文件
    if fix_dashboard_js():
        print("✅ JavaScript修复完成")
    else:
        print("❌ JavaScript修复失败")
        return
    
    # 检查HTML文件
    check_html_file()
    
    # 创建测试页面
    create_test_page()
    
    print("\n" + "=" * 50)
    print("🎉 修复完成!")
    print("\n💡 下一步:")
    print("1. 刷新浏览器页面 (Ctrl+F5)")
    print("2. 或者访问测试页面: http://localhost:8080/dashboard_error_fix_test.html")
    print("3. 检查浏览器控制台是否还有错误")
    print("4. 测试Dashboard功能是否正常")

if __name__ == "__main__":
    main()
