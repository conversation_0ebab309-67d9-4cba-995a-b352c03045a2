#!/usr/bin/env python3
"""
API Services Launcher for AI Policy Analyzer Dashboard
This script starts all required API services with proper configuration
"""

import os
import sys
import subprocess
import time
import socket
import signal
import platform

def is_port_in_use(port):
    """Check if a port is already in use"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('localhost', port)) == 0

def start_api_service(script_name, port, window_title):
    """Start an API service in a new process"""
    if is_port_in_use(port):
        print(f"Warning: Port {port} is already in use. Stopping existing service...")
        # Try to free up the port
        if platform.system() == "Windows":
            os.system(f'for /f "tokens=5" %a in (\'netstat -aon ^| find ":{port}"\') do taskkill /F /PID %a')
        else:
            os.system(f"fuser -k {port}/tcp")
        time.sleep(1)
    
    script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "backend", script_name)
    
    if platform.system() == "Windows":
        # Start the service in a new command window
        cmd = f'start "{window_title}" cmd /k python "{script_path}"'
        subprocess.Popen(cmd, shell=True)
    else:
        # For non-Windows systems
        cmd = ["python3", script_path]
        subprocess.Popen(cmd)

def main():
    print("Starting AI Policy Analyzer API Services...")
    
    # Define all required API services
    services = [
        {"name": "visualization_api.py", "port": 5001, "title": "Visualization API"},
        {"name": "search_api_server.py", "port": 5002, "title": "Search API"},
        {"name": "analysis_results_api.py", "port": 5003, "title": "Historical Data API"},
        {"name": "advanced_analytics_api.py", "port": 5005, "title": "Analytics API"},
        {"name": "batch_processing_api.py", "port": 5007, "title": "Batch Processing API"}
    ]
    
    # Start each service
    for service in services:
        print(f"Starting {service['title']} on port {service['port']}...")
        start_api_service(service["name"], service["port"], service["title"])
        time.sleep(1)  # Brief pause between starting services
    
    print("\nAll API services have been started.")
    print("The dashboard should now be able to connect to all required endpoints.")
    print("\nPress Ctrl+C to exit this script. Note that API services will continue running.")
    
    try:
        # Keep the script running
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nExiting API services launcher...")

if __name__ == "__main__":
    main()
