#!/usr/bin/env python3
"""
检查Dashboard模块功能状态

Author: <PERSON> Code
Date: 2025-08-08
"""

import requests
import time
from pathlib import Path

def check_dashboard_modules():
    """检查Dashboard各模块功能状态"""
    
    print("🔍 检查Dashboard模块功能状态")
    print("=" * 50)
    
    modules = {
        "Dashboard": {
            "description": "主仪表板 - 统计概览",
            "api_endpoint": "http://localhost:5001/api/analysis/statistics",
            "status": "unknown"
        },
        "Upload Documents": {
            "description": "文档上传功能",
            "api_endpoint": "http://localhost:5001/api/upload",
            "status": "unknown"
        },
        "Analysis Results": {
            "description": "分析结果展示",
            "api_endpoint": "http://localhost:5001/api/analysis/results",
            "status": "unknown"
        },
        "Search & Discovery": {
            "description": "搜索和发现功能",
            "api_endpoint": "http://localhost:5003/api/search",
            "status": "unknown"
        },
        "Historical Data": {
            "description": "历史数据分析 (已修复)",
            "api_endpoint": "http://localhost:5003/api/historical/browse",
            "status": "working"
        },
        "ML Insights": {
            "description": "机器学习洞察",
            "api_endpoint": "http://localhost:5005/api/analytics/ml-insights",
            "status": "unknown"
        },
        "Predictive Analytics": {
            "description": "预测分析",
            "api_endpoint": "http://localhost:5005/api/analytics/predictions",
            "status": "unknown"
        },
        "Network Analysis": {
            "description": "网络分析",
            "api_endpoint": "http://localhost:5006/api/network/analysis",
            "status": "unknown"
        },
        "Sentiment Lab": {
            "description": "情感分析实验室",
            "api_endpoint": "http://localhost:5004/api/sentiment/analysis",
            "status": "unknown"
        },
        "Policy Simulator": {
            "description": "政策模拟器",
            "api_endpoint": "http://localhost:5002/api/policy/simulate",
            "status": "unknown"
        },
        "Real-time Monitor": {
            "description": "实时监控",
            "api_endpoint": "http://localhost:5008/api/realtime/status",
            "status": "unknown"
        },
        "Visualizations": {
            "description": "数据可视化",
            "api_endpoint": "http://localhost:5001/api/visualize/all",
            "status": "unknown"
        }
    }
    
    # 检查每个模块
    for module_name, module_info in modules.items():
        if module_info["status"] == "working":
            print(f"✅ {module_name}: {module_info['description']} - Already Working")
            continue
            
        print(f"\n🔍 检查模块: {module_name}")
        print(f"   描述: {module_info['description']}")
        print(f"   API: {module_info['api_endpoint']}")
        
        try:
            response = requests.get(module_info['api_endpoint'], timeout=3)
            if response.status_code == 200:
                print(f"   ✅ 状态: 正常工作")
                module_info["status"] = "working"
            else:
                print(f"   ⚠️ 状态: HTTP {response.status_code}")
                module_info["status"] = f"error_{response.status_code}"
        except requests.exceptions.ConnectionError:
            print(f"   ❌ 状态: 服务未启动")
            module_info["status"] = "not_running"
        except requests.exceptions.Timeout:
            print(f"   ⏱️ 状态: 超时")
            module_info["status"] = "timeout"
        except Exception as e:
            print(f"   ❌ 状态: 错误 - {e}")
            module_info["status"] = "error"
    
    return modules

def check_file_structure():
    """检查文件结构"""
    print("\n📁 检查文件结构...")
    
    important_files = [
        ("dashboard/frontend/index.html", "主HTML文件"),
        ("dashboard/frontend/dashboard.js", "主JavaScript文件"),
        ("dashboard/backend/search_api.py", "搜索API"),
        ("dashboard/backend/historical_data_integration.py", "历史数据集成"),
        ("dashboard/backend/analysis_results_api.py", "分析结果API"),
        ("dashboard/backend/visualization_api.py", "可视化API"),
        ("dashboard/backend/advanced_analytics_api.py", "高级分析API"),
        ("dashboard/backend/interactive_visualization_api.py", "交互式可视化API")
    ]
    
    existing_files = []
    missing_files = []
    
    for file_path, description in important_files:
        if Path(file_path).exists():
            print(f"   ✅ {description}: {file_path}")
            existing_files.append((file_path, description))
        else:
            print(f"   ❌ {description}: {file_path} - 缺失")
            missing_files.append((file_path, description))
    
    return existing_files, missing_files

def analyze_current_files():
    """分析当前文件内容"""
    print("\n📊 分析当前文件...")
    
    # 检查数据目录
    data_dirs = [
        "data",
        "data/90_FR_9088_pdfs",
        "dashboard/backend"
    ]
    
    for dir_path in data_dirs:
        path = Path(dir_path)
        if path.exists():
            files = list(path.glob("*"))
            print(f"   📁 {dir_path}: {len(files)} 个文件")
            
            # 显示前几个文件
            for i, file in enumerate(files[:3]):
                if file.is_file():
                    size = file.stat().st_size
                    print(f"      - {file.name} ({size} bytes)")
            
            if len(files) > 3:
                print(f"      ... 还有 {len(files) - 3} 个文件")
        else:
            print(f"   ❌ {dir_path}: 目录不存在")

def generate_recommendations(modules, existing_files, missing_files):
    """生成建议"""
    print("\n💡 建议和下一步行动:")
    print("=" * 50)
    
    # 统计模块状态
    working_modules = [name for name, info in modules.items() if info["status"] == "working"]
    not_running_modules = [name for name, info in modules.items() if info["status"] == "not_running"]
    error_modules = [name for name, info in modules.items() if "error" in info["status"]]
    
    print(f"📊 模块状态统计:")
    print(f"   ✅ 正常工作: {len(working_modules)} 个")
    print(f"   ❌ 未启动: {len(not_running_modules)} 个")
    print(f"   ⚠️ 有错误: {len(error_modules)} 个")
    
    print(f"\n🎯 优先级建议:")
    
    # 优先级1: 核心功能
    core_modules = ["Upload Documents", "Analysis Results", "Search & Discovery"]
    print(f"\n1. 核心功能模块 (优先级最高):")
    for module in core_modules:
        status = modules[module]["status"]
        if status != "working":
            print(f"   🔧 修复 {module} - {modules[module]['description']}")
    
    # 优先级2: 数据处理
    data_modules = ["Visualizations"]
    print(f"\n2. 数据处理模块:")
    for module in data_modules:
        status = modules[module]["status"]
        if status != "working":
            print(f"   🔧 修复 {module} - {modules[module]['description']}")
    
    # 优先级3: 高级功能
    advanced_modules = ["ML Insights", "Predictive Analytics", "Network Analysis", "Sentiment Lab", "Policy Simulator"]
    print(f"\n3. 高级功能模块 (可选):")
    for module in advanced_modules:
        status = modules[module]["status"]
        if status != "working":
            print(f"   🔧 修复 {module} - {modules[module]['description']}")
    
    # 文件建议
    if missing_files:
        print(f"\n📁 缺失文件需要创建:")
        for file_path, description in missing_files:
            print(f"   📄 {description}: {file_path}")
    
    print(f"\n🚀 立即行动建议:")
    print(f"1. 先修复核心上传和分析功能")
    print(f"2. 确保能读取现有文件进行分析")
    print(f"3. 实现文件上传功能")
    print(f"4. 最后添加高级分析功能")

def main():
    """主函数"""
    print("🔍 Dashboard模块功能检查")
    print("=" * 50)
    
    # 1. 检查模块API状态
    modules = check_dashboard_modules()
    
    # 2. 检查文件结构
    existing_files, missing_files = check_file_structure()
    
    # 3. 分析当前文件
    analyze_current_files()
    
    # 4. 生成建议
    generate_recommendations(modules, existing_files, missing_files)

if __name__ == "__main__":
    main()
