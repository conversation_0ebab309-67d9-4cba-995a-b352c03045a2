#!/usr/bin/env python3
"""
测试DOM安全访问修复效果
验证"Cannot set properties of null (setting 'textContent')"错误是否已解决

Author: Claude Code
Date: 2025-08-10
"""

import os
import sys
import re
from pathlib import Path

def count_unsafe_dom_access():
    """统计不安全的DOM访问"""
    print("🔍 统计不安全的DOM访问...")
    
    dashboard_js = Path("dashboard/frontend/dashboard.js")
    
    if not dashboard_js.exists():
        print("❌ dashboard.js文件不存在")
        return False
    
    try:
        with open(dashboard_js, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找不安全的DOM访问模式
        unsafe_patterns = [
            r"document\.getElementById\([^)]+\)\.textContent\s*=",
            r"document\.getElementById\([^)]+\)\.innerHTML\s*=",
            r"document\.getElementById\([^)]+\)\.style\.\w+\s*=",
            r"document\.getElementById\([^)]+\)\.value\s*="
        ]
        
        total_unsafe = 0
        pattern_counts = {}
        
        for pattern in unsafe_patterns:
            matches = re.findall(pattern, content)
            count = len(matches)
            total_unsafe += count
            pattern_name = pattern.split('\\.')[-1].split('\\s')[0]
            pattern_counts[pattern_name] = count
        
        print(f"   不安全DOM访问统计:")
        for pattern_name, count in pattern_counts.items():
            print(f"     {pattern_name}: {count} 处")
        
        print(f"   总计不安全访问: {total_unsafe} 处")
        
        return total_unsafe
        
    except Exception as e:
        print(f"❌ 统计时出错: {e}")
        return -1

def count_safe_dom_access():
    """统计安全的DOM访问"""
    print("\n✅ 统计安全的DOM访问...")
    
    dashboard_js = Path("dashboard/frontend/dashboard.js")
    
    try:
        with open(dashboard_js, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找安全的DOM访问模式
        safe_patterns = [
            r"safeUpdateElement\([^)]+,\s*['\"]textContent['\"]",
            r"safeUpdateElement\([^)]+,\s*['\"]innerHTML['\"]",
            r"safeUpdateElement\([^)]+,\s*['\"]style\.\w+['\"]",
            r"safeUpdateElement\([^)]+,\s*['\"]value['\"]"
        ]
        
        total_safe = 0
        pattern_counts = {}
        
        for pattern in safe_patterns:
            matches = re.findall(pattern, content)
            count = len(matches)
            total_safe += count
            pattern_name = pattern.split('[\'"]')[1].split('[\'"]')[0]
            pattern_counts[pattern_name] = count
        
        print(f"   安全DOM访问统计:")
        for pattern_name, count in pattern_counts.items():
            print(f"     {pattern_name}: {count} 处")
        
        print(f"   总计安全访问: {total_safe} 处")
        
        return total_safe
        
    except Exception as e:
        print(f"❌ 统计时出错: {e}")
        return -1

def check_safe_update_function():
    """检查安全更新函数是否存在"""
    print("\n🛡️ 检查安全更新函数...")
    
    dashboard_js = Path("dashboard/frontend/dashboard.js")
    
    try:
        with open(dashboard_js, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查safeUpdateElement函数是否存在
        function_exists = 'function safeUpdateElement(' in content
        
        if function_exists:
            print("   ✅ safeUpdateElement函数已定义")
            
            # 检查函数的功能完整性
            features = {
                'null_check': 'if (element)' in content,
                'textContent_support': "'textContent'" in content,
                'innerHTML_support': "'innerHTML'" in content,
                'style_support': "'style.'" in content,
                'warning_log': 'console.warn' in content and 'not found' in content
            }
            
            print("   功能检查:")
            all_features = True
            for feature_name, exists in features.items():
                status = "✅" if exists else "❌"
                print(f"     {status} {feature_name.replace('_', ' ').title()}")
                if not exists:
                    all_features = False
            
            return all_features
        else:
            print("   ❌ safeUpdateElement函数未定义")
            return False
        
    except Exception as e:
        print(f"❌ 检查函数时出错: {e}")
        return False

def check_critical_functions():
    """检查关键函数是否使用安全访问"""
    print("\n🎯 检查关键函数的安全性...")
    
    dashboard_js = Path("dashboard/frontend/dashboard.js")
    
    try:
        with open(dashboard_js, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 关键函数列表
        critical_functions = [
            'updateSentimentMetrics',
            'loadDashboardData',
            'updateRealtimeData',
            'updateModelPerformance',
            'displayMLResults'
        ]
        
        function_safety = {}
        
        for func_name in critical_functions:
            # 查找函数定义
            func_pattern = f"function {func_name}("
            func_start = content.find(func_pattern)
            
            if func_start != -1:
                # 查找函数结束
                brace_count = 0
                func_body_start = content.find('{', func_start)
                func_end = func_body_start
                
                for i, char in enumerate(content[func_body_start:], func_body_start):
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            func_end = i
                            break
                
                func_body = content[func_body_start:func_end+1]
                
                # 检查函数中的DOM访问安全性
                unsafe_access = len(re.findall(r"document\.getElementById\([^)]+\)\.\w+\s*=", func_body))
                safe_access = len(re.findall(r"safeUpdateElement\(", func_body))
                
                function_safety[func_name] = {
                    'unsafe_count': unsafe_access,
                    'safe_count': safe_access,
                    'is_safe': unsafe_access == 0 or safe_access > unsafe_access
                }
            else:
                function_safety[func_name] = {
                    'unsafe_count': 0,
                    'safe_count': 0,
                    'is_safe': False,
                    'not_found': True
                }
        
        print("   关键函数安全性:")
        all_safe = True
        for func_name, safety in function_safety.items():
            if safety.get('not_found'):
                status = "⚠️"
                detail = "Function not found"
            elif safety['is_safe']:
                status = "✅"
                detail = f"Safe ({safety['safe_count']} safe, {safety['unsafe_count']} unsafe)"
            else:
                status = "❌"
                detail = f"Unsafe ({safety['safe_count']} safe, {safety['unsafe_count']} unsafe)"
                all_safe = False
            
            print(f"     {status} {func_name}: {detail}")
        
        return all_safe
        
    except Exception as e:
        print(f"❌ 检查关键函数时出错: {e}")
        return False

def generate_safety_report(unsafe_count, safe_count, function_exists, functions_safe):
    """生成安全性报告"""
    print("\n" + "="*60)
    print("📊 DOM安全访问修复报告")
    print("="*60)
    
    total_access = unsafe_count + safe_count
    safety_ratio = (safe_count / total_access * 100) if total_access > 0 else 0
    
    print(f"DOM访问统计:")
    print(f"  不安全访问: {unsafe_count}")
    print(f"  安全访问: {safe_count}")
    print(f"  总访问数: {total_access}")
    print(f"  安全比例: {safety_ratio:.1f}%")
    
    print(f"\n功能检查:")
    print(f"  安全更新函数: {'✅ 存在' if function_exists else '❌ 缺失'}")
    print(f"  关键函数安全: {'✅ 安全' if functions_safe else '❌ 需要改进'}")
    
    # 计算总体评分
    score = 0
    if function_exists:
        score += 30
    if functions_safe:
        score += 30
    if safety_ratio >= 80:
        score += 40
    elif safety_ratio >= 60:
        score += 30
    elif safety_ratio >= 40:
        score += 20
    elif safety_ratio >= 20:
        score += 10
    
    print(f"\n🎯 总体评分: {score}/100")
    
    if score >= 90:
        status = "✅ 优秀"
        recommendation = "DOM访问已充分安全化，可以正常使用。"
    elif score >= 70:
        status = "⚠️ 良好"
        recommendation = "大部分DOM访问已安全化，建议继续改进剩余部分。"
    elif score >= 50:
        status = "⚠️ 一般"
        recommendation = "部分DOM访问已安全化，需要继续修复更多不安全访问。"
    else:
        status = "❌ 需要改进"
        recommendation = "DOM访问安全性不足，需要大量修复工作。"
    
    print(f"状态: {status}")
    print(f"建议: {recommendation}")
    
    print("="*60)
    
    return score >= 70

def main():
    """主函数"""
    print("🧪 DOM安全访问修复测试")
    print("="*60)
    print("测试目标: 验证'Cannot set properties of null'错误修复效果")
    print()
    
    # 执行检查
    unsafe_count = count_unsafe_dom_access()
    safe_count = count_safe_dom_access()
    function_exists = check_safe_update_function()
    functions_safe = check_critical_functions()
    
    # 生成报告
    if unsafe_count >= 0 and safe_count >= 0:
        success = generate_safety_report(unsafe_count, safe_count, function_exists, functions_safe)
        return 0 if success else 1
    else:
        print("\n❌ 测试过程中出现错误")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
