/* AI Policy Analyzer Dashboard - Custom Styles
 * Author: <PERSON>
 * Date: August 2025
 */

:root {
  --primary-color: #4e73df;
  --secondary-color: #858796;
  --success-color: #1cc88a;
  --info-color: #36b9cc;
  --warning-color: #f6c23e;
  --danger-color: #e74a3b;
  --light-color: #f8f9fc;
  --dark-color: #5a5c69;
  --sidebar-width: 240px;
  --sidebar-width-collapsed: 80px;
  --transition-speed: 0.3s;
}

/* Global Styles */
body {
  font-family: 'Nunito', 'Segoe UI', Roboto, sans-serif;
  background-color: #f8f9fc;
}

.chart-container {
  position: relative;
  height: 100%;
  min-height: 240px;
}

/* Sidebar Styles */
#sidebar-wrapper {
  min-height: 100vh;
  width: var(--sidebar-width);
  transition: width var(--transition-speed);
  z-index: 1;
  box-shadow: 0 .15rem 1.75rem 0 rgba(58, 59, 69, .15);
}

#sidebar-wrapper .sidebar-heading {
  padding: 1.2rem 1rem;
  font-size: 1.2rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 10px;
}

.logo-img {
  height: 32px;
  width: auto;
}

#sidebar-wrapper .list-group {
  width: 100%;
}

#sidebar-wrapper .list-group-item {
  border: none;
  padding: 0.75rem 1.25rem;
  border-radius: 0;
  color: var(--secondary-color);
}

#sidebar-wrapper .list-group-item.active {
  color: var(--primary-color);
  background-color: rgba(78, 115, 223, 0.1);
  border-left: 4px solid var(--primary-color);
}

#sidebar-wrapper .list-group-item:hover {
  color: var(--primary-color);
  background-color: rgba(78, 115, 223, 0.05);
}

/* Page Content Styles */
#page-content-wrapper {
  min-width: 100vw;
  flex-grow: 1;
}

.container-fluid {
  padding: 1.5rem;
}

/* Toggle Button */
#sidebarToggle {
  margin-right: 10px;
}

/* Content Sections */
.content-section {
  display: none;
  padding-bottom: 2rem;
}

.content-section.active {
  display: block;
}

/* Card Styles */
.card {
  margin-bottom: 1.5rem;
  border: none;
  border-radius: 0.5rem;
}

.card-header {
  padding: 0.75rem 1.25rem;
  background-color: #fff;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.card-footer {
  background-color: #fff;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding: 0.75rem 1.25rem;
}

/* Loading Overlay */
#loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(255, 255, 255, 0.7);
  z-index: 9999;
  display: none;
  justify-content: center;
  align-items: center;
}

/* Notifications */
#notification-area {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  max-width: 350px;
}

.notification {
  margin-bottom: 10px;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Upload Area */
.upload-area {
  border: 2px dashed #d1d3e2;
  border-radius: 0.5rem;
  background-color: #fafafa;
  transition: all 0.3s;
}

.upload-area:hover {
  border-color: var(--primary-color);
  background-color: rgba(78, 115, 223, 0.05);
  cursor: pointer;
}

.upload-area.dragover {
  border-color: var(--primary-color);
  background-color: rgba(78, 115, 223, 0.1);
}

/* Search Results Table */
.table-container {
  overflow-x: auto;
  border-radius: 0.35rem;
}

/* Badge Customizations */
.badge {
  font-weight: 500;
  padding: 0.35em 0.65em;
}

/* Key Metric Cards */
.metric-card {
  border-radius: 0.5rem;
  overflow: hidden;
}

.metric-card .card-body {
  padding: 1.5rem;
}

.metric-card .metric-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.metric-card .metric-value {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

/* Media Queries for Responsive Layout */
@media (min-width: 768px) {
  #page-content-wrapper {
    min-width: 0;
    width: 100%;
  }
}

@media (max-width: 768px) {
  #sidebar-wrapper {
    width: var(--sidebar-width-collapsed);
  }
  
  #sidebar-wrapper .sidebar-heading {
    padding: 0.75rem 0.5rem;
    justify-content: center;
  }
  
  #sidebar-wrapper .sidebar-heading div {
    display: none;
  }
  
  #sidebar-wrapper .list-group-item span {
    display: none;
  }
  
  #sidebar-wrapper .list-group-item i {
    font-size: 1.25rem;
  }
}
