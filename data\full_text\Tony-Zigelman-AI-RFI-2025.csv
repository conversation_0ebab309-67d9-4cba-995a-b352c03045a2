﻿content_type,page_number,content,character_count,word_count,metadata_field,metadata_value
document_info,0,Document: <PERSON><PERSON>-<PERSON>-RFI-2025.pdf,0,0,filename,<PERSON><PERSON><PERSON><PERSON>-AI-RFI-2025.pdf
metadata,0,"Comments Received in Response To: Request for Information on the Development of an Artificial Intelligence (AI) Action Plan (""Plan"")",132,19,title,"Comments Received in Response To: Request for Information on the Development of an Artificial Intelligence (AI) Action Plan (""Plan"")"
metadata,0,Adobe Acrobat Pro (64-bit) 25.1.20432,37,5,creator,Adobe Acrobat Pro (64-bit) 25.1.20432
metadata,0,Adobe Acrobat Pro (64-bit) 25.1.20432,37,5,producer,Adobe Acrobat Pro (64-bit) 25.1.20432
metadata,0,D:20250415131255-04'00',23,1,creation_date,D:20250415131255-04'00'
metadata,0,D:20250415131255-04'00',23,1,modification_date,D:20250415131255-04'00'
document_stats,0,"Total pages: 1, Total characters: 2131, Total words: 342",2131,342,document_stats,"pages:1,chars:2131,words:342"
full_text,0,"From: tony zigelman To: ostp-ai-rfi Subject: [External] Comments regarding AI action plan Date: Saturday, March 15, 2025 11:39:30 PM CAUTION: This email originated from outside your organization. Exercise caution when opening attachments or clicking links, especially from unknown senders. Hello, It is my understanding that AI models compress training data down to a matrix that they then use to generate responses. Those responses, for some queries and some training data, mayreproduce the contents of the training data. This is not ok.This is a clear violation of copyright law. You are not allowed to make a thing that has a chance -- any chance -- of intentionally reproducing someone else's copyrightedwork. Should you doubt the validity of my claims, I would ask you to investigate OpenAI's implementation of a secondary layer on ChatGPT that checks to see if their AI model isdumping large quantities of copyrighted text to the user and stops the response mid-way if itdetects said copyrighted text. This suggests to me that modern AI models do, in fact, have theability (and the propensity!) to distribute copyrighted work. It is my belief that the government should strongly investigate one or both of the following stances: AI models should be treated as compressed data, and that all of their training data should be considered to be in their data, and/or all AI-generated data should immediately enterthe public domain. AI is directly responsible for a loss of American jobs, American productivity, and in a handfulof cases, American lives. it is astoundingly good at generating things that *look* correct butaren't. We do not need to protect American AI interests. We should instead be looking toprotect American intellectual work and prohibit the use of AI for tasks where it purports to present factual information. If you have any questions, please do not hesitate to get in touch.Tony Zigelman All e-mails to and from this account are for NITRD official use only and subject to certain disclosure requirements.If you have received this e-mail in error, we ask that you notify the sender and delete it immediately.",2131,342,full_document_text,
page_text,1,"From: tony zigelman To: ostp-ai-rfi Subject: [External] Comments regarding AI action plan Date: Saturday, March 15, 2025 11:39:30 PM CAUTION: This email originated from outside your organization. Exercise caution when opening attachments or clicking links, especially from unknown senders. Hello, It is my understanding that AI models compress training data down to a matrix that they then use to generate responses. Those responses, for some queries and some training data, mayreproduce the contents of the training data. This is not ok.This is a clear violation of copyright law. You are not allowed to make a thing that has a chance -- any chance -- of intentionally reproducing someone else's copyrightedwork. Should you doubt the validity of my claims, I would ask you to investigate OpenAI's implementation of a secondary layer on ChatGPT that checks to see if their AI model isdumping large quantities of copyrighted text to the user and stops the response mid-way if itdetects said copyrighted text. This suggests to me that modern AI models do, in fact, have theability (and the propensity!) to distribute copyrighted work. It is my belief that the government should strongly investigate one or both of the following stances: AI models should be treated as compressed data, and that all of their training data should be considered to be in their data, and/or all AI-generated data should immediately enterthe public domain. AI is directly responsible for a loss of American jobs, American productivity, and in a handfulof cases, American lives. it is astoundingly good at generating things that *look* correct butaren't. We do not need to protect American AI interests. We should instead be looking toprotect American intellectual work and prohibit the use of AI for tasks where it purports to present factual information. If you have any questions, please do not hesitate to get in touch.Tony Zigelman All e-mails to and from this account are for NITRD official use only and subject to certain disclosure requirements.If you have received this e-mail in error, we ask that you notify the sender and delete it immediately.",2131,342,page_content,page_1
