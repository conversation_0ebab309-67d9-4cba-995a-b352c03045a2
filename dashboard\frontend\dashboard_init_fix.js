/**
 * Dashboard Initialization Fix
 * This script fixes initialization issues by adapting to the actual DOM elements present
 */

(function() {
    console.log('Applying dashboard initialization fixes...');
    
    // Override the dashboard initialization check to use elements that actually exist
    document.addEventListener('DOMContentLoaded', function() {
        // Instead of checking for specific elements, we'll check if core container elements exist
        const requiredElements = [
            document.getElementById('wrapper'),
            document.getElementById('sidebarToggle'),
            document.getElementById('dashboard-section')
        ];
        
        const missingElements = requiredElements.filter(el => !el);
        if (missingElements.length > 0) {
            console.error('Some required elements are missing. Dashboard initialization may fail.');
        } else {
            console.log('All required elements found, dashboard should initialize properly.');
            
            // Make sure total-documents element is available even if it's not in the DOM
            if (!document.getElementById('total-documents')) {
                // Find a suitable container for the total documents count
                const metricsContainer = document.querySelector('.card-body .row .col-md-4 .p-3');
                if (metricsContainer) {
                    // If we found a suitable container but no total-documents element,
                    // we'll create one with the correct ID
                    const displayElement = metricsContainer.querySelector('h3');
                    if (displayElement) {
                        displayElement.id = 'total-documents';
                        console.log('Added missing id to total-documents element');
                    }
                }
            }
        }
    });
    
    console.log('Dashboard initialization fixes applied successfully');
})();
