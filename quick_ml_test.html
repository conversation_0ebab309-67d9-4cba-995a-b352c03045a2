<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick ML Analysis Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Quick ML Analysis Test</h1>
        <p>This page tests the ML analysis functionality to verify the "Cannot read properties of undefined" fix.</p>
        
        <div class="test-section">
            <h3>🔧 Test Controls</h3>
            <button onclick="testMLModelsLoading()">Test ML Models Loading</button>
            <button onclick="testSentimentAnalysis()">Test Sentiment Analysis</button>
            <button onclick="testClassificationAnalysis()">Test Classification Analysis</button>
            <button onclick="testAllModelTypes()">Test All Model Types</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>
        
        <div class="test-section">
            <h3>📊 Test Results</h3>
            <div id="testResults">No tests run yet.</div>
        </div>
        
        <div class="test-section">
            <h3>📝 Console Log</h3>
            <div id="consoleLog" class="log">Console output will appear here...</div>
        </div>
    </div>

    <script>
        // Mock the dashboard.js ML functionality for testing
        let mlModels = {};
        let testResults = [];
        
        // Override console.log to capture output
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        
        function logToPage(message, type = 'log') {
            const logDiv = document.getElementById('consoleLog');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : '📝';
            logDiv.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            logToPage(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            logToPage(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            logToPage(args.join(' '), 'warn');
        };
        
        // Copy the fixed ML functions from dashboard.js
        function loadMLModels() {
            try {
                mlModels = {
                    sentiment: {
                        name: 'Sentiment Analysis Model',
                        version: '2.1.0',
                        accuracy: 0.942,
                        precision: 0.918,
                        recall: 0.895,
                        f1_score: 0.906,
                        last_trained: '2025-08-01',
                        features: ['text_content', 'word_count', 'organization_type', 'sector']
                    },
                    classification: {
                        name: 'Policy Stance Classifier',
                        version: '3.2.0',
                        accuracy: 0.889,
                        precision: 0.876,
                        recall: 0.901,
                        f1_score: 0.888,
                        last_trained: '2025-08-03',
                        features: ['text_features', 'organization_features', 'sentiment_features']
                    }
                };
                
                console.log('🤖 ML models loaded successfully:', Object.keys(mlModels));
                return true;
            } catch (error) {
                console.error('❌ Failed to load ML models:', error);
                mlModels = {};
                return false;
            }
        }
        
        function generateSentimentAnalysisResults(dataSource, confidenceThreshold) {
            // Safely get model info with fallback
            const modelInfo = (mlModels && mlModels.sentiment) ? mlModels.sentiment : {
                name: 'Sentiment Analysis Model',
                version: '2.1.0',
                accuracy: 0.942,
                precision: 0.918,
                recall: 0.895,
                f1_score: 0.906,
                last_trained: '2025-08-01',
                features: ['text_content', 'word_count', 'organization_type', 'sector']
            };

            return {
                model_type: 'sentiment',
                model_info: modelInfo,
                data_source: dataSource,
                confidence_threshold: confidenceThreshold,
                results: {
                    sentiment_distribution: { positive: 0.42, neutral: 0.38, negative: 0.20 },
                    insights: ['Test sentiment analysis completed successfully']
                },
                analysis_timestamp: new Date().toISOString()
            };
        }
        
        function generateClassificationResults(dataSource, confidenceThreshold) {
            // Safely get model info with fallback
            const modelInfo = (mlModels && mlModels.classification) ? mlModels.classification : {
                name: 'Policy Stance Classifier',
                version: '3.2.0',
                accuracy: 0.889,
                precision: 0.876,
                recall: 0.901,
                f1_score: 0.888,
                last_trained: '2025-08-03',
                features: ['text_features', 'organization_features', 'sentiment_features']
            };

            return {
                model_type: 'classification',
                model_info: modelInfo,
                data_source: dataSource,
                confidence_threshold: confidenceThreshold,
                results: {
                    policy_stance_distribution: { self_regulation: 0.42, co_regulation: 0.31, government_oversight: 0.27 },
                    insights: ['Test classification analysis completed successfully']
                },
                analysis_timestamp: new Date().toISOString()
            };
        }
        
        function updateModelPerformance(modelInfo) {
            // Check if modelInfo is valid and has required properties
            if (!modelInfo || typeof modelInfo !== 'object') {
                console.warn('⚠️ Invalid modelInfo provided to updateModelPerformance:', modelInfo);
                return false;
            }

            // Provide default values for missing properties
            const safeModelInfo = {
                accuracy: modelInfo.accuracy || 0,
                precision: modelInfo.precision || 0,
                recall: modelInfo.recall || 0,
                f1_score: modelInfo.f1_score || 0,
                name: modelInfo.name || 'Unknown Model',
                version: modelInfo.version || '1.0.0'
            };

            console.log(`📊 Model Performance: Accuracy=${(safeModelInfo.accuracy * 100).toFixed(1)}%, Precision=${(safeModelInfo.precision * 100).toFixed(1)}%`);
            return true;
        }
        
        // Test functions
        function testMLModelsLoading() {
            console.log('🧪 Testing ML models loading...');
            const result = loadMLModels();
            updateTestResults('ML Models Loading', result, result ? 'Models loaded successfully' : 'Failed to load models');
        }
        
        function testSentimentAnalysis() {
            console.log('🧪 Testing sentiment analysis...');
            try {
                const results = generateSentimentAnalysisResults('all', 0.8);
                const performanceResult = updateModelPerformance(results.model_info);
                updateTestResults('Sentiment Analysis', performanceResult, 'Sentiment analysis completed without errors');
            } catch (error) {
                console.error('Sentiment analysis test failed:', error);
                updateTestResults('Sentiment Analysis', false, error.message);
            }
        }
        
        function testClassificationAnalysis() {
            console.log('🧪 Testing classification analysis...');
            try {
                const results = generateClassificationResults('all', 0.8);
                const performanceResult = updateModelPerformance(results.model_info);
                updateTestResults('Classification Analysis', performanceResult, 'Classification analysis completed without errors');
            } catch (error) {
                console.error('Classification analysis test failed:', error);
                updateTestResults('Classification Analysis', false, error.message);
            }
        }
        
        function testAllModelTypes() {
            console.log('🧪 Testing all model types...');
            testMLModelsLoading();
            setTimeout(() => testSentimentAnalysis(), 100);
            setTimeout(() => testClassificationAnalysis(), 200);
        }
        
        function updateTestResults(testName, success, message) {
            testResults.push({ name: testName, success, message, timestamp: new Date() });
            
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<h4>Test Results:</h4>';
            
            testResults.forEach(result => {
                const div = document.createElement('div');
                div.className = `test-section ${result.success ? 'success' : 'error'}`;
                div.innerHTML = `
                    <strong>${result.success ? '✅' : '❌'} ${result.name}</strong><br>
                    ${result.message}<br>
                    <small>Time: ${result.timestamp.toLocaleTimeString()}</small>
                `;
                resultsDiv.appendChild(div);
            });
        }
        
        function clearLog() {
            document.getElementById('consoleLog').textContent = 'Console cleared...\n';
            testResults = [];
            document.getElementById('testResults').innerHTML = 'No tests run yet.';
        }
        
        // Auto-run initial test
        window.onload = function() {
            console.log('🚀 Quick ML Test Page Loaded');
            console.log('Click the test buttons to verify ML functionality');
        };
    </script>
</body>
</html>
