/**
 * <PERSON>le uploaded files
 */
function handleFiles(files) {
    if (files.length === 0) return;
    
    console.log(`📁 Processing ${files.length} file(s)...`);
    
    const uploadProgress = document.getElementById('uploadProgress');
    const progressBar = document.getElementById('progressBar');
    const uploadStatus = document.getElementById('uploadStatus');
    
    if (!uploadProgress || !progressBar || !uploadStatus) return;
    
    uploadProgress.style.display = 'block';
    
    // Simulate upload progress
    let progress = 0;
    const interval = setInterval(() => {
        progress += 10;
        progressBar.style.width = progress + '%';
        progressBar.textContent = progress + '%';
        
        if (progress >= 100) {
            clearInterval(interval);
            
            // Show completion status
            uploadStatus.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i> 
                    Successfully uploaded ${files.length} file(s). Analysis will begin shortly.
                </div>
            `;
            
            // Add to upload history
            addToUploadHistory(files);
            
            // Reset after 3 seconds
            setTimeout(() => {
                uploadProgress.style.display = 'none';
                progressBar.style.width = '0%';
                progressBar.textContent = '';
                uploadStatus.innerHTML = '';
            }, 3000);
        }
    }, 200);
}

/**
 * Add uploaded files to history
 */
function addToUploadHistory(files) {
    const uploadHistory = document.getElementById('uploadHistory');
    if (!uploadHistory) return;
    
    // Clear "No recent uploads" message if present
    if (uploadHistory.innerHTML.includes('No recent uploads')) {
        uploadHistory.innerHTML = '';
    }
    
    // Add each file to history
    Array.from(files).forEach(file => {
        const row = document.createElement('tr');
        const now = new Date();
        
        row.innerHTML = `
            <td>${file.name}</td>
            <td>${now.toLocaleDateString()} ${now.toLocaleTimeString()}</td>
            <td><span class="badge bg-success">Processed</span></td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="viewAnalysis('${file.name}')">
                    <i class="fas fa-eye me-1"></i> View
                </button>
            </td>
        `;
        
        uploadHistory.prepend(row);
    });
}

/**
 * Setup search functionality
 */
function setupSearch() {
    // Search suggestions (could be implemented here)
    const searchInput = document.getElementById('searchInput');
    
    if (!searchInput) return;
    
    searchInput.addEventListener('input', function() {
        const query = this.value;
        if (query.length > 2) {
            // Could implement search suggestions here
            console.log('Search suggestions for:', query);
        }
    });
}

/**
 * Perform search
 */
async function performSearch() {
    const query = document.getElementById('searchInput').value.trim();
    const orgTypeFilter = document.getElementById('orgTypeFilter')?.value || '';
    const sectorFilter = document.getElementById('sectorFilter')?.value || '';
    const analysisFilter = document.getElementById('analysisFilter')?.value || '';
    
    // Navigate to search section if not already there
    navigateToSection('search');
    
    if (!query && !orgTypeFilter && !sectorFilter && !analysisFilter) {
        showNotification('Please enter a search query or select filters.', 'warning');
        return;
    }
    
    try {
        showLoading(true);
        
        let searchUrl = `${API_CONFIG.search}/search`;
        const params = new URLSearchParams();
        
        if (query) params.append('query', query);
        if (orgTypeFilter) params.append('organization_type', orgTypeFilter);
        if (sectorFilter) params.append('sector', sectorFilter);
        
        if (params.toString()) {
            searchUrl += '?' + params.toString();
        }
        
        const response = await fetch(searchUrl);
        const data = await response.json();
        
        if (response.ok) {
            displaySearchResults(data);
        } else {
            throw new Error(data.error || 'Search failed');
        }
        
    } catch (error) {
        console.error('Search error:', error);
        
        // Show sample results instead
        displaySampleResults();
        
        showNotification('Search API not available. Showing sample results.', 'info');
    } finally {
        showLoading(false);
    }
}

/**
 * Navigate to a specific section
 */
function navigateToSection(sectionName) {
    const navLink = document.querySelector(`.list-group-item[data-section="${sectionName}"]`);
    if (navLink) {
        navLink.click();
    }
}

/**
 * Display search results
 */
function displaySearchResults(data) {
    const resultsContainer = document.getElementById('searchResults');
    if (!resultsContainer) return;
    
    if (!data.results || data.results.length === 0) {
        // Show sample results if no results found
        displaySampleResults();
        return;
    }
    
    let resultsHTML = `
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5>Search Results (${data.total_results || data.results.length})</h5>
            <small class="text-muted">Query: "${data.query || 'filtered search'}"</small>
        </div>
    `;
    
    resultsHTML += createResultsTable(data.results);
    
    resultsContainer.innerHTML = resultsHTML;
    dashboardState.searchResults = data.results;
}

/**
 * Display sample search results
 */
function displaySampleResults() {
    const sampleResults = [
        {
            document_id: 'google_llc_analysis',
            organization_name: 'Google LLC',
            organization_type: 'Corporate',
            sector: 'Technology',  
            document_type: 'AI Policy Statement',
            relevance_score: '95%'
        },
        {
            document_id: '1day_sooner_analysis',
            organization_name: '1Day Sooner',
            organization_type: 'Nonprofit',
            sector: 'Research',
            document_type: 'AI RFI Response',
            relevance_score: '92%'
        },
        {
            document_id: 'microsoft_corporation_analysis',
            organization_name: 'Microsoft Corporation',
            organization_type: 'Corporate',
            sector: 'Technology',
            document_type: 'Policy Framework',
            relevance_score: '88%'
        },
        {
            document_id: '3c_analysis',
            organization_name: '3C',
            organization_type: 'Academic',
            sector: 'Research',
            document_type: 'Position Paper',
            relevance_score: '85%'
        },
        {
            document_id: 'openai_analysis',
            organization_name: 'OpenAI',
            organization_type: 'Corporate',
            sector: 'AI Research',
            document_type: 'Safety Guidelines',
            relevance_score: '90%'
        }
    ];
    
    const resultsContainer = document.getElementById('searchResults');
    if (!resultsContainer) return;
    
    let resultsHTML = `
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5>Sample Analysis Results (${sampleResults.length})</h5>
            <small class="text-muted">Click "View" to see detailed analysis</small>
        </div>
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i> 
            These are sample results from the analysis database. Search functionality requires API connections.
        </div>
    `;
    
    resultsHTML += createResultsTable(sampleResults);
    
    resultsContainer.innerHTML = resultsHTML;
    dashboardState.searchResults = sampleResults;
}
