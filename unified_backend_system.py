#!/usr/bin/env python3
"""
统一后端数据处理系统 - AI Policy Dashboard
集成历史数据处理、批量分析、API接口

Author: Claude Code
Date: 2025-08-08
"""

import os
import sys
import json
import sqlite3
import asyncio
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
from flask import Flask, jsonify, request, cors
from flask_cors import CORS
import threading
import queue
import time

# 添加路径
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))
sys.path.append(str(current_dir / 'dashboard' / 'backend'))

# 导入现有组件
try:
    from phase2_policy_narrative_analysis import PolicyNarrativeAnalyzer
    from dashboard.backend.new_historical_integration import NewHistoricalDataLoader
    from dashboard.backend.real_analysis_engine import RealAnalysisEngine
    ANALYSIS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 分析组件导入失败: {e}")
    ANALYSIS_AVAILABLE = False

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('unified_backend.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class UnifiedBackendSystem:
    """统一后端数据处理系统"""
    
    def __init__(self, data_directory: str = "data/90_FR_9088_pdfs"):
        """初始化统一后端系统"""
        logger.info("🚀 初始化统一后端数据处理系统...")
        
        self.data_directory = Path(data_directory)
        self.db_path = "unified_analysis_results.db"
        self.processing_queue = queue.Queue()
        self.is_processing = False
        
        # 初始化组件
        self._initialize_components()
        self._initialize_database()
        self._start_background_processor()
        
        logger.info("✅ 统一后端系统初始化完成")
    
    def _initialize_components(self):
        """初始化分析组件"""
        if ANALYSIS_AVAILABLE:
            self.narrative_analyzer = PolicyNarrativeAnalyzer()
            self.historical_loader = NewHistoricalDataLoader(str(self.data_directory))
            self.analysis_engine = RealAnalysisEngine(str(self.data_directory))
            logger.info("✅ 分析组件初始化完成")
        else:
            logger.warning("⚠️ 分析组件不可用，使用模拟模式")
            self.narrative_analyzer = None
            self.historical_loader = None
            self.analysis_engine = None
    
    def _initialize_database(self):
        """初始化数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建主要数据表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS documents (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    filename TEXT UNIQUE NOT NULL,
                    organization TEXT,
                    document_type TEXT,
                    file_path TEXT,
                    file_size INTEGER,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    processed_date TIMESTAMP,
                    processing_status TEXT DEFAULT 'pending',
                    text_content TEXT,
                    analysis_results TEXT
                )
            ''')
            
            # 创建分析结果表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS analysis_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    document_id INTEGER,
                    analysis_type TEXT,
                    analysis_version TEXT,
                    results TEXT,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (document_id) REFERENCES documents (id)
                )
            ''')
            
            # 创建处理队列表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS processing_queue (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    document_id INTEGER,
                    priority INTEGER DEFAULT 1,
                    status TEXT DEFAULT 'queued',
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    started_date TIMESTAMP,
                    completed_date TIMESTAMP,
                    error_message TEXT,
                    FOREIGN KEY (document_id) REFERENCES documents (id)
                )
            ''')
            
            # 创建系统统计表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_stats (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stat_name TEXT UNIQUE,
                    stat_value TEXT,
                    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            logger.info("✅ 数据库初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 数据库初始化失败: {e}")
    
    def _start_background_processor(self):
        """启动后台处理器"""
        def background_worker():
            while True:
                try:
                    if not self.processing_queue.empty():
                        task = self.processing_queue.get()
                        self._process_document_task(task)
                    else:
                        time.sleep(1)
                except Exception as e:
                    logger.error(f"❌ 后台处理错误: {e}")
        
        processor_thread = threading.Thread(target=background_worker, daemon=True)
        processor_thread.start()
        logger.info("✅ 后台处理器启动完成")
    
    def scan_and_index_documents(self) -> Dict[str, Any]:
        """扫描并索引所有文档"""
        logger.info("📁 开始扫描和索引文档...")
        
        results = {
            'total_files': 0,
            'new_files': 0,
            'existing_files': 0,
            'errors': [],
            'scan_time': datetime.now().isoformat()
        }
        
        try:
            # 扫描PDF目录
            if self.data_directory.exists():
                pdf_files = list(self.data_directory.glob('*.pdf'))
                results['total_files'] = len(pdf_files)
                
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                for pdf_file in pdf_files:
                    try:
                        # 检查文件是否已存在
                        cursor.execute('SELECT id FROM documents WHERE filename = ?', (pdf_file.name,))
                        existing = cursor.fetchone()
                        
                        if not existing:
                            # 解析组织信息
                            org_info = self._parse_filename(pdf_file.name)
                            
                            # 插入新文档记录
                            cursor.execute('''
                                INSERT INTO documents (filename, organization, document_type, file_path, file_size)
                                VALUES (?, ?, ?, ?, ?)
                            ''', (
                                pdf_file.name,
                                org_info.get('organization', 'Unknown'),
                                org_info.get('document_type', 'PDF'),
                                str(pdf_file),
                                pdf_file.stat().st_size
                            ))
                            
                            results['new_files'] += 1
                            
                            # 添加到处理队列
                            document_id = cursor.lastrowid
                            self.add_to_processing_queue(document_id)
                            
                        else:
                            results['existing_files'] += 1
                            
                    except Exception as e:
                        error_msg = f"处理文件 {pdf_file.name} 时出错: {e}"
                        results['errors'].append(error_msg)
                        logger.error(error_msg)
                
                conn.commit()
                conn.close()
                
                logger.info(f"📊 扫描完成: {results['total_files']} 总文件, {results['new_files']} 新文件")
                
            else:
                logger.warning(f"⚠️ 数据目录不存在: {self.data_directory}")
                
        except Exception as e:
            error_msg = f"扫描索引过程出错: {e}"
            results['errors'].append(error_msg)
            logger.error(error_msg)
        
        return results
    
    def _parse_filename(self, filename: str) -> Dict[str, str]:
        """解析文件名获取组织信息"""
        # 移除扩展名
        name_without_ext = filename.replace('.pdf', '').replace('.csv', '')
        
        # 基本解析逻辑
        parts = name_without_ext.split('-')
        
        if len(parts) >= 2:
            organization = parts[0]
            document_type = 'AI-RFI-Response'
        else:
            organization = name_without_ext
            document_type = 'Unknown'
        
        return {
            'organization': organization,
            'document_type': document_type,
            'filename_base': name_without_ext
        }
    
    def add_to_processing_queue(self, document_id: int, priority: int = 1):
        """添加文档到处理队列"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO processing_queue (document_id, priority)
                VALUES (?, ?)
            ''', (document_id, priority))
            
            conn.commit()
            conn.close()
            
            # 添加到内存队列
            self.processing_queue.put({
                'document_id': document_id,
                'priority': priority,
                'timestamp': datetime.now()
            })
            
            logger.info(f"📝 文档 {document_id} 已添加到处理队列")
            
        except Exception as e:
            logger.error(f"❌ 添加到处理队列失败: {e}")
    
    def _process_document_task(self, task: Dict):
        """处理单个文档任务"""
        document_id = task['document_id']
        logger.info(f"🔄 开始处理文档 {document_id}")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取文档信息
            cursor.execute('SELECT * FROM documents WHERE id = ?', (document_id,))
            doc_info = cursor.fetchone()
            
            if not doc_info:
                logger.error(f"❌ 文档 {document_id} 不存在")
                return
            
            # 更新处理状态
            cursor.execute('''
                UPDATE processing_queue 
                SET status = 'processing', started_date = CURRENT_TIMESTAMP 
                WHERE document_id = ?
            ''', (document_id,))
            
            cursor.execute('''
                UPDATE documents 
                SET processing_status = 'processing' 
                WHERE id = ?
            ''', (document_id,))
            
            conn.commit()
            
            # 执行实际分析
            analysis_results = self._analyze_document(doc_info)
            
            # 保存分析结果
            cursor.execute('''
                UPDATE documents 
                SET analysis_results = ?, processing_status = 'completed', processed_date = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (json.dumps(analysis_results), document_id))
            
            cursor.execute('''
                UPDATE processing_queue 
                SET status = 'completed', completed_date = CURRENT_TIMESTAMP 
                WHERE document_id = ?
            ''', (document_id,))
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ 文档 {document_id} 处理完成")
            
        except Exception as e:
            error_msg = f"处理文档 {document_id} 时出错: {e}"
            logger.error(error_msg)
            
            # 更新错误状态
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                cursor.execute('''
                    UPDATE processing_queue 
                    SET status = 'error', error_message = ?, completed_date = CURRENT_TIMESTAMP 
                    WHERE document_id = ?
                ''', (error_msg, document_id))
                
                cursor.execute('''
                    UPDATE documents 
                    SET processing_status = 'error' 
                    WHERE id = ?
                ''', (document_id,))
                
                conn.commit()
                conn.close()
            except Exception as e2:
                logger.error(f"❌ 更新错误状态失败: {e2}")
    
    def _analyze_document(self, doc_info: tuple) -> Dict[str, Any]:
        """分析单个文档"""
        doc_id, filename, organization, doc_type, file_path, file_size, created_date, processed_date, status, text_content, analysis_results = doc_info
        
        results = {
            'document_id': doc_id,
            'filename': filename,
            'organization': organization,
            'analysis_timestamp': datetime.now().isoformat(),
            'analysis_version': '2.0'
        }
        
        try:
            # 如果有分析引擎，使用真实分析
            if self.analysis_engine and ANALYSIS_AVAILABLE:
                # 提取文本内容
                if not text_content:
                    text_content = self.analysis_engine.extract_text_from_pdf(file_path)
                
                # 执行政策叙事分析
                if text_content and self.narrative_analyzer:
                    narrative_results = self.narrative_analyzer.comprehensive_narrative_analysis(
                        text_content, 
                        metadata={'filename': filename, 'organization': organization}
                    )
                    results.update(narrative_results)
                
            else:
                # 模拟分析结果
                results.update(self._generate_mock_analysis(filename, organization))
            
        except Exception as e:
            logger.error(f"❌ 分析文档 {filename} 时出错: {e}")
            results['error'] = str(e)
        
        return results
    
    def _generate_mock_analysis(self, filename: str, organization: str) -> Dict[str, Any]:
        """生成模拟分析结果"""
        import random
        
        return {
            'text_stats': {
                'word_count': random.randint(1000, 5000),
                'sentence_count': random.randint(50, 200),
                'paragraph_count': random.randint(10, 50)
            },
            'sentiment_tone': {
                'overall_sentiment': random.choice(['positive', 'neutral', 'negative']),
                'confidence': random.uniform(0.7, 0.95)
            },
            'policy_solutions': {
                'regulation_focus': random.uniform(0.3, 0.8),
                'innovation_focus': random.uniform(0.2, 0.7),
                'collaboration_focus': random.uniform(0.4, 0.9)
            },
            'ai_specific_analysis': {
                'ai_governance_mentions': random.randint(5, 25),
                'ai_safety_mentions': random.randint(3, 15),
                'ai_innovation_mentions': random.randint(8, 30)
            }
        }
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取文档统计
            cursor.execute('SELECT COUNT(*) FROM documents')
            total_docs = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM documents WHERE processing_status = "completed"')
            processed_docs = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM documents WHERE processing_status = "processing"')
            processing_docs = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM processing_queue WHERE status = "queued"')
            queued_docs = cursor.fetchone()[0]
            
            conn.close()
            
            return {
                'total_documents': total_docs,
                'processed_documents': processed_docs,
                'processing_documents': processing_docs,
                'queued_documents': queued_docs,
                'processing_rate': round((processed_docs / total_docs * 100), 2) if total_docs > 0 else 0,
                'system_status': 'running',
                'last_updated': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ 获取系统状态失败: {e}")
            return {
                'error': str(e),
                'system_status': 'error',
                'last_updated': datetime.now().isoformat()
            }

# Flask API 应用
app = Flask(__name__)
CORS(app)

# 全局后端系统实例
backend_system = None

def initialize_backend():
    """初始化后端系统"""
    global backend_system
    if backend_system is None:
        backend_system = UnifiedBackendSystem()
    return backend_system

@app.route('/api/system/status', methods=['GET'])
def get_system_status():
    """获取系统状态API"""
    try:
        system = initialize_backend()
        status = system.get_system_status()
        return jsonify(status)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/documents/scan', methods=['POST'])
def scan_documents():
    """扫描文档API"""
    try:
        system = initialize_backend()
        results = system.scan_and_index_documents()
        return jsonify(results)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/documents/list', methods=['GET'])
def list_documents():
    """列出文档API"""
    try:
        system = initialize_backend()
        
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        status = request.args.get('status', None)
        
        conn = sqlite3.connect(system.db_path)
        cursor = conn.cursor()
        
        # 构建查询
        where_clause = ""
        params = []
        
        if status:
            where_clause = "WHERE processing_status = ?"
            params.append(status)
        
        # 获取总数
        cursor.execute(f'SELECT COUNT(*) FROM documents {where_clause}', params)
        total = cursor.fetchone()[0]
        
        # 获取分页数据
        offset = (page - 1) * per_page
        cursor.execute(f'''
            SELECT id, filename, organization, document_type, file_size, 
                   created_date, processed_date, processing_status
            FROM documents {where_clause}
            ORDER BY created_date DESC
            LIMIT ? OFFSET ?
        ''', params + [per_page, offset])
        
        documents = []
        for row in cursor.fetchall():
            documents.append({
                'id': row[0],
                'filename': row[1],
                'organization': row[2],
                'document_type': row[3],
                'file_size': row[4],
                'created_date': row[5],
                'processed_date': row[6],
                'processing_status': row[7]
            })
        
        conn.close()
        
        return jsonify({
            'documents': documents,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total,
                'pages': (total + per_page - 1) // per_page
            }
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/documents/<int:doc_id>/analysis', methods=['GET'])
def get_document_analysis(doc_id):
    """获取文档分析结果API"""
    try:
        system = initialize_backend()

        conn = sqlite3.connect(system.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT filename, organization, analysis_results, processed_date
            FROM documents WHERE id = ?
        ''', (doc_id,))

        result = cursor.fetchone()
        conn.close()

        if not result:
            return jsonify({'error': 'Document not found'}), 404

        filename, organization, analysis_results, processed_date = result

        if analysis_results:
            analysis_data = json.loads(analysis_results)
        else:
            analysis_data = None

        return jsonify({
            'document_id': doc_id,
            'filename': filename,
            'organization': organization,
            'processed_date': processed_date,
            'analysis_results': analysis_data
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/analytics/summary', methods=['GET'])
def get_analytics_summary():
    """获取分析摘要API"""
    try:
        system = initialize_backend()

        conn = sqlite3.connect(system.db_path)
        cursor = conn.cursor()

        # 获取组织统计
        cursor.execute('''
            SELECT organization, COUNT(*) as doc_count
            FROM documents
            WHERE processing_status = 'completed'
            GROUP BY organization
            ORDER BY doc_count DESC
            LIMIT 10
        ''')
        top_organizations = [{'organization': row[0], 'document_count': row[1]} for row in cursor.fetchall()]

        # 获取处理状态统计
        cursor.execute('''
            SELECT processing_status, COUNT(*) as count
            FROM documents
            GROUP BY processing_status
        ''')
        status_stats = {row[0]: row[1] for row in cursor.fetchall()}

        # 获取每日处理统计
        cursor.execute('''
            SELECT DATE(processed_date) as date, COUNT(*) as count
            FROM documents
            WHERE processed_date IS NOT NULL
            GROUP BY DATE(processed_date)
            ORDER BY date DESC
            LIMIT 30
        ''')
        daily_processing = [{'date': row[0], 'count': row[1]} for row in cursor.fetchall()]

        conn.close()

        return jsonify({
            'top_organizations': top_organizations,
            'status_statistics': status_stats,
            'daily_processing': daily_processing,
            'generated_at': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/analytics/sentiment', methods=['GET'])
def get_sentiment_analytics():
    """获取情感分析统计API"""
    try:
        system = initialize_backend()

        conn = sqlite3.connect(system.db_path)
        cursor = conn.cursor()

        # 获取已处理的文档分析结果
        cursor.execute('''
            SELECT analysis_results, organization
            FROM documents
            WHERE processing_status = 'completed' AND analysis_results IS NOT NULL
        ''')

        sentiment_data = []
        organization_sentiment = {}

        for row in cursor.fetchall():
            try:
                analysis = json.loads(row[0])
                organization = row[1]

                if 'sentiment_tone' in analysis:
                    sentiment = analysis['sentiment_tone']
                    sentiment_data.append(sentiment)

                    if organization not in organization_sentiment:
                        organization_sentiment[organization] = []
                    organization_sentiment[organization].append(sentiment)

            except (json.JSONDecodeError, KeyError):
                continue

        # 计算总体情感统计
        if sentiment_data:
            positive_count = sum(1 for s in sentiment_data if s.get('overall_sentiment') == 'positive')
            neutral_count = sum(1 for s in sentiment_data if s.get('overall_sentiment') == 'neutral')
            negative_count = sum(1 for s in sentiment_data if s.get('overall_sentiment') == 'negative')

            total = len(sentiment_data)
            overall_stats = {
                'positive': round(positive_count / total * 100, 2),
                'neutral': round(neutral_count / total * 100, 2),
                'negative': round(negative_count / total * 100, 2),
                'total_analyzed': total
            }
        else:
            overall_stats = {
                'positive': 0,
                'neutral': 0,
                'negative': 0,
                'total_analyzed': 0
            }

        # 计算组织情感统计
        org_sentiment_stats = {}
        for org, sentiments in organization_sentiment.items():
            if sentiments:
                positive = sum(1 for s in sentiments if s.get('overall_sentiment') == 'positive')
                neutral = sum(1 for s in sentiments if s.get('overall_sentiment') == 'neutral')
                negative = sum(1 for s in sentiments if s.get('overall_sentiment') == 'negative')
                total = len(sentiments)

                org_sentiment_stats[org] = {
                    'positive': round(positive / total * 100, 2),
                    'neutral': round(neutral / total * 100, 2),
                    'negative': round(negative / total * 100, 2),
                    'total': total
                }

        conn.close()

        return jsonify({
            'overall_sentiment': overall_stats,
            'organization_sentiment': org_sentiment_stats,
            'generated_at': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/processing/start', methods=['POST'])
def start_batch_processing():
    """启动批量处理API"""
    try:
        system = initialize_backend()

        # 获取请求参数
        data = request.get_json() or {}
        priority = data.get('priority', 1)
        limit = data.get('limit', None)

        conn = sqlite3.connect(system.db_path)
        cursor = conn.cursor()

        # 获取待处理文档
        query = '''
            SELECT id FROM documents
            WHERE processing_status = 'pending'
            ORDER BY created_date ASC
        '''

        if limit:
            query += f' LIMIT {limit}'

        cursor.execute(query)
        pending_docs = cursor.fetchall()

        # 添加到处理队列
        added_count = 0
        for doc_row in pending_docs:
            doc_id = doc_row[0]
            system.add_to_processing_queue(doc_id, priority)
            added_count += 1

        conn.close()

        return jsonify({
            'message': f'已添加 {added_count} 个文档到处理队列',
            'documents_added': added_count,
            'priority': priority,
            'started_at': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/processing/queue', methods=['GET'])
def get_processing_queue():
    """获取处理队列状态API"""
    try:
        system = initialize_backend()

        conn = sqlite3.connect(system.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT pq.id, pq.document_id, d.filename, d.organization,
                   pq.priority, pq.status, pq.created_date, pq.started_date,
                   pq.completed_date, pq.error_message
            FROM processing_queue pq
            JOIN documents d ON pq.document_id = d.id
            ORDER BY pq.priority DESC, pq.created_date ASC
            LIMIT 100
        ''')

        queue_items = []
        for row in cursor.fetchall():
            queue_items.append({
                'queue_id': row[0],
                'document_id': row[1],
                'filename': row[2],
                'organization': row[3],
                'priority': row[4],
                'status': row[5],
                'created_date': row[6],
                'started_date': row[7],
                'completed_date': row[8],
                'error_message': row[9]
            })

        conn.close()

        return jsonify({
            'queue_items': queue_items,
            'total_items': len(queue_items),
            'generated_at': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/search', methods=['GET'])
def search_documents():
    """搜索文档API"""
    try:
        system = initialize_backend()

        # 获取搜索参数
        query = request.args.get('q', '')
        organization = request.args.get('organization', '')
        status = request.args.get('status', '')
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        conn = sqlite3.connect(system.db_path)
        cursor = conn.cursor()

        # 构建搜索查询
        where_conditions = []
        params = []

        if query:
            where_conditions.append('(filename LIKE ? OR organization LIKE ?)')
            params.extend([f'%{query}%', f'%{query}%'])

        if organization:
            where_conditions.append('organization = ?')
            params.append(organization)

        if status:
            where_conditions.append('processing_status = ?')
            params.append(status)

        where_clause = ''
        if where_conditions:
            where_clause = 'WHERE ' + ' AND '.join(where_conditions)

        # 获取总数
        cursor.execute(f'SELECT COUNT(*) FROM documents {where_clause}', params)
        total = cursor.fetchone()[0]

        # 获取搜索结果
        offset = (page - 1) * per_page
        cursor.execute(f'''
            SELECT id, filename, organization, document_type, file_size,
                   created_date, processed_date, processing_status
            FROM documents {where_clause}
            ORDER BY created_date DESC
            LIMIT ? OFFSET ?
        ''', params + [per_page, offset])

        results = []
        for row in cursor.fetchall():
            results.append({
                'id': row[0],
                'filename': row[1],
                'organization': row[2],
                'document_type': row[3],
                'file_size': row[4],
                'created_date': row[5],
                'processed_date': row[6],
                'processing_status': row[7]
            })

        conn.close()

        return jsonify({
            'results': results,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total,
                'pages': (total + per_page - 1) // per_page
            },
            'search_params': {
                'query': query,
                'organization': organization,
                'status': status
            }
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    print("🚀 启动统一后端数据处理系统...")

    # 初始化系统
    backend_system = initialize_backend()

    # 启动Flask应用
    app.run(host='0.0.0.0', port=5001, debug=True)
