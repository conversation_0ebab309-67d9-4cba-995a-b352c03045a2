@echo off
echo Starting AI Policy Analyzer Dashboard (Quick Start Mode)...

REM Stop any running services first
echo Stopping existing services...
taskkill /F /FI "WINDOWTITLE eq Analytics API*" 2>NUL
taskkill /F /FI "WINDOWTITLE eq Visualization API*" 2>NUL
taskkill /F /FI "WINDOWTITLE eq Search API*" 2>NUL
taskkill /F /FI "WINDOWTITLE eq Historical Data API*" 2>NUL
taskkill /F /FI "WINDOWTITLE eq Batch Processing API*" 2>NUL
taskkill /F /FI "WINDOWTITLE eq Frontend Server*" 2>NUL
timeout /t 2 /nobreak > NUL

REM Set working directory
cd /d "%~dp0"

REM Start API services with the quick-start version for historical data
echo.
echo Starting Visualization API on port 5001...
start "Visualization API" cmd /k python backend\visualization_api.py

timeout /t 1 /nobreak > NUL
echo Starting Search API on port 5002...
start "Search API" cmd /k python backend\search_api_server.py

timeout /t 1 /nobreak > NUL
echo Starting Quick Start Historical Data API on port 5003...
start "Historical Data API" cmd /k python backend\quick_start_historical_api.py

timeout /t 1 /nobreak > NUL
echo Starting Analytics API on port 5005...
start "Analytics API" cmd /k python backend\advanced_analytics_api.py

timeout /t 1 /nobreak > NUL
echo Starting Batch Processing API on port 5007...
start "Batch Processing API" cmd /k python backend\batch_processing_api.py

echo.
echo Starting Frontend Server on port 8080...
cd frontend
start "Frontend Server" cmd /k python -m http.server 8080

echo.
echo All services have been started!
echo Dashboard URL: http://localhost:8080/index.html
echo.
echo Opening dashboard in browser...
timeout /t 2 /nobreak > NUL
start http://localhost:8080/index.html
