# PowerShell script to find specific syntax errors that cause "Invalid left-hand side in assignment" errors

# List of files to check
$files = @(
    "dashboard_complete.js",
    "dashboard_complete_part2.js", 
    "dashboard_complete_part3.js",
    "dashboard_complete_part4.js",
    "dashboard_complete_part5.js",
    "dashboard_complete_part6.js",
    "dashboard_complete_part7.js",
    "dashboard_complete_part8.js",
    "dashboard_enhancements.js"
)

# Function to check a file for problematic code patterns
function Check-File {
    param (
        [string]$filePath
    )
    
    Write-Output "`nChecking $filePath for syntax errors..."
    
    if (Test-Path $filePath) {
        $content = Get-Content $filePath -Raw
        
        # Split the file into lines for better context
        $lines = $content -split "`n"
        
        # Look for common patterns that cause "Invalid left-hand side in assignment" errors
        # 1. if statement with single equals
        $ifMatches = [regex]::Matches($content, 'if\s*\(\s*([^=!<>]+=(?![=]))')
        foreach ($match in $ifMatches) {
            $lineNumber = ($content.Substring(0, $match.Index) -split "`n").Length
            $errorLine = $lines[$lineNumber-1]
            Write-Output "LIKELY ERROR: Single equals in if condition at line $lineNumber"
            Write-Output "  $errorLine"
        }
        
        # 2. while statement with single equals
        $whileMatches = [regex]::Matches($content, 'while\s*\(\s*([^=!<>]+=(?![=]))')
        foreach ($match in $whileMatches) {
            $lineNumber = ($content.Substring(0, $match.Index) -split "`n").Length
            $errorLine = $lines[$lineNumber-1]
            Write-Output "LIKELY ERROR: Single equals in while condition at line $lineNumber"
            Write-Output "  $errorLine"
        }
        
        # 3. Function call on the left side of assignment
        $funcCallMatches = [regex]::Matches($content, '(\w+\([^)]*\))\s*=\s*[^=]')
        foreach ($match in $funcCallMatches) {
            $lineNumber = ($content.Substring(0, $match.Index) -split "`n").Length
            $errorLine = $lines[$lineNumber-1]
            Write-Output "LIKELY ERROR: Function call on left side of assignment at line $lineNumber"
            Write-Output "  $errorLine"
        }
        
        # 4. Equality check with wrong number of equals signs
        $equalityMatches = [regex]::Matches($content, '(==(?!=)|===(?!=))\s*=(?!=)')
        foreach ($match in $equalityMatches) {
            $lineNumber = ($content.Substring(0, $match.Index) -split "`n").Length
            $errorLine = $lines[$lineNumber-1]
            Write-Output "LIKELY ERROR: Invalid equality check at line $lineNumber"
            Write-Output "  $errorLine"
        }
        
        # 5. Parenthesized expression on left of assignment
        $parensMatches = [regex]::Matches($content, '\([^)]+\)\s*=\s*[^=]')
        foreach ($match in $parensMatches) {
            $lineNumber = ($content.Substring(0, $match.Index) -split "`n").Length
            $errorLine = $lines[$lineNumber-1]
            Write-Output "LIKELY ERROR: Parenthesized expression on left side of assignment at line $lineNumber"
            Write-Output "  $errorLine"
        }
        
        # 6. Assigning to a literal value
        $literalMatches = [regex]::Matches($content, '(true|false|null|undefined|[0-9]+(\.[0-9]+)?)\s*=\s*[^=]')
        foreach ($match in $literalMatches) {
            $lineNumber = ($content.Substring(0, $match.Index) -split "`n").Length
            $errorLine = $lines[$lineNumber-1]
            Write-Output "LIKELY ERROR: Assigning to a literal at line $lineNumber"
            Write-Output "  $errorLine"
        }
    }
    else {
        Write-Output "File not found: $filePath"
    }
}

# Check each file
foreach ($file in $files) {
    Check-File -filePath $file
}

Write-Output "`nDone checking for syntax errors"
