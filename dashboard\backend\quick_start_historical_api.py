#!/usr/bin/env python3
"""
AI Policy Analyzer - Quick Start Analysis Results API
Provides REST API endpoints for analysis results storage and retrieval
Modified version that skips the initial data processing for faster startup
"""

import json
import os
import sys
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add backend path for imports
sys.path.append(os.path.dirname(__file__))

try:
    from flask import Flask, jsonify, request
    from flask_cors import CORS
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False
    print("Flask not available, using mock API mode")

from data_analysis_engine import PolicyAnalyzer, AnalysisStorageManager, BatchAnalysisProcessor

class AnalysisAPI:
    """Analysis results API handler"""
    
    def __init__(self):
        self.analyzer = PolicyAnalyzer()
        self.storage = AnalysisStorageManager()
        self.batch_processor = BatchAnalysisProcessor()
        
        if FLASK_AVAILABLE:
            self.app = Flask(__name__)
            CORS(self.app)
            self._setup_routes()
        else:
            self.app = None
    
    def _setup_routes(self):
        """Setup Flask routes"""
        @self.app.route('/api/historical/health', methods=['GET'])
        def historical_health_check():
            return jsonify({
                'status': 'healthy',
                'service': 'Historical Data API',
                'version': '1.0',
                'port': 5003
            })
        
        @self.app.route('/api/analysis/health', methods=['GET'])
        def analysis_health_check():
            return jsonify({
                'status': 'healthy',
                'service': 'Analysis API',
                'version': '1.0',
                'database_path': self.storage.db_path
            })
        
        @self.app.route('/api/analysis/analyze', methods=['POST'])
        def analyze_document():
            """Analyze a single document"""
            try:
                data = request.get_json()
                
                if not data or 'text' not in data:
                    return jsonify({'error': 'Missing text in request'}), 400
                
                text = data['text']
                metadata = data.get('metadata', {})
                
                # Perform analysis
                result = self.analyzer.analyze_document(text, metadata)
                
                if 'error' not in result:
                    # Store result
                    if self.storage.store_analysis(result):
                        return jsonify({
                            'success': True,
                            'analysis_id': result['analysis_id'],
                            'analysis_result': result
                        })
                    else:
                        return jsonify({'error': 'Failed to store analysis result'}), 500
                else:
                    return jsonify({'error': result['error']}), 400
                    
            except Exception as e:
                return jsonify({'error': f'Analysis failed: {str(e)}'}), 500
        
        @self.app.route('/api/analysis/results', methods=['GET'])
        def get_analysis_results():
            """Get analysis results with pagination"""
            try:
                limit = int(request.args.get('limit', 20))
                offset = int(request.args.get('offset', 0))
                date_range = request.args.get('date_range', '30')
                org_type = request.args.get('org_type', '')
                doc_type = request.args.get('doc_type', '')
                sort_field = request.args.get('sort', 'timestamp')
                sort_dir = request.args.get('sort_dir', 'desc')
                
                results = self.storage.get_filtered_analyses(
                    limit=limit, 
                    offset=offset,
                    date_range=date_range,
                    org_type=org_type,
                    doc_type=doc_type,
                    sort_field=sort_field,
                    sort_dir=sort_dir
                )
                
                return jsonify({
                    'success': True,
                    'results': results['results'],
                    'pagination': {
                        'limit': limit,
                        'offset': offset,
                        'total': results['total'],
                        'has_more': offset + limit < results['total']
                    }
                })
                
            except Exception as e:
                return jsonify({'error': f'Failed to get analysis results: {str(e)}'}), 500
        
        @self.app.route('/api/analysis/statistics', methods=['GET'])
        def get_analysis_statistics():
            """Get overall analysis statistics"""
            try:
                stats = self.storage.get_analysis_statistics()
                
                return jsonify({
                    'success': True,
                    'statistics': stats
                })
                
            except Exception as e:
                return jsonify({'error': f'Failed to get analysis statistics: {str(e)}'}), 500
        
        @self.app.route('/api/analysis/search', methods=['GET'])
        def search_analyses():
            """Search analysis results"""
            try:
                query = request.args.get('q', '')
                limit = int(request.args.get('limit', 20))
                offset = int(request.args.get('offset', 0))
                
                if not query:
                    return jsonify({'error': 'Missing search query'}), 400
                
                results = self.storage.search_analyses(query, limit, offset)
                
                return jsonify({
                    'success': True,
                    'query': query,
                    'results': results['results'],
                    'pagination': {
                        'limit': limit,
                        'offset': offset,
                        'total': results['total'],
                        'has_more': offset + limit < results['total']
                    }
                })
                
            except Exception as e:
                return jsonify({'error': f'Search failed: {str(e)}'}), 500
        
        @self.app.route('/api/analysis/<analysis_id>', methods=['GET'])
        def get_analysis_by_id(analysis_id):
            """Get specific analysis result by ID"""
            try:
                result = self.storage.get_analysis_by_id(analysis_id)
                
                if result:
                    return jsonify({
                        'success': True,
                        'result': result
                    })
                else:
                    return jsonify({'error': f'Analysis with ID {analysis_id} not found'}), 404
                
            except Exception as e:
                return jsonify({'error': f'Failed to get analysis: {str(e)}'}), 500

    def run_server(self, host='localhost', port=5003, debug=False):
        """Run the Flask server"""
        if self.app:
            print(f"🚀 Starting Analysis API server on http://{host}:{port}")
            self.app.run(host=host, port=port, debug=debug)
        else:
            print("❌ Flask not available - cannot start server")


def main():
    """Main function to start the Analysis API server"""
    
    print("🔍 AI Policy Analyzer - Analysis Results API (Quick Start Version)")
    print("=" * 60)
    
    # Initialize API
    api = AnalysisAPI()
    
    print("⚡ Quick start mode - skipping data processing")
    
    # Show statistics
    try:
        stats = api.storage.get_analysis_statistics()
        print(f"\n📈 Database Statistics:")
        print(f"   Total Analyses: {stats.get('total_analyses', 0)}")
    except Exception as e:
        print(f"\n⚠️ Unable to retrieve database statistics: {str(e)}")
    
    if FLASK_AVAILABLE:
        print(f"\n🌐 API Endpoints:")
        print(f"   Health: http://localhost:5003/api/historical/health")
        print(f"   Statistics: http://localhost:5003/api/analysis/statistics")
        print(f"   Results: http://localhost:5003/api/analysis/results")
        print(f"   Search: http://localhost:5003/api/analysis/search")
        print(f"   Analyze: POST http://localhost:5003/api/analysis/analyze")
        
        # Start server
        api.run_server(host='0.0.0.0', port=5003)
    else:
        print(f"\n⚠️ Flask not available - API running in mock mode")
        print(f"💾 Database available at: {api.storage.db_path}")


if __name__ == "__main__":
    main()
