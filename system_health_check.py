#!/usr/bin/env python3
"""
AI Policy Analyzer - System Health Check
系统健康检查和诊断工具

Author: <PERSON> Code
Date: 2025-08-10
"""

import os
import sys
import json
import sqlite3
import requests
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

class SystemHealthChecker:
    """系统健康检查器"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.dashboard_dir = self.base_dir / "dashboard"
        self.backend_dir = self.dashboard_dir / "backend"
        self.frontend_dir = self.dashboard_dir / "frontend"
        self.health_report = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'unknown',
            'checks': {}
        }
    
    def check_python_environment(self) -> Dict[str, Any]:
        """检查Python环境"""
        print("🐍 检查Python环境...")
        
        result = {
            'status': 'pass',
            'details': {},
            'issues': []
        }
        
        # Python版本
        python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
        result['details']['python_version'] = python_version
        
        if sys.version_info < (3, 8):
            result['status'] = 'fail'
            result['issues'].append(f"Python版本过低: {python_version} (需要 >= 3.8)")
        
        # 检查必要的包
        required_packages = {
            'flask': 'Web框架',
            'flask_cors': 'CORS支持',
            'pandas': '数据处理',
            'numpy': '数值计算',
            'sqlite3': '数据库',
            'requests': 'HTTP客户端'
        }
        
        installed_packages = {}
        missing_packages = []
        
        for package, description in required_packages.items():
            try:
                module = __import__(package)
                version = getattr(module, '__version__', 'unknown')
                installed_packages[package] = {
                    'version': version,
                    'description': description,
                    'status': 'installed'
                }
            except ImportError:
                missing_packages.append(package)
                installed_packages[package] = {
                    'version': None,
                    'description': description,
                    'status': 'missing'
                }
        
        result['details']['packages'] = installed_packages
        
        if missing_packages:
            result['status'] = 'fail'
            result['issues'].append(f"缺少必要包: {', '.join(missing_packages)}")
        
        print(f"   Python版本: {python_version}")
        print(f"   已安装包: {len(installed_packages) - len(missing_packages)}/{len(required_packages)}")
        if missing_packages:
            print(f"   ❌ 缺少包: {', '.join(missing_packages)}")
        else:
            print("   ✅ 所有必要包已安装")
        
        return result
    
    def check_file_structure(self) -> Dict[str, Any]:
        """检查文件结构"""
        print("📁 检查文件结构...")
        
        result = {
            'status': 'pass',
            'details': {},
            'issues': []
        }
        
        # 必要的文件和目录
        required_paths = {
            'directories': [
                self.dashboard_dir,
                self.backend_dir,
                self.frontend_dir,
                self.dashboard_dir / "data",
                self.dashboard_dir / "analysis_results"
            ],
            'files': [
                self.frontend_dir / "index.html",
                self.frontend_dir / "dashboard.js",
                self.frontend_dir / "server.py",
                self.backend_dir / "visualization_api.py",
                self.backend_dir / "search_endpoints.py",
                self.backend_dir / "historical_data_endpoints.py",
                self.backend_dir / "historical_data_integration.py",
                self.backend_dir / "real_analysis_engine.py"
            ]
        }
        
        file_status = {}
        missing_items = []
        
        # 检查目录
        for directory in required_paths['directories']:
            exists = directory.exists() and directory.is_dir()
            file_status[str(directory)] = {
                'type': 'directory',
                'exists': exists,
                'path': str(directory)
            }
            if not exists:
                missing_items.append(f"目录: {directory}")
        
        # 检查文件
        for file_path in required_paths['files']:
            exists = file_path.exists() and file_path.is_file()
            size = file_path.stat().st_size if exists else 0
            file_status[str(file_path)] = {
                'type': 'file',
                'exists': exists,
                'size': size,
                'path': str(file_path)
            }
            if not exists:
                missing_items.append(f"文件: {file_path}")
        
        result['details']['file_status'] = file_status
        
        if missing_items:
            result['status'] = 'fail'
            result['issues'] = missing_items
        
        total_items = len(required_paths['directories']) + len(required_paths['files'])
        existing_items = total_items - len(missing_items)
        
        print(f"   文件结构完整性: {existing_items}/{total_items}")
        if missing_items:
            print(f"   ❌ 缺少 {len(missing_items)} 个项目")
            for item in missing_items[:5]:  # 只显示前5个
                print(f"      - {item}")
            if len(missing_items) > 5:
                print(f"      ... 还有 {len(missing_items) - 5} 个项目")
        else:
            print("   ✅ 文件结构完整")
        
        return result
    
    def check_database_status(self) -> Dict[str, Any]:
        """检查数据库状态"""
        print("🗄️ 检查数据库状态...")
        
        result = {
            'status': 'pass',
            'details': {},
            'issues': []
        }
        
        # 检查主要数据库文件
        db_files = [
            self.backend_dir / "analysis_results.db",
            self.backend_dir / "search_index.db"
        ]
        
        db_status = {}
        
        for db_file in db_files:
            db_name = db_file.name
            if db_file.exists():
                try:
                    conn = sqlite3.connect(str(db_file))
                    cursor = conn.cursor()
                    
                    # 获取表信息
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                    tables = [row[0] for row in cursor.fetchall()]
                    
                    # 获取数据库大小
                    size = db_file.stat().st_size
                    
                    # 检查主要表的记录数
                    table_counts = {}
                    for table in tables:
                        try:
                            cursor.execute(f"SELECT COUNT(*) FROM {table}")
                            count = cursor.fetchone()[0]
                            table_counts[table] = count
                        except Exception:
                            table_counts[table] = 'error'
                    
                    db_status[db_name] = {
                        'exists': True,
                        'size': size,
                        'tables': tables,
                        'table_counts': table_counts,
                        'status': 'healthy'
                    }
                    
                    conn.close()
                    
                except Exception as e:
                    db_status[db_name] = {
                        'exists': True,
                        'status': 'error',
                        'error': str(e)
                    }
                    result['issues'].append(f"数据库 {db_name} 访问错误: {e}")
            else:
                db_status[db_name] = {
                    'exists': False,
                    'status': 'missing'
                }
                result['issues'].append(f"数据库文件不存在: {db_name}")
        
        result['details']['databases'] = db_status
        
        if result['issues']:
            result['status'] = 'warning' if any('error' in issue for issue in result['issues']) else 'fail'
        
        # 显示数据库状态
        for db_name, status in db_status.items():
            if status['exists']:
                if status['status'] == 'healthy':
                    total_records = sum(count for count in status['table_counts'].values() if isinstance(count, int))
                    print(f"   ✅ {db_name}: {len(status['tables'])} 表, {total_records} 记录")
                else:
                    print(f"   ❌ {db_name}: {status.get('error', '未知错误')}")
            else:
                print(f"   ⚠️ {db_name}: 文件不存在")
        
        return result
    
    def check_api_services(self) -> Dict[str, Any]:
        """检查API服务状态"""
        print("🌐 检查API服务状态...")
        
        result = {
            'status': 'pass',
            'details': {},
            'issues': []
        }
        
        # API服务列表
        api_services = [
            {'name': 'Frontend', 'url': 'http://localhost:8000', 'description': '前端服务'},
            {'name': 'Visualization API', 'url': 'http://localhost:5001/api/health', 'description': '可视化API'},
            {'name': 'Search API', 'url': 'http://localhost:5002/api/health', 'description': '搜索API'},
            {'name': 'Historical Data API', 'url': 'http://localhost:5003/api/health', 'description': '历史数据API'},
        ]
        
        service_status = {}
        healthy_services = 0
        
        for service in api_services:
            try:
                response = requests.get(service['url'], timeout=5)
                if response.status_code == 200:
                    service_status[service['name']] = {
                        'status': 'healthy',
                        'response_time': response.elapsed.total_seconds(),
                        'status_code': response.status_code
                    }
                    healthy_services += 1
                    print(f"   ✅ {service['name']}: 正常 ({response.elapsed.total_seconds():.2f}s)")
                else:
                    service_status[service['name']] = {
                        'status': 'error',
                        'status_code': response.status_code
                    }
                    result['issues'].append(f"{service['name']} 响应异常: HTTP {response.status_code}")
                    print(f"   ❌ {service['name']}: HTTP {response.status_code}")
                    
            except requests.exceptions.ConnectionError:
                service_status[service['name']] = {
                    'status': 'offline',
                    'error': 'Connection refused'
                }
                result['issues'].append(f"{service['name']} 无法连接")
                print(f"   🔴 {service['name']}: 离线")
                
            except Exception as e:
                service_status[service['name']] = {
                    'status': 'error',
                    'error': str(e)
                }
                result['issues'].append(f"{service['name']} 错误: {e}")
                print(f"   ❌ {service['name']}: {e}")
        
        result['details']['services'] = service_status
        result['details']['healthy_count'] = healthy_services
        result['details']['total_count'] = len(api_services)
        
        if healthy_services == 0:
            result['status'] = 'fail'
        elif healthy_services < len(api_services):
            result['status'] = 'warning'
        
        print(f"   服务状态: {healthy_services}/{len(api_services)} 正常")
        
        return result
    
    def generate_health_report(self) -> Dict[str, Any]:
        """生成健康报告"""
        print("\n🏥 生成系统健康报告...")
        
        # 执行所有检查
        checks = {
            'python_environment': self.check_python_environment(),
            'file_structure': self.check_file_structure(),
            'database_status': self.check_database_status(),
            'api_services': self.check_api_services()
        }
        
        self.health_report['checks'] = checks
        
        # 计算总体状态
        statuses = [check['status'] for check in checks.values()]
        if all(status == 'pass' for status in statuses):
            self.health_report['overall_status'] = 'healthy'
        elif any(status == 'fail' for status in statuses):
            self.health_report['overall_status'] = 'critical'
        else:
            self.health_report['overall_status'] = 'warning'
        
        # 收集所有问题
        all_issues = []
        for check in checks.values():
            all_issues.extend(check.get('issues', []))
        
        self.health_report['total_issues'] = len(all_issues)
        self.health_report['issues'] = all_issues
        
        return self.health_report
    
    def save_report(self, report: Dict[str, Any]):
        """保存健康报告"""
        report_file = self.base_dir / f"health_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            print(f"📄 健康报告已保存: {report_file}")
        except Exception as e:
            print(f"❌ 保存报告失败: {e}")
    
    def print_summary(self, report: Dict[str, Any]):
        """打印摘要"""
        print("\n" + "="*60)
        print("📊 系统健康检查摘要")
        print("="*60)
        
        status_emoji = {
            'healthy': '✅',
            'warning': '⚠️',
            'critical': '❌'
        }
        
        overall_status = report['overall_status']
        print(f"总体状态: {status_emoji.get(overall_status, '❓')} {overall_status.upper()}")
        print(f"检查时间: {report['timestamp']}")
        print(f"发现问题: {report['total_issues']} 个")
        
        print("\n📋 检查项目:")
        for check_name, check_result in report['checks'].items():
            status = check_result['status']
            emoji = {'pass': '✅', 'warning': '⚠️', 'fail': '❌'}.get(status, '❓')
            print(f"   {emoji} {check_name.replace('_', ' ').title()}: {status.upper()}")
        
        if report['issues']:
            print(f"\n⚠️ 需要注意的问题:")
            for i, issue in enumerate(report['issues'][:10], 1):
                print(f"   {i}. {issue}")
            if len(report['issues']) > 10:
                print(f"   ... 还有 {len(report['issues']) - 10} 个问题")
        
        print("="*60)

def main():
    """主函数"""
    print("🔍 AI Policy Analyzer - 系统健康检查")
    print("="*60)
    
    checker = SystemHealthChecker()
    
    try:
        # 生成健康报告
        report = checker.generate_health_report()
        
        # 打印摘要
        checker.print_summary(report)
        
        # 保存报告
        checker.save_report(report)
        
        # 根据状态返回适当的退出码
        exit_code = {
            'healthy': 0,
            'warning': 1,
            'critical': 2
        }.get(report['overall_status'], 3)
        
        sys.exit(exit_code)
        
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        sys.exit(3)

if __name__ == "__main__":
    main()
