/**
 * This script contains fixes for the "Invalid left-hand side in assignment" error
 * Include this file before dashboard_complete_final.js
 */

// Fix any potential issues with function prototypes
if (!window.fixApplied) {
    console.log('Applying dashboard fixes...');

    // Fix 1: Override any problematic prototype assignments
    // Make sure Object.defineProperty is used correctly
    const originalDefineProperty = Object.defineProperty;
    Object.defineProperty = function(obj, prop, desc) {
        try {
            return originalDefineProperty.call(this, obj, prop, desc);
        } catch (e) {
            console.warn('Error in Object.defineProperty:', e);
            return obj;
        }
    };

    // Fix 2: Protect against invalid assignments by wrapping problematic operations
    // Make certain that assignment expressions are valid
    const originalSetTimeout = window.setTimeout;
    window.setTimeout = function(callback, delay) {
        try {
            return originalSetTimeout.call(this, callback, delay);
        } catch (e) {
            console.warn('Error in setTimeout:', e);
            return 0;
        }
    };

    // Fix 3: Add defensive check for Array.prototype.sort to avoid issues
    const originalArraySort = Array.prototype.sort;
    Array.prototype.sort = function(compareFn) {
        try {
            return originalArraySort.call(this, compareFn);
        } catch (e) {
            console.warn('Error in Array.prototype.sort:', e);
            return this;
        }
    };
    
    // Fix 4: Prevent illegal assignments in Promise chains
    const originalThen = Promise.prototype.then;
    Promise.prototype.then = function(onFulfilled, onRejected) {
        try {
            return originalThen.call(this, onFulfilled, onRejected);
        } catch (e) {
            console.warn('Error in Promise.prototype.then:', e);
            return this;
        }
    };
    
    const originalCatch = Promise.prototype.catch;
    Promise.prototype.catch = function(onRejected) {
        try {
            return originalCatch.call(this, onRejected);
        } catch (e) {
            console.warn('Error in Promise.prototype.catch:', e);
            return this;
        }
    };
    
    const originalFinally = Promise.prototype.finally;
    Promise.prototype.finally = function(onFinally) {
        try {
            return originalFinally.call(this, onFinally);
        } catch (e) {
            console.warn('Error in Promise.prototype.finally:', e);
            return this;
        }
    };

    // Mark fixes as applied
    window.fixApplied = true;
    console.log('Dashboard fixes applied successfully');
}
