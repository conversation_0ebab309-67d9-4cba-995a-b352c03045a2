<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Error Fix Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Dashboard Error Fix Test</h1>
        
        <div class="alert alert-info">
            <h5>测试JavaScript修复</h5>
            <p>这个页面用于测试Dashboard JavaScript错误的修复情况。</p>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6>函数测试</h6>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt"></i> Test Refresh Dashboard
                        </button>
                        <button class="btn btn-secondary ms-2" onclick="testVariables()">
                            <i class="fas fa-vial"></i> Test Variables
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6>测试结果</h6>
                    </div>
                    <div class="card-body">
                        <div id="testResults">
                            <p class="text-muted">点击按钮开始测试...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="dashboard/frontend/dashboard.js"></script>
    <script>
        function testVariables() {
            const results = document.getElementById('testResults');
            let html = '<h6>变量测试结果:</h6><ul>';
            
            // 测试变量是否存在
            try {
                if (typeof documentAnalysisFiles !== 'undefined') {
                    html += '<li class="text-success">✅ documentAnalysisFiles 变量存在</li>';
                } else {
                    html += '<li class="text-warning">⚠️ documentAnalysisFiles 变量未定义</li>';
                }
                
                if (typeof uploadSelectedFiles !== 'undefined') {
                    html += '<li class="text-success">✅ uploadSelectedFiles 变量存在</li>';
                } else {
                    html += '<li class="text-warning">⚠️ uploadSelectedFiles 变量未定义</li>';
                }
                
                if (typeof refreshDashboard === 'function') {
                    html += '<li class="text-success">✅ refreshDashboard 函数存在</li>';
                } else {
                    html += '<li class="text-danger">❌ refreshDashboard 函数不存在</li>';
                }
                
            } catch (error) {
                html += `<li class="text-danger">❌ 测试出错: ${error.message}</li>`;
            }
            
            html += '</ul>';
            results.innerHTML = html;
        }
        
        // 页面加载时自动测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Dashboard Error Fix Test Page Loaded');
            setTimeout(testVariables, 1000);
        });
    </script>
</body>
</html>