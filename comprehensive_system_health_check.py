#!/usr/bin/env python3
"""
AI Policy Analyzer - 全面系统健康检查
检查所有API服务和功能模块的可用性
"""

import requests
import json
import time
import sys
import os
from datetime import datetime
from pathlib import Path

# 添加路径以便导入配置
sys.path.append('dashboard/backend')

try:
    from port_config import PORTS, SERVICES
    print("✅ 成功加载端口配置")
except ImportError:
    print("⚠️ 无法加载端口配置，使用默认值")
    PORTS = {
        'frontend': 8028,
        'visualization_api': 5001,
        'search_api': 5002,
        'historical_api': 5003,
        'analysis_api': 5004,
        'analytics_api': 5005,
        'batch_api': 5006,
        'enhanced_batch_api': 5007,
        'analysis_details_api': 5008
    }
    SERVICES = {
        'frontend': 'Frontend Server',
        'visualization_api': 'Visualization API',
        'search_api': 'Search API',
        'historical_api': 'Historical Data API',
        'analysis_api': 'Analysis Results API',
        'analytics_api': 'Advanced Analytics API',
        'batch_api': 'Batch Processing API',
        'enhanced_batch_api': 'Enhanced Batch Processor',
        'analysis_details_api': 'Analysis Details API'
    }

class SystemHealthChecker:
    """系统健康检查器"""
    
    def __init__(self):
        self.results = {}
        self.total_checks = 0
        self.passed_checks = 0
        self.failed_checks = 0
        
    def check_api_health(self, service_name, port, health_endpoint="/api/health"):
        """检查API服务健康状态"""
        try:
            url = f"http://localhost:{port}{health_endpoint}"
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                return {
                    'status': 'healthy',
                    'response_time': response.elapsed.total_seconds(),
                    'data': data
                }
            else:
                return {
                    'status': 'unhealthy',
                    'error': f"HTTP {response.status_code}",
                    'response_time': response.elapsed.total_seconds()
                }
                
        except requests.exceptions.ConnectionError:
            return {
                'status': 'offline',
                'error': 'Connection refused - service not running'
            }
        except requests.exceptions.Timeout:
            return {
                'status': 'timeout',
                'error': 'Request timeout'
            }
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def check_frontend_server(self):
        """检查前端服务器"""
        try:
            port = PORTS['frontend']
            url = f"http://localhost:{port}"
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                return {
                    'status': 'healthy',
                    'response_time': response.elapsed.total_seconds()
                }
            else:
                return {
                    'status': 'unhealthy',
                    'error': f"HTTP {response.status_code}"
                }
                
        except Exception as e:
            return {
                'status': 'offline',
                'error': str(e)
            }
    
    def check_database_files(self):
        """检查数据库文件"""
        db_files = [
            'dashboard/backend/search_index.db',
            'dashboard/backend/analysis_results.db',
            'simple_backend.db'
        ]
        
        db_status = {}
        for db_file in db_files:
            if os.path.exists(db_file):
                size = os.path.getsize(db_file)
                db_status[db_file] = {
                    'exists': True,
                    'size_bytes': size,
                    'size_mb': round(size / 1024 / 1024, 2)
                }
            else:
                db_status[db_file] = {
                    'exists': False,
                    'error': 'File not found'
                }
        
        return db_status
    
    def check_data_directories(self):
        """检查数据目录"""
        data_dirs = [
            'data/90_FR_9088_pdfs',
            'dashboard/data',
            'dashboard/analysis_results'
        ]
        
        dir_status = {}
        for data_dir in data_dirs:
            if os.path.exists(data_dir):
                files = list(Path(data_dir).glob('*'))
                dir_status[data_dir] = {
                    'exists': True,
                    'file_count': len(files),
                    'files': [f.name for f in files[:5]]  # 只显示前5个文件
                }
            else:
                dir_status[data_dir] = {
                    'exists': False,
                    'error': 'Directory not found'
                }
        
        return dir_status
    
    def test_api_endpoints(self):
        """测试关键API端点"""
        test_results = {}
        
        # 测试可视化API
        viz_port = PORTS['visualization_api']
        test_results['visualization_endpoints'] = {
            'health': self.check_api_health('visualization_api', viz_port, '/api/visualize/health'),
            'sentiment': self.check_api_health('visualization_api', viz_port, '/api/visualize/sentiment'),
        }
        
        # 测试搜索API
        search_port = PORTS['search_api']
        test_results['search_endpoints'] = {
            'health': self.check_api_health('search_api', search_port, '/api/search/health'),
            'search': self.check_api_health('search_api', search_port, '/api/search?query=test'),
        }
        
        # 测试历史数据API
        hist_port = PORTS['historical_api']
        test_results['historical_endpoints'] = {
            'health': self.check_api_health('historical_api', hist_port, '/api/historical/health'),
            'statistics': self.check_api_health('historical_api', hist_port, '/api/historical/statistics'),
        }
        
        # 测试高级分析API
        analytics_port = PORTS['analytics_api']
        test_results['analytics_endpoints'] = {
            'health': self.check_api_health('analytics_api', analytics_port, '/api/analytics/health'),
            'ml_insights': self.check_api_health('analytics_api', analytics_port, '/api/analytics/ml-insights'),
        }
        
        return test_results
    
    def run_comprehensive_check(self):
        """运行全面检查"""
        print("🔍 开始全面系统健康检查...")
        print("=" * 60)
        
        # 1. 检查端口配置
        print("\n📋 1. 端口配置检查")
        print(f"   前端端口: {PORTS['frontend']}")
        for service, port in PORTS.items():
            if service != 'frontend':
                print(f"   {SERVICES.get(service, service)}: {port}")
        
        # 2. 检查前端服务器
        print("\n🌐 2. 前端服务器检查")
        frontend_result = self.check_frontend_server()
        self._print_result("Frontend Server", frontend_result)
        
        # 3. 检查API服务
        print("\n🔧 3. API服务检查")
        api_services = ['visualization_api', 'search_api', 'historical_api', 'analytics_api']
        
        for service in api_services:
            port = PORTS[service]
            health_endpoint = self._get_health_endpoint(service)
            result = self.check_api_health(service, port, health_endpoint)
            self._print_result(SERVICES[service], result)
        
        # 4. 检查数据库文件
        print("\n💾 4. 数据库文件检查")
        db_status = self.check_database_files()
        for db_file, status in db_status.items():
            if status['exists']:
                print(f"   ✅ {db_file} ({status['size_mb']} MB)")
            else:
                print(f"   ❌ {db_file} - {status['error']}")
        
        # 5. 检查数据目录
        print("\n📁 5. 数据目录检查")
        dir_status = self.check_data_directories()
        for data_dir, status in dir_status.items():
            if status['exists']:
                print(f"   ✅ {data_dir} ({status['file_count']} files)")
            else:
                print(f"   ❌ {data_dir} - {status['error']}")
        
        # 6. 测试API端点
        print("\n🧪 6. API端点功能测试")
        endpoint_results = self.test_api_endpoints()
        
        for api_group, endpoints in endpoint_results.items():
            print(f"\n   {api_group.replace('_', ' ').title()}:")
            for endpoint, result in endpoints.items():
                self._print_result(f"     {endpoint}", result, indent="     ")
        
        # 7. 生成总结报告
        self._generate_summary_report()
    
    def _get_health_endpoint(self, service):
        """获取服务的健康检查端点"""
        health_endpoints = {
            'visualization_api': '/api/visualize/health',
            'search_api': '/api/search/health',
            'historical_api': '/api/historical/health',
            'analytics_api': '/api/analytics/health',
            'batch_api': '/api/batch/health'
        }
        return health_endpoints.get(service, '/api/health')
    
    def _print_result(self, name, result, indent="   "):
        """打印检查结果"""
        self.total_checks += 1
        
        if result['status'] == 'healthy':
            self.passed_checks += 1
            response_time = result.get('response_time', 0)
            print(f"{indent}✅ {name} - 健康 ({response_time:.3f}s)")
        elif result['status'] == 'offline':
            self.failed_checks += 1
            print(f"{indent}🔴 {name} - 离线 ({result['error']})")
        elif result['status'] == 'unhealthy':
            self.failed_checks += 1
            print(f"{indent}⚠️ {name} - 不健康 ({result['error']})")
        else:
            self.failed_checks += 1
            print(f"{indent}❌ {name} - 错误 ({result['error']})")
    
    def _generate_summary_report(self):
        """生成总结报告"""
        print("\n" + "=" * 60)
        print("📊 系统健康检查总结")
        print("=" * 60)
        
        success_rate = (self.passed_checks / self.total_checks * 100) if self.total_checks > 0 else 0
        
        print(f"总检查项目: {self.total_checks}")
        print(f"通过检查: {self.passed_checks}")
        print(f"失败检查: {self.failed_checks}")
        print(f"成功率: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("\n🎉 系统整体状态良好！")
        elif success_rate >= 60:
            print("\n⚠️ 系统部分功能存在问题，需要修复")
        else:
            print("\n🚨 系统存在严重问题，需要立即修复")
        
        # 保存报告到文件
        report_file = f"system_health_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'total_checks': self.total_checks,
            'passed_checks': self.passed_checks,
            'failed_checks': self.failed_checks,
            'success_rate': success_rate,
            'port_configuration': PORTS
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细报告已保存到: {report_file}")

if __name__ == '__main__':
    checker = SystemHealthChecker()
    checker.run_comprehensive_check()
