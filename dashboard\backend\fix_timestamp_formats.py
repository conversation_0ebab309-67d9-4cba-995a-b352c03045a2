#!/usr/bin/env python3
"""
修复Analytics API时间戳解析格式问题
此脚本修复高级分析引擎中的时间戳格式错误

Author: Claude Code
Date: August 13, 2025
"""

import os
import sys
import re

def fix_timestamp_formats(file_path):
    """
    修复文件中的时间戳格式解析问题
    将所有pd.to_datetime调用更新为使用更灵活的格式选项
    """
    print(f"正在修复时间戳格式问题: {file_path}")
    
    # 读取原文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复时间戳格式解析
    fixed_content = re.sub(
        r"df\['timestamp'\]\s*=\s*pd\.to_datetime\(df\['timestamp'\]\)",
        r"df['timestamp'] = pd.to_datetime(df['timestamp'], format='mixed')",
        content
    )
    
    # 备份原文件
    backup_path = file_path + '.bak'
    if not os.path.exists(backup_path):
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"创建备份: {backup_path}")
    
    # 写入修复后的内容
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    # 检查是否进行了更改
    changes = sum(1 for a, b in zip(content.splitlines(), fixed_content.splitlines()) if a != b)
    print(f"完成: 修复了 {changes} 处时间戳格式问题")
    return changes

def main():
    """主函数"""
    print("时间戳格式修复工具")
    print("="*40)
    
    # 获取脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 需要修复的文件列表
    files_to_fix = [
        os.path.join(current_dir, 'advanced_analytics_engine.py')
    ]
    
    total_fixes = 0
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            total_fixes += fix_timestamp_formats(file_path)
        else:
            print(f"错误: 找不到文件 {file_path}")
    
    print(f"总计修复了 {total_fixes} 处时间戳格式问题")
    
    print("\n下一步:")
    print("1. 请重启Analytics API服务")
    print("2. 验证仪表板连接状态")
    print("="*40)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
