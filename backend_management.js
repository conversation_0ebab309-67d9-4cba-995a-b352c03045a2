// ============================================================================
// BACKEND MANAGEMENT DASHBOARD JAVASCRIPT
// ============================================================================

let managementDashboard = {
    backend: null,
    updateInterval: null,
    isAutoRefresh: true,
    refreshRate: 10000, // 10 seconds
    logs: []
};

/**
 * Initialize management dashboard
 */
async function initializeManagementDashboard() {
    console.log('🖥️ Initializing Backend Management Dashboard...');
    
    try {
        // Initialize backend integration
        managementDashboard.backend = getBackendIntegration();
        
        // Check backend connection
        await checkBackendConnection();
        
        // Load initial data
        await loadInitialData();
        
        // Start auto-refresh
        startAutoRefresh();
        
        addLog('success', 'Management dashboard initialized successfully');
        
    } catch (error) {
        console.error('❌ Failed to initialize management dashboard:', error);
        addLog('error', `Initialization failed: ${error.message}`);
    }
}

/**
 * Check backend connection
 */
async function checkBackendConnection() {
    try {
        const status = await managementDashboard.backend.getSystemStatus();
        updateBackendStatus(true);
        addLog('success', 'Backend connection established');
        return status;
    } catch (error) {
        updateBackendStatus(false);
        addLog('error', `Backend connection failed: ${error.message}`);
        throw error;
    }
}

/**
 * Update backend status indicator
 */
function updateBackendStatus(isConnected) {
    const statusElement = document.getElementById('backend-status');
    if (statusElement) {
        statusElement.className = isConnected ? 'badge bg-success' : 'badge bg-danger';
        statusElement.textContent = isConnected ? 'Connected' : 'Disconnected';
    }
}

/**
 * Load initial data
 */
async function loadInitialData() {
    try {
        // Load system status
        await refreshSystemStatus();
        
        // Load processing queue
        await refreshProcessingQueue();
        
        // Load analytics summary
        await loadAnalyticsSummary();
        
        addLog('info', 'Initial data loaded successfully');
        
    } catch (error) {
        addLog('error', `Failed to load initial data: ${error.message}`);
    }
}

/**
 * Refresh system status
 */
async function refreshSystemStatus() {
    try {
        const status = await managementDashboard.backend.getSystemStatus();
        
        // Update document counts
        document.getElementById('total-documents').textContent = status.total_documents || 0;
        document.getElementById('processed-documents').textContent = status.processed_documents || 0;
        document.getElementById('processing-documents').textContent = status.processing_documents || 0;
        document.getElementById('queued-documents').textContent = status.queued_documents || 0;
        
        // Update progress
        const processingRate = status.processing_rate || 0;
        document.getElementById('progress-percentage').textContent = processingRate + '%';
        document.getElementById('processing-rate').textContent = processingRate + '%';
        
        const progressBar = document.getElementById('processing-progress');
        if (progressBar) {
            progressBar.style.width = processingRate + '%';
            progressBar.querySelector('#progress-text').textContent = processingRate + '%';
        }
        
        // Update performance ring
        updatePerformanceRing(processingRate);
        
        // Calculate ETA
        updateProcessingETA(status);
        
        addLog('info', 'System status refreshed');
        
    } catch (error) {
        addLog('error', `Failed to refresh system status: ${error.message}`);
    }
}

/**
 * Update performance ring
 */
function updatePerformanceRing(percentage) {
    const ring = document.getElementById('performance-ring');
    const performanceText = document.getElementById('system-performance');
    
    if (ring && performanceText) {
        const circumference = 2 * Math.PI * 50; // radius = 50
        const offset = circumference - (percentage / 100) * circumference;
        
        ring.style.strokeDasharray = `${circumference} ${circumference}`;
        ring.style.strokeDashoffset = offset;
        
        performanceText.textContent = Math.round(percentage) + '%';
    }
}

/**
 * Update processing ETA
 */
function updateProcessingETA(status) {
    const etaElement = document.getElementById('eta-completion');
    const avgTimeElement = document.getElementById('avg-processing-time');
    
    if (status.processing_documents > 0 || status.queued_documents > 0) {
        // Estimate based on current processing rate
        const remainingDocs = (status.processing_documents || 0) + (status.queued_documents || 0);
        const avgProcessingTime = 30; // seconds per document (estimate)
        const etaSeconds = remainingDocs * avgProcessingTime;
        
        const hours = Math.floor(etaSeconds / 3600);
        const minutes = Math.floor((etaSeconds % 3600) / 60);
        
        if (hours > 0) {
            etaElement.textContent = `${hours}h ${minutes}m`;
        } else {
            etaElement.textContent = `${minutes}m`;
        }
        
        avgTimeElement.textContent = `${avgProcessingTime}s`;
    } else {
        etaElement.textContent = 'Complete';
        avgTimeElement.textContent = '--';
    }
}

/**
 * Refresh processing queue
 */
async function refreshProcessingQueue() {
    try {
        const queueData = await managementDashboard.backend.getProcessingQueue();
        const queueContainer = document.getElementById('processing-queue');
        
        if (queueContainer && queueData.queue_items) {
            const queueHTML = queueData.queue_items.map(item => `
                <div class="queue-item ${item.status}" data-queue-id="${item.queue_id}">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>${item.filename}</strong>
                            <br>
                            <small class="text-muted">${item.organization}</small>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-${getStatusColor(item.status)}">${item.status}</span>
                            <br>
                            <small class="text-muted">Priority: ${item.priority}</small>
                        </div>
                    </div>
                    ${item.error_message ? `<div class="text-danger mt-1"><small>${item.error_message}</small></div>` : ''}
                </div>
            `).join('');
            
            queueContainer.innerHTML = queueHTML || '<div class="text-center text-muted p-3">No items in queue</div>';
        }
        
        addLog('info', `Processing queue refreshed: ${queueData.total_items} items`);
        
    } catch (error) {
        addLog('error', `Failed to refresh processing queue: ${error.message}`);
    }
}

/**
 * Get status color for badges
 */
function getStatusColor(status) {
    const colorMap = {
        'queued': 'secondary',
        'processing': 'warning',
        'completed': 'success',
        'error': 'danger'
    };
    return colorMap[status] || 'secondary';
}

/**
 * Load analytics summary
 */
async function loadAnalyticsSummary() {
    try {
        const analytics = await managementDashboard.backend.getAnalyticsSummary();
        const summaryContainer = document.getElementById('analytics-summary');
        
        if (summaryContainer && analytics) {
            let summaryHTML = '<div class="row">';
            
            // Top organizations
            if (analytics.top_organizations && analytics.top_organizations.length > 0) {
                summaryHTML += `
                    <div class="col-md-6">
                        <h6>Top Organizations</h6>
                        <div class="list-group list-group-flush">
                            ${analytics.top_organizations.slice(0, 5).map(org => `
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <span>${org.organization}</span>
                                    <span class="badge bg-primary rounded-pill">${org.document_count}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            }
            
            // Status statistics
            if (analytics.status_statistics) {
                summaryHTML += `
                    <div class="col-md-6">
                        <h6>Processing Status</h6>
                        <div class="row">
                            ${Object.entries(analytics.status_statistics).map(([status, count]) => `
                                <div class="col-6 mb-2">
                                    <div class="text-center">
                                        <div class="h4 mb-0">${count}</div>
                                        <small class="text-muted">${status}</small>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            }
            
            summaryHTML += '</div>';
            summaryContainer.innerHTML = summaryHTML;
        }
        
        addLog('info', 'Analytics summary loaded');
        
    } catch (error) {
        addLog('error', `Failed to load analytics summary: ${error.message}`);
    }
}

/**
 * Start auto-refresh
 */
function startAutoRefresh() {
    if (managementDashboard.updateInterval) {
        clearInterval(managementDashboard.updateInterval);
    }
    
    managementDashboard.updateInterval = setInterval(async () => {
        if (managementDashboard.isAutoRefresh) {
            try {
                await refreshSystemStatus();
                await refreshProcessingQueue();
            } catch (error) {
                console.warn('Auto-refresh failed:', error);
            }
        }
    }, managementDashboard.refreshRate);
    
    addLog('info', `Auto-refresh started (${managementDashboard.refreshRate / 1000}s interval)`);
}

/**
 * Stop auto-refresh
 */
function stopAutoRefresh() {
    if (managementDashboard.updateInterval) {
        clearInterval(managementDashboard.updateInterval);
        managementDashboard.updateInterval = null;
    }
    managementDashboard.isAutoRefresh = false;
    addLog('info', 'Auto-refresh stopped');
}

/**
 * Add log entry
 */
function addLog(type, message) {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = {
        timestamp,
        type,
        message
    };
    
    managementDashboard.logs.push(logEntry);
    
    const logsContainer = document.getElementById('system-logs');
    if (logsContainer) {
        const logElement = document.createElement('div');
        logElement.className = `log-entry log-${type}`;
        logElement.textContent = `[${type.toUpperCase()}] ${timestamp} - ${message}`;
        
        logsContainer.insertBefore(logElement, logsContainer.firstChild);
        
        // Keep only last 50 entries
        while (logsContainer.children.length > 50) {
            logsContainer.removeChild(logsContainer.lastChild);
        }
    }
}

/**
 * Clear logs
 */
function clearLogs() {
    const logsContainer = document.getElementById('system-logs');
    if (logsContainer) {
        logsContainer.innerHTML = '<div class="log-entry log-info">[INFO] Logs cleared</div>';
    }
    managementDashboard.logs = [];
}

/**
 * Show status message
 */
function showStatusMessage(message, type = 'info') {
    const messagesContainer = document.getElementById('status-messages');
    if (messagesContainer) {
        const alertClass = type === 'error' ? 'alert-danger' : 
                          type === 'success' ? 'alert-success' : 
                          type === 'warning' ? 'alert-warning' : 'alert-info';
        
        const messageHTML = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        messagesContainer.insertAdjacentHTML('afterbegin', messageHTML);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            const alert = messagesContainer.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }
}

// ============================================================================
// MANAGEMENT ACTION FUNCTIONS
// ============================================================================

/**
 * Scan documents
 */
async function scanDocuments() {
    try {
        addLog('info', 'Starting document scan...');
        showStatusMessage('Scanning documents...', 'info');
        
        const result = await managementDashboard.backend.scanDocuments();
        
        addLog('success', `Document scan completed: ${result.new_files} new files found`);
        showStatusMessage(`Scan completed: ${result.new_files} new files, ${result.existing_files} existing files`, 'success');
        
        // Refresh status after scan
        await refreshSystemStatus();
        
    } catch (error) {
        addLog('error', `Document scan failed: ${error.message}`);
        showStatusMessage(`Scan failed: ${error.message}`, 'error');
    }
}

/**
 * Start batch processing
 */
async function startBatchProcessing() {
    try {
        const batchSize = parseInt(document.getElementById('batchSize').value) || 100;
        const priority = parseInt(document.getElementById('processingPriority').value) || 2;
        
        addLog('info', `Starting batch processing: ${batchSize} documents, priority ${priority}`);
        showStatusMessage(`Starting batch processing of ${batchSize} documents...`, 'info');
        
        const result = await managementDashboard.backend.startBatchProcessing(priority, batchSize);
        
        addLog('success', `Batch processing started: ${result.documents_added} documents added to queue`);
        showStatusMessage(`Batch processing started: ${result.documents_added} documents queued`, 'success');
        
        // Refresh status and queue
        await refreshSystemStatus();
        await refreshProcessingQueue();
        
    } catch (error) {
        addLog('error', `Batch processing failed: ${error.message}`);
        showStatusMessage(`Batch processing failed: ${error.message}`, 'error');
    }
}

/**
 * Refresh status
 */
async function refreshStatus() {
    try {
        addLog('info', 'Refreshing all data...');
        
        await checkBackendConnection();
        await refreshSystemStatus();
        await refreshProcessingQueue();
        await loadAnalyticsSummary();
        
        showStatusMessage('All data refreshed successfully', 'success');
        
    } catch (error) {
        addLog('error', `Refresh failed: ${error.message}`);
        showStatusMessage(`Refresh failed: ${error.message}`, 'error');
    }
}

/**
 * Export results
 */
async function exportResults() {
    try {
        addLog('info', 'Exporting results...');
        showStatusMessage('Preparing export...', 'info');
        
        // Get analytics data
        const analytics = await managementDashboard.backend.getAnalyticsSummary();
        const sentiment = await managementDashboard.backend.getSentimentAnalytics();
        const status = await managementDashboard.backend.getSystemStatus();
        
        const exportData = {
            export_timestamp: new Date().toISOString(),
            system_status: status,
            analytics_summary: analytics,
            sentiment_analytics: sentiment,
            logs: managementDashboard.logs
        };
        
        // Create and download file
        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `backend_export_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        addLog('success', 'Results exported successfully');
        showStatusMessage('Results exported successfully', 'success');
        
    } catch (error) {
        addLog('error', `Export failed: ${error.message}`);
        showStatusMessage(`Export failed: ${error.message}`, 'error');
    }
}

/**
 * Set batch size
 */
function setBatchSize() {
    const batchSize = document.getElementById('batchSize').value;
    addLog('info', `Batch size set to: ${batchSize}`);
    showStatusMessage(`Batch size set to ${batchSize}`, 'info');
}

/**
 * Set priority
 */
function setPriority() {
    const priority = document.getElementById('processingPriority').value;
    const priorityText = ['', 'Low', 'Normal', 'High'][priority] || 'Unknown';
    addLog('info', `Processing priority set to: ${priorityText}`);
    showStatusMessage(`Processing priority set to ${priorityText}`, 'info');
}

/**
 * Refresh queue
 */
async function refreshQueue() {
    try {
        await refreshProcessingQueue();
        showStatusMessage('Processing queue refreshed', 'success');
    } catch (error) {
        showStatusMessage(`Failed to refresh queue: ${error.message}`, 'error');
    }
}
