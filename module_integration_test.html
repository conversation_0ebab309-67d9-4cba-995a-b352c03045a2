<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Module Integration Test - AI Policy Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .integration-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 0;
            margin-bottom: 30px;
        }
        
        .module-flow {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        
        .module-box {
            background: white;
            border: 2px solid #dee2e6;
            border-radius: 12px;
            padding: 15px;
            margin: 10px;
            text-align: center;
            min-width: 150px;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .module-box.active {
            border-color: #007bff;
            background: #e3f2fd;
            transform: scale(1.05);
        }
        
        .module-box.completed {
            border-color: #28a745;
            background: #d4edda;
        }
        
        .module-box.error {
            border-color: #dc3545;
            background: #f8d7da;
        }
        
        .flow-arrow {
            font-size: 1.5em;
            color: #6c757d;
            margin: 0 10px;
        }
        
        .flow-arrow.active {
            color: #007bff;
            animation: pulse 1s infinite;
        }
        
        .test-scenario {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .scenario-step {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }
        
        .scenario-step.active {
            border-color: #007bff;
            background: #e3f2fd;
        }
        
        .scenario-step.completed {
            border-color: #28a745;
            background: #d4edda;
        }
        
        .step-number {
            background: #007bff;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
        }
        
        .step-number.completed {
            background: #28a745;
        }
        
        .integration-results {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
        }
        
        .result-metric {
            text-align: center;
            padding: 15px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            margin: 10px;
            backdrop-filter: blur(10px);
        }
        
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .slide-in {
            animation: slideIn 0.5s ease-out;
        }
    </style>
</head>
<body>
    <!-- Integration Header -->
    <div class="integration-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-network-wired me-3"></i>Module Integration Test</h1>
                    <p class="lead mb-0">Testing cross-module functionality and data flow</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light btn-lg" onclick="startIntegrationTest()">
                        <i class="fas fa-play"></i> Start Integration Test
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Module Flow Visualization -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-sitemap"></i> Module Integration Flow</h5>
            </div>
            <div class="card-body">
                <div class="module-flow" id="moduleFlow">
                    <div class="module-box" id="module-document">
                        <i class="fas fa-file-alt fa-2x mb-2"></i>
                        <div><strong>Document Analysis</strong></div>
                        <small>File Processing</small>
                    </div>
                    <div class="flow-arrow" id="arrow-1">→</div>
                    <div class="module-box" id="module-sentiment">
                        <i class="fas fa-heart fa-2x mb-2"></i>
                        <div><strong>Sentiment Lab</strong></div>
                        <small>Emotion Analysis</small>
                    </div>
                    <div class="flow-arrow" id="arrow-2">→</div>
                    <div class="module-box" id="module-predictive">
                        <i class="fas fa-chart-line fa-2x mb-2"></i>
                        <div><strong>Predictive Analytics</strong></div>
                        <small>Trend Forecasting</small>
                    </div>
                    <div class="flow-arrow" id="arrow-3">→</div>
                    <div class="module-box" id="module-network">
                        <i class="fas fa-project-diagram fa-2x mb-2"></i>
                        <div><strong>Network Analysis</strong></div>
                        <small>Relationship Mapping</small>
                    </div>
                </div>
                <div class="module-flow">
                    <div class="module-box" id="module-comparative">
                        <i class="fas fa-balance-scale fa-2x mb-2"></i>
                        <div><strong>Comparative Analysis</strong></div>
                        <small>Benchmarking</small>
                    </div>
                    <div class="flow-arrow" id="arrow-4">←</div>
                    <div class="module-box" id="module-policy">
                        <i class="fas fa-cogs fa-2x mb-2"></i>
                        <div><strong>Policy Simulator</strong></div>
                        <small>Impact Modeling</small>
                    </div>
                    <div class="flow-arrow" id="arrow-5">←</div>
                    <div class="module-box" id="module-realtime">
                        <i class="fas fa-satellite-dish fa-2x mb-2"></i>
                        <div><strong>Realtime Monitor</strong></div>
                        <small>Live Tracking</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Scenarios -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-list-ol"></i> Test Scenario 1: Document-to-Insight Pipeline</h6>
                    </div>
                    <div class="card-body">
                        <div class="test-scenario">
                            <h6>End-to-End Document Processing</h6>
                            <div id="scenario1Steps">
                                <div class="scenario-step" id="step1-1">
                                    <div class="step-number">1</div>
                                    <div>
                                        <strong>Upload Document</strong><br>
                                        <small>Test file upload and validation</small>
                                    </div>
                                </div>
                                <div class="scenario-step" id="step1-2">
                                    <div class="step-number">2</div>
                                    <div>
                                        <strong>Extract Content</strong><br>
                                        <small>Parse and extract text content</small>
                                    </div>
                                </div>
                                <div class="scenario-step" id="step1-3">
                                    <div class="step-number">3</div>
                                    <div>
                                        <strong>Analyze Sentiment</strong><br>
                                        <small>Process emotional content</small>
                                    </div>
                                </div>
                                <div class="scenario-step" id="step1-4">
                                    <div class="step-number">4</div>
                                    <div>
                                        <strong>Generate Predictions</strong><br>
                                        <small>Create trend forecasts</small>
                                    </div>
                                </div>
                            </div>
                            <button class="btn btn-primary mt-3" onclick="runScenario1()">
                                <i class="fas fa-play"></i> Run Scenario 1
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-list-ol"></i> Test Scenario 2: Policy Impact Analysis</h6>
                    </div>
                    <div class="card-body">
                        <div class="test-scenario">
                            <h6>Policy Simulation and Monitoring</h6>
                            <div id="scenario2Steps">
                                <div class="scenario-step" id="step2-1">
                                    <div class="step-number">1</div>
                                    <div>
                                        <strong>Configure Policy</strong><br>
                                        <small>Set policy parameters</small>
                                    </div>
                                </div>
                                <div class="scenario-step" id="step2-2">
                                    <div class="step-number">2</div>
                                    <div>
                                        <strong>Run Simulation</strong><br>
                                        <small>Execute policy modeling</small>
                                    </div>
                                </div>
                                <div class="scenario-step" id="step2-3">
                                    <div class="step-number">3</div>
                                    <div>
                                        <strong>Compare Results</strong><br>
                                        <small>Benchmark against alternatives</small>
                                    </div>
                                </div>
                                <div class="scenario-step" id="step2-4">
                                    <div class="step-number">4</div>
                                    <div>
                                        <strong>Monitor Impact</strong><br>
                                        <small>Real-time tracking</small>
                                    </div>
                                </div>
                            </div>
                            <button class="btn btn-primary mt-3" onclick="runScenario2()">
                                <i class="fas fa-play"></i> Run Scenario 2
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Integration Results -->
        <div class="integration-results" id="integrationResults" style="display: none;">
            <h5><i class="fas fa-chart-bar me-2"></i>Integration Test Results</h5>
            <div class="row">
                <div class="col-md-3">
                    <div class="result-metric">
                        <div class="metric-value" id="dataFlowSuccess">0%</div>
                        <div>Data Flow Success</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="result-metric">
                        <div class="metric-value" id="moduleCompatibility">0%</div>
                        <div>Module Compatibility</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="result-metric">
                        <div class="metric-value" id="performanceScore">0ms</div>
                        <div>Avg Response Time</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="result-metric">
                        <div class="metric-value" id="integrationScore">0%</div>
                        <div>Overall Score</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Log -->
        <div class="card mt-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0"><i class="fas fa-terminal"></i> Integration Test Log</h6>
                <button class="btn btn-outline-secondary btn-sm" onclick="clearTestLog()">
                    <i class="fas fa-trash"></i> Clear Log
                </button>
            </div>
            <div class="card-body">
                <div id="testLog" style="height: 300px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 6px; font-family: monospace;">
                    <div class="text-muted">[INFO] Integration test environment initialized</div>
                    <div class="text-muted">[INFO] Ready to start module integration testing</div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-bolt"></i> Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <button class="btn btn-outline-primary w-100 mb-2" onclick="testDataFlow()">
                            <i class="fas fa-exchange-alt"></i> Test Data Flow
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-outline-success w-100 mb-2" onclick="testModuleAPIs()">
                            <i class="fas fa-plug"></i> Test Module APIs
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-outline-info w-100 mb-2" onclick="testPerformance()">
                            <i class="fas fa-tachometer-alt"></i> Test Performance
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-outline-warning w-100 mb-2" onclick="generateReport()">
                            <i class="fas fa-file-alt"></i> Generate Report
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="dashboard/frontend/dashboard.js"></script>
    <script src="module_integration_test.js"></script>
    <script>
        // Initialize integration test when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔗 Module Integration Test Page Loaded');
            initializeIntegrationTest();
        });
    </script>
</body>
</html>
