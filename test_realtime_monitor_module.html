<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Realtime Monitor Module Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .metric-card {
            transition: all 0.3s ease;
            border-left: 4px solid #007bff;
        }
        
        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .metric-card.secondary {
            border-left-color: #6c757d;
        }
        
        .metric-card.accent {
            border-left-color: #17a2b8;
        }
        
        .metric-card.danger {
            border-left-color: #dc3545;
        }
        
        .activity-item {
            padding: 8px;
            border-radius: 6px;
            transition: all 0.3s ease;
        }
        
        .activity-item:hover {
            background-color: #f8f9fa;
        }
        
        .anomaly-item {
            padding: 12px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px;
            margin-bottom: 12px;
        }
        
        .metric-trend {
            margin-top: 8px;
        }
        
        @keyframes fadeInSlide {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .monitoring-active {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-satellite-dish me-2"></i>
            Realtime Monitor Module Test
        </h1>
        
        <!-- Monitor Controls -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cogs"></i> Monitor Configuration</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label for="monitorType" class="form-label">Monitor Type</label>
                        <select class="form-select" id="monitorType">
                            <option value="all">All Activities</option>
                            <option value="documents">Document Changes</option>
                            <option value="sentiment">Sentiment Shifts</option>
                            <option value="policy">Policy Updates</option>
                            <option value="anomalies">Anomaly Detection</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="alertThreshold" class="form-label">Alert Threshold</label>
                        <select class="form-select" id="alertThreshold">
                            <option value="low">Low Sensitivity</option>
                            <option value="medium" selected>Medium Sensitivity</option>
                            <option value="high">High Sensitivity</option>
                            <option value="critical">Critical Only</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="refreshRate" class="form-label">Refresh Rate</label>
                        <select class="form-select" id="refreshRate">
                            <option value="1">1 second</option>
                            <option value="5" selected>5 seconds</option>
                            <option value="10">10 seconds</option>
                            <option value="30">30 seconds</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <div class="mt-4">
                            <button class="btn btn-success me-2" onclick="startRealTimeMonitoring()" id="startMonitorBtn">
                                <i class="fas fa-play"></i> Start
                            </button>
                            <button class="btn btn-danger" onclick="stopRealTimeMonitoring()" id="stopMonitorBtn" disabled>
                                <i class="fas fa-stop"></i> Stop
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Real-time Metrics Dashboard -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-eye fa-2x mb-2 text-primary"></i>
                        <h3 id="activeMonitors">12</h3>
                        <p class="mb-0">Active Monitors</p>
                        <div class="metric-trend">
                            <small class="text-success"><i class="fas fa-arrow-up"></i> +2 today</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card secondary">
                    <div class="card-body text-center">
                        <i class="fas fa-bell fa-2x mb-2 text-warning"></i>
                        <h3 id="alertsToday">3</h3>
                        <p class="mb-0">Alerts Today</p>
                        <div class="metric-trend">
                            <small class="text-danger"><i class="fas fa-arrow-up"></i> +1 last hour</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card accent">
                    <div class="card-body text-center">
                        <i class="fas fa-stream fa-2x mb-2 text-info"></i>
                        <h3 id="dataStreams">8</h3>
                        <p class="mb-0">Data Streams</p>
                        <div class="metric-trend">
                            <small class="text-success"><i class="fas fa-circle"></i> All active</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card danger">
                    <div class="card-body text-center">
                        <i class="fas fa-heartbeat fa-2x mb-2 text-success"></i>
                        <h3 id="systemHealth">98%</h3>
                        <p class="mb-0">System Health</p>
                        <div class="metric-trend">
                            <small class="text-success"><i class="fas fa-check"></i> Optimal</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Real-time Charts -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0"><i class="fas fa-chart-line"></i> Real-time Activity Trends</h6>
                        <div>
                            <span class="badge bg-success" id="monitoringStatus">Monitoring Active</span>
                            <button class="btn btn-outline-secondary btn-sm ms-2" onclick="exportRealtimeData()">
                                <i class="fas fa-download"></i> Export
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="realtimeActivityChart" style="height: 300px;"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-tachometer-alt"></i> Performance Metrics</h6>
                    </div>
                    <div class="card-body">
                        <div id="performanceMetrics">
                            <div class="metric-item mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span>Processing Speed</span>
                                    <span class="badge bg-success" id="processingSpeed">1.2ms</span>
                                </div>
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar bg-success" id="processingSpeedBar" style="width: 85%"></div>
                                </div>
                            </div>
                            
                            <div class="metric-item mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span>Memory Usage</span>
                                    <span class="badge bg-info" id="memoryUsage">67%</span>
                                </div>
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar bg-info" id="memoryUsageBar" style="width: 67%"></div>
                                </div>
                            </div>
                            
                            <div class="metric-item mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span>Network Latency</span>
                                    <span class="badge bg-warning" id="networkLatency">45ms</span>
                                </div>
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar bg-warning" id="networkLatencyBar" style="width: 30%"></div>
                                </div>
                            </div>
                            
                            <div class="metric-item mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span>Error Rate</span>
                                    <span class="badge bg-success" id="errorRate">0.02%</span>
                                </div>
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar bg-success" id="errorRateBar" style="width: 2%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Live Feed and Alerts -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0"><i class="fas fa-rss"></i> Live Activity Feed</h6>
                        <div>
                            <button class="btn btn-outline-secondary btn-sm" onclick="clearActivityFeed()">
                                <i class="fas fa-trash"></i> Clear
                            </button>
                            <button class="btn btn-outline-primary btn-sm" onclick="pauseActivityFeed()" id="pauseFeedBtn">
                                <i class="fas fa-pause"></i> Pause
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="liveFeed" style="max-height: 400px; overflow-y: auto;">
                            <!-- Live feed items will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Active Alerts</h6>
                        <span class="badge bg-danger" id="alertCount">3</span>
                    </div>
                    <div class="card-body">
                        <div id="activeAlerts">
                            <!-- Active alerts will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Anomaly Detection -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-search"></i> Anomaly Detection</h6>
                    </div>
                    <div class="card-body">
                        <div id="anomalyDetection">
                            <div class="anomaly-item mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">Sentiment Anomaly</h6>
                                        <small class="text-muted">Unusual negative sentiment spike in tech sector</small>
                                    </div>
                                    <span class="badge bg-warning">Medium</span>
                                </div>
                                <div class="progress mt-2" style="height: 4px;">
                                    <div class="progress-bar bg-warning" style="width: 65%"></div>
                                </div>
                            </div>
                            
                            <div class="anomaly-item mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">Document Volume Spike</h6>
                                        <small class="text-muted">300% increase in policy documents from EU</small>
                                    </div>
                                    <span class="badge bg-danger">High</span>
                                </div>
                                <div class="progress mt-2" style="height: 4px;">
                                    <div class="progress-bar bg-danger" style="width: 85%"></div>
                                </div>
                            </div>
                            
                            <div class="anomaly-item mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">Network Pattern Change</h6>
                                        <small class="text-muted">New collaboration patterns detected</small>
                                    </div>
                                    <span class="badge bg-info">Low</span>
                                </div>
                                <div class="progress mt-2" style="height: 4px;">
                                    <div class="progress-bar bg-info" style="width: 35%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-chart-pie"></i> Alert Distribution</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="alertDistributionChart" style="height: 250px;"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Controls -->
        <div class="test-section">
            <h2><i class="fas fa-cogs me-2"></i>Test Controls</h2>
            <div class="row">
                <div class="col-md-6">
                    <h6>Quick Test Functions</h6>
                    <button class="btn btn-outline-primary me-2 mb-2" onclick="testMonitoringToggle()">
                        <i class="fas fa-power-off"></i> Test Start/Stop
                    </button>
                    <button class="btn btn-outline-success me-2 mb-2" onclick="testAlertThreshold('high')">
                        <i class="fas fa-bell"></i> Test High Alerts
                    </button>
                    <button class="btn btn-outline-info me-2 mb-2" onclick="testActivityFeed()">
                        <i class="fas fa-rss"></i> Test Activity Feed
                    </button>
                    <button class="btn btn-outline-warning me-2 mb-2" onclick="testPerformanceMetrics()">
                        <i class="fas fa-tachometer-alt"></i> Test Performance
                    </button>
                </div>
                <div class="col-md-6">
                    <h6>Test Results</h6>
                    <div id="testResults" class="alert alert-info">
                        <p><strong>Test Status:</strong> Ready to test realtime monitoring functionality</p>
                        <ul>
                            <li>✅ Real-time data streaming</li>
                            <li>✅ Dynamic alert system</li>
                            <li>✅ Live activity feed</li>
                            <li>✅ Performance monitoring</li>
                            <li>✅ Anomaly detection</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="dashboard/frontend/dashboard.js"></script>
    <script>
        // Test functions
        function testMonitoringToggle() {
            console.log('🧪 Testing monitoring start/stop...');
            
            if (isMonitoring) {
                stopRealTimeMonitoring();
                setTimeout(() => {
                    startRealTimeMonitoring();
                }, 2000);
            } else {
                startRealTimeMonitoring();
            }
            
            const testResults = document.getElementById('testResults');
            testResults.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle"></i> Monitoring Toggle Test</h6>
                    <p>✅ Monitoring start/stop functionality tested</p>
                    <p>✅ Button states updated correctly</p>
                    <p>✅ Status indicators working</p>
                    <p><strong>Try:</strong> Watch the real-time charts update</p>
                </div>
            `;
        }
        
        function testAlertThreshold(threshold) {
            console.log(`🧪 Testing alert threshold: ${threshold}`);
            document.getElementById('alertThreshold').value = threshold;
            updateAlertThreshold(threshold);
            
            const testResults = document.getElementById('testResults');
            testResults.innerHTML = `
                <div class="alert alert-warning">
                    <h6><i class="fas fa-bell"></i> Alert Threshold Test: ${threshold}</h6>
                    <p>✅ Alert threshold changed to ${threshold}</p>
                    <p>✅ Alert filtering updated</p>
                    <p>✅ Alert count recalculated</p>
                    <p><strong>Try:</strong> Switch between different thresholds</p>
                </div>
            `;
        }
        
        function testActivityFeed() {
            console.log('🧪 Testing activity feed...');
            
            // Add some test activities
            for (let i = 0; i < 5; i++) {
                setTimeout(() => {
                    addActivityFeedItem();
                }, i * 500);
            }
            
            const testResults = document.getElementById('testResults');
            testResults.innerHTML = `
                <div class="alert alert-info">
                    <h6><i class="fas fa-rss"></i> Activity Feed Test</h6>
                    <p>✅ Multiple activity items added</p>
                    <p>✅ Feed scrolling working</p>
                    <p>✅ Activity animations active</p>
                    <p><strong>Try:</strong> Pause/resume the feed</p>
                </div>
            `;
        }
        
        function testPerformanceMetrics() {
            console.log('🧪 Testing performance metrics...');
            
            // Simulate performance changes
            const metrics = realtimeMonitorData.metrics;
            metrics.processingSpeed = 2.5;
            metrics.memoryUsage = 85;
            metrics.networkLatency = 120;
            metrics.errorRate = 0.15;
            
            updateRealtimeData();
            
            const testResults = document.getElementById('testResults');
            testResults.innerHTML = `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-tachometer-alt"></i> Performance Metrics Test</h6>
                    <p>✅ Performance metrics updated</p>
                    <p>✅ Color coding changed based on values</p>
                    <p>✅ Progress bars updated</p>
                    <p><strong>Notice:</strong> Metrics now show degraded performance</p>
                </div>
            `;
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Realtime Monitor Test Page Loaded');
            
            // Initialize realtime monitor functionality
            if (typeof loadRealtimeMonitor === 'function') {
                loadRealtimeMonitor();
            } else {
                console.warn('Realtime Monitor functions not loaded. Make sure dashboard.js is included.');
            }
        });
    </script>
</body>
</html>
