#!/usr/bin/env python3
"""
AI Policy Analyzer - Analysis Results API
Provides REST API endpoints for analysis results storage and retrieval

Author: Claude Code  
Date: July 31, 2025
"""

import json
import os
import sys
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add backend path for imports
sys.path.append(os.path.dirname(__file__))

try:
    from flask import Flask, jsonify, request
    from flask_cors import CORS
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False
    print("Flask not available, using mock API mode")

from data_analysis_engine import PolicyAnalyzer, AnalysisStorageManager, BatchAnalysisProcessor

class AnalysisAPI:
    """Analysis results API handler"""
    
    def __init__(self):
        self.analyzer = PolicyAnalyzer()
        self.storage = AnalysisStorageManager()
        self.batch_processor = BatchAnalysisProcessor()
        
        if FLASK_AVAILABLE:
            self.app = Flask(__name__)
            CORS(self.app)
            self._setup_routes()
        else:
            self.app = None
    
    def _setup_routes(self):
        """Setup Flask routes"""
        
        @self.app.route('/api/analysis/health', methods=['GET'])
        def health_check():
            return jsonify({
                'status': 'healthy',
                'service': 'Analysis API',
                'version': '1.0',
                'database_path': self.storage.db_path
            })
        
        @self.app.route('/api/analysis/analyze', methods=['POST'])
        def analyze_document():
            """Analyze a single document"""
            try:
                data = request.get_json()
                
                if not data or 'text' not in data:
                    return jsonify({'error': 'Missing text in request'}), 400
                
                text = data['text']
                metadata = data.get('metadata', {})
                
                # Perform analysis
                result = self.analyzer.analyze_document(text, metadata)
                
                if 'error' not in result:
                    # Store result
                    if self.storage.store_analysis(result):
                        return jsonify({
                            'success': True,
                            'analysis_id': result['analysis_id'],
                            'analysis_result': result
                        })
                    else:
                        return jsonify({'error': 'Failed to store analysis result'}), 500
                else:
                    return jsonify({'error': result['error']}), 400
                    
            except Exception as e:
                return jsonify({'error': f'Analysis failed: {str(e)}'}), 500
        
        @self.app.route('/api/analysis/batch', methods=['POST'])
        def batch_analyze():
            """Batch analyze multiple documents"""
            try:
                data = request.get_json()
                
                if not data or 'documents' not in data:
                    return jsonify({'error': 'Missing documents in request'}), 400
                
                documents = data['documents']
                if not isinstance(documents, list):
                    return jsonify({'error': 'Documents must be a list'}), 400
                
                # Process batch
                results = self.batch_processor.analyze_text_batch(documents)
                
                return jsonify({
                    'success': True,
                    'batch_results': results
                })
                
            except Exception as e:
                return jsonify({'error': f'Batch analysis failed: {str(e)}'}), 500
        
        @self.app.route('/api/analysis/process-existing', methods=['POST'])
        def process_existing_data():
            """Process existing analysis files"""
            try:
                data = request.get_json()
                data_directory = data.get('directory') if data else None
                
                if not data_directory:
                    # Use default directory
                    data_directory = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
                
                results = self.batch_processor.process_existing_data(data_directory)
                
                return jsonify({
                    'success': True,
                    'processing_results': results
                })
                
            except Exception as e:
                return jsonify({'error': f'Processing failed: {str(e)}'}), 500
        
        @self.app.route('/api/analysis/results', methods=['GET'])
        def get_analysis_results():
            """Get analysis results with pagination"""
            try:
                limit = request.args.get('limit', 50, type=int)
                offset = request.args.get('offset', 0, type=int)
                
                # Limit maximum results per request
                limit = min(limit, 200)
                
                results = self.storage.get_all_analyses(limit=limit, offset=offset)
                
                return jsonify({
                    'success': True,
                    'results': results,
                    'pagination': {
                        'limit': limit,
                        'offset': offset,
                        'has_more': len(results) == limit
                    }
                })
                
            except Exception as e:
                return jsonify({'error': f'Failed to retrieve results: {str(e)}'}), 500
        
        @self.app.route('/api/analysis/results/<analysis_id>', methods=['GET'])
        def get_analysis_by_id(analysis_id):
            """Get specific analysis result by ID"""
            try:
                result = self.storage.get_analysis_by_id(analysis_id)
                
                if result:
                    return jsonify({
                        'success': True,
                        'analysis': result
                    })
                else:
                    return jsonify({'error': 'Analysis not found'}), 404
                    
            except Exception as e:
                return jsonify({'error': f'Failed to retrieve analysis: {str(e)}'}), 500
        
        @self.app.route('/api/analysis/statistics', methods=['GET'])
        def get_analysis_statistics():
            """Get overall analysis statistics"""
            try:
                stats = self.storage.get_analysis_statistics()
                
                return jsonify({
                    'success': True,
                    'statistics': stats
                })
                
            except Exception as e:
                return jsonify({'error': f'Failed to get statistics: {str(e)}'}), 500
        
        @self.app.route('/api/analysis/search', methods=['GET'])
        def search_analyses():
            """Search analysis results"""
            try:
                query = request.args.get('q', '')
                sentiment = request.args.get('sentiment')
                moral_frame = request.args.get('moral_frame')
                policy_preference = request.args.get('policy_preference')
                organization_type = request.args.get('organization_type')
                limit = request.args.get('limit', 50, type=int)
                
                # Build search query
                conditions = []
                params = []
                
                if query:
                    conditions.append("organization_name LIKE ?")
                    params.append(f"%{query}%")
                
                if sentiment:
                    conditions.append("dominant_sentiment = ?")
                    params.append(sentiment)
                
                if moral_frame:
                    conditions.append("dominant_moral_frame = ?")
                    params.append(moral_frame)
                
                if policy_preference:
                    conditions.append("dominant_policy_preference = ?")
                    params.append(policy_preference)
                
                if organization_type:
                    conditions.append("organization_type = ?")
                    params.append(organization_type)
                
                # Execute search
                where_clause = " AND ".join(conditions) if conditions else "1=1"
                
                import sqlite3
                with sqlite3.connect(self.storage.db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute(f'''
                        SELECT analysis_id, timestamp, organization_name, organization_type,
                               dominant_sentiment, dominant_moral_frame, dominant_policy_preference,
                               problem_intensity, word_count
                        FROM analysis_results 
                        WHERE {where_clause}
                        ORDER BY timestamp DESC
                        LIMIT ?
                    ''', params + [limit])
                    
                    columns = [description[0] for description in cursor.description]
                    results = []
                    
                    for row in cursor.fetchall():
                        result_dict = dict(zip(columns, row))
                        results.append(result_dict)
                
                return jsonify({
                    'success': True,
                    'results': results,
                    'search_params': {
                        'query': query,
                        'sentiment': sentiment,
                        'moral_frame': moral_frame,
                        'policy_preference': policy_preference,
                        'organization_type': organization_type
                    }
                })
                
            except Exception as e:
                return jsonify({'error': f'Search failed: {str(e)}'}), 500

    def run_server(self, host='localhost', port=5004, debug=False):
        """Run the Flask server"""
        if self.app:
            print(f"🚀 Starting Analysis API server on http://{host}:{port}")
            self.app.run(host=host, port=port, debug=debug)
        else:
            print("❌ Flask not available - cannot start server")

    def get_mock_response(self, endpoint: str, method: str = 'GET', data: dict = None) -> dict:
        """Get mock API responses when Flask is not available"""
        
        if endpoint == '/api/analysis/health':
            return {
                'status': 'healthy',
                'service': 'Analysis API (Mock)',
                'version': '1.0',
                'mode': 'mock'
            }
        
        elif endpoint == '/api/analysis/statistics':
            # Get real statistics if possible
            try:
                stats = self.storage.get_analysis_statistics()
                return {'success': True, 'statistics': stats}
            except:
                return {
                    'success': True,
                    'statistics': {
                        'total_analyses': 0,
                        'sentiment_distribution': {},
                        'error': 'Database not accessible'
                    }
                }
        
        elif endpoint == '/api/analysis/results':
            # Get real results if possible
            try:
                results = self.storage.get_all_analyses(limit=10)
                return {
                    'success': True,
                    'results': results,
                    'pagination': {'limit': 10, 'offset': 0, 'has_more': False}
                }
            except:
                return {
                    'success': True,
                    'results': [],
                    'error': 'Database not accessible'
                }
        
        else:
            return {'error': f'Mock endpoint not implemented: {endpoint}'}


def main():
    """Main function to start the Analysis API server"""
    
    print("🔍 AI Policy Analyzer - Analysis Results API")
    print("=" * 60)
    
    # Initialize API
    api = AnalysisAPI()
    
    # Check if we have existing data to process
    data_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
    print(f"📁 Checking for existing data in: {data_dir}")
    
    # Process existing data automatically
    print("🔄 Processing existing analysis data...")
    results = api.batch_processor.process_existing_data(data_dir)
    
    print(f"📊 Processing completed:")
    print(f"   ✅ Processed: {results['processed']}")
    print(f"   ❌ Errors: {results['errors']}")
    print(f"   ⏭️ Skipped: {results['skipped']}")
    
    # Show statistics
    stats = api.storage.get_analysis_statistics()
    print(f"\n📈 Database Statistics:")
    print(f"   Total Analyses: {stats.get('total_analyses', 0)}")
    
    if FLASK_AVAILABLE:
        print(f"\n🌐 API Endpoints:")
        print(f"   Health: http://localhost:5004/api/analysis/health")
        print(f"   Statistics: http://localhost:5004/api/analysis/statistics")
        print(f"   Results: http://localhost:5004/api/analysis/results")
        print(f"   Search: http://localhost:5004/api/analysis/search")
        print(f"   Analyze: POST http://localhost:5004/api/analysis/analyze")
        
        # Start server
        api.run_server(host='0.0.0.0', port=5004)
    else:
        print(f"\n⚠️ Flask not available - API running in mock mode")
        print(f"💾 Database available at: {api.storage.db_path}")
        
        # Test mock responses
        print(f"\n🧪 Testing mock API responses:")
        health = api.get_mock_response('/api/analysis/health')
        print(f"   Health: {health}")
        
        stats_response = api.get_mock_response('/api/analysis/statistics')
        print(f"   Statistics: {stats_response}")


if __name__ == "__main__":
    main()