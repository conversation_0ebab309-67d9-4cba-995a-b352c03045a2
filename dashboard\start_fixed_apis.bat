@echo off
echo 启动所有修复后的API服务 - AI Policy Analyzer Dashboard

REM 停止任何可能已经运行的服务
echo 正在停止现有服务...
taskkill /F /FI "WINDOWTITLE eq Analytics API*" 2>NUL
taskkill /F /FI "WINDOWTITLE eq Visualization API*" 2>NUL
taskkill /F /FI "WINDOWTITLE eq Search API*" 2>NUL
taskkill /F /FI "WINDOWTITLE eq Historical Data API*" 2>NUL
taskkill /F /FI "WINDOWTITLE eq Batch Processing API*" 2>NUL
timeout /t 2 /nobreak > NUL

REM 设置工作目录
cd /d "%~dp0"

REM 启动各API服务
echo.
echo 启动 Visualization API 在端口 5001...
start "Visualization API" cmd /k python backend\visualization_api.py

timeout /t 1 /nobreak > NUL
echo 启动 Search API 在端口 5002...
start "Search API" cmd /k python backend\search_api_server.py

timeout /t 1 /nobreak > NUL
echo 启动 Historical Data API 在端口 5003...
start "Historical Data API" cmd /k python backend\analysis_results_api.py

timeout /t 1 /nobreak > NUL
echo 启动 Analytics API 在端口 5005...
start "Analytics API" cmd /k python backend\advanced_analytics_api.py

timeout /t 1 /nobreak > NUL
echo 启动 Batch Processing API 在端口 5007...
start "Batch Processing API" cmd /k python backend\batch_processing_api.py

echo.
echo 所有API服务已启动，正在启动前端服务...
timeout /t 2 /nobreak > NUL

REM 启动前端服务
cd frontend
start "Frontend Server" cmd /k python -m http.server 8001

echo.
echo 访问仪表板: http://localhost:8001/index.html
start http://localhost:8001/index.html

echo.
echo 按任意键停止所有服务...
pause > NUL

REM 停止所有服务
taskkill /F /FI "WINDOWTITLE eq Analytics API*" 2>NUL
taskkill /F /FI "WINDOWTITLE eq Visualization API*" 2>NUL
taskkill /F /FI "WINDOWTITLE eq Search API*" 2>NUL
taskkill /F /FI "WINDOWTITLE eq Historical Data API*" 2>NUL
taskkill /F /FI "WINDOWTITLE eq Batch Processing API*" 2>NUL
taskkill /F /FI "WINDOWTITLE eq Frontend Server*" 2>NUL
