#!/usr/bin/env python3
"""
测试模型属性访问修复效果
验证"Cannot read properties of undefined (reading 'accuracy')"错误是否已解决

Author: <PERSON> Code
Date: 2025-08-10
"""

import os
import sys
import time
import json
from pathlib import Path

def test_model_property_safety():
    """测试模型属性安全访问"""
    print("🛡️ 测试模型属性安全访问...")
    
    dashboard_js = Path("dashboard/frontend/dashboard.js")
    
    if not dashboard_js.exists():
        print("   ❌ dashboard.js文件不存在")
        return False
    
    try:
        with open(dashboard_js, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查安全访问模式
        safety_checks = {
            'safe_model_access': '(mlModels && mlModels.' in content,
            'fallback_objects': 'modelInfo = (mlModels' in content,
            'property_validation': 'if (!modelInfo || typeof modelInfo' in content,
            'default_values': 'safeModelInfo = {' in content,
            'error_handling': 'Invalid modelInfo provided' in content
        }
        
        print("   安全访问检查:")
        all_safe = True
        for check_name, found in safety_checks.items():
            status = "✅" if found else "❌"
            print(f"     {status} {check_name.replace('_', ' ').title()}: {'Found' if found else 'Missing'}")
            if not found:
                all_safe = False
        
        if all_safe:
            print("   ✅ 所有安全访问模式已实现")
        else:
            print("   ⚠️ 部分安全访问模式缺失")
        
        return all_safe
        
    except Exception as e:
        print(f"   ❌ 检查安全访问时出错: {e}")
        return False

def test_fallback_model_definitions():
    """测试fallback模型定义"""
    print("🔄 测试fallback模型定义...")
    
    dashboard_js = Path("dashboard/frontend/dashboard.js")
    
    try:
        with open(dashboard_js, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查每个模型类型的fallback定义
        model_types = ['sentiment', 'topic', 'clustering', 'classification', 'anomaly']
        fallback_checks = {}
        
        for model_type in model_types:
            # 检查是否有fallback定义
            fallback_pattern = f"mlModels.{model_type}) ? mlModels.{model_type} : {{"
            has_fallback = fallback_pattern in content
            
            # 检查fallback对象是否包含必要属性
            if has_fallback:
                # 查找fallback对象的定义
                start_idx = content.find(fallback_pattern)
                if start_idx != -1:
                    # 找到对应的闭合括号
                    brace_count = 0
                    fallback_start = content.find('{', start_idx)
                    fallback_end = fallback_start
                    
                    for i, char in enumerate(content[fallback_start:], fallback_start):
                        if char == '{':
                            brace_count += 1
                        elif char == '}':
                            brace_count -= 1
                            if brace_count == 0:
                                fallback_end = i
                                break
                    
                    fallback_def = content[fallback_start:fallback_end+1]
                    
                    # 检查必要属性
                    required_props = ['name', 'version']
                    if model_type in ['sentiment', 'classification']:
                        required_props.extend(['accuracy', 'precision', 'recall', 'f1_score'])
                    elif model_type == 'anomaly':
                        required_props.extend(['precision', 'recall', 'f1_score'])
                    
                    props_found = all(prop in fallback_def for prop in required_props)
                    fallback_checks[model_type] = {
                        'has_fallback': True,
                        'has_required_props': props_found,
                        'complete': props_found
                    }
                else:
                    fallback_checks[model_type] = {
                        'has_fallback': True,
                        'has_required_props': False,
                        'complete': False
                    }
            else:
                fallback_checks[model_type] = {
                    'has_fallback': False,
                    'has_required_props': False,
                    'complete': False
                }
        
        print("   Fallback定义检查:")
        all_complete = True
        for model_type, checks in fallback_checks.items():
            complete_status = "✅" if checks['complete'] else "❌"
            print(f"     {complete_status} {model_type}: {'Complete' if checks['complete'] else 'Incomplete'}")
            if not checks['complete']:
                all_complete = False
                if not checks['has_fallback']:
                    print(f"       - Missing fallback definition")
                elif not checks['has_required_props']:
                    print(f"       - Missing required properties")
        
        if all_complete:
            print("   ✅ 所有模型类型都有完整的fallback定义")
        else:
            print("   ⚠️ 部分模型类型的fallback定义不完整")
        
        return all_complete
        
    except Exception as e:
        print(f"   ❌ 检查fallback定义时出错: {e}")
        return False

def test_error_handling_improvements():
    """测试错误处理改进"""
    print("🚨 测试错误处理改进...")
    
    dashboard_js = Path("dashboard/frontend/dashboard.js")
    
    try:
        with open(dashboard_js, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查错误处理改进
        error_handling_checks = {
            'property_validation': 'if (!modelInfo || typeof modelInfo' in content,
            'warning_messages': 'Invalid modelInfo provided' in content,
            'safe_property_access': 'safeModelInfo = {' in content,
            'default_fallbacks': 'modelInfo.accuracy || 0' in content,
            'user_friendly_errors': 'Model information not available' in content
        }
        
        print("   错误处理改进检查:")
        all_improved = True
        for improvement, found in error_handling_checks.items():
            status = "✅" if found else "❌"
            print(f"     {status} {improvement.replace('_', ' ').title()}: {'Found' if found else 'Missing'}")
            if not found:
                all_improved = False
        
        if all_improved:
            print("   ✅ 错误处理已全面改进")
        else:
            print("   ⚠️ 部分错误处理改进缺失")
        
        return all_improved
        
    except Exception as e:
        print(f"   ❌ 检查错误处理时出错: {e}")
        return False

def test_function_robustness():
    """测试函数健壮性"""
    print("💪 测试函数健壮性...")
    
    dashboard_js = Path("dashboard/frontend/dashboard.js")
    
    try:
        with open(dashboard_js, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键函数的健壮性
        critical_functions = [
            'updateModelPerformance',
            'updateModelMetrics',
            'generateSentimentAnalysisResults',
            'generateClassificationResults',
            'generateTopicModelingResults',
            'generateClusteringResults',
            'generateAnomalyDetectionResults'
        ]
        
        robustness_checks = {}
        
        for func_name in critical_functions:
            # 查找函数定义
            func_pattern = f"function {func_name}("
            func_start = content.find(func_pattern)
            
            if func_start != -1:
                # 查找函数结束
                brace_count = 0
                func_body_start = content.find('{', func_start)
                func_end = func_body_start
                
                for i, char in enumerate(content[func_body_start:], func_body_start):
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            func_end = i
                            break
                
                func_body = content[func_body_start:func_end+1]
                
                # 检查健壮性特征
                has_validation = 'if (!modelInfo' in func_body or 'typeof modelInfo' in func_body
                has_fallback = 'mlModels &&' in func_body or 'safeModelInfo' in func_body
                has_error_handling = 'try {' in func_body or 'catch' in func_body or 'console.warn' in func_body
                
                robustness_checks[func_name] = {
                    'has_validation': has_validation,
                    'has_fallback': has_fallback,
                    'has_error_handling': has_error_handling,
                    'robust': has_validation or has_fallback
                }
            else:
                robustness_checks[func_name] = {
                    'has_validation': False,
                    'has_fallback': False,
                    'has_error_handling': False,
                    'robust': False
                }
        
        print("   函数健壮性检查:")
        all_robust = True
        for func_name, checks in robustness_checks.items():
            robust_status = "✅" if checks['robust'] else "❌"
            print(f"     {robust_status} {func_name}: {'Robust' if checks['robust'] else 'Needs improvement'}")
            if not checks['robust']:
                all_robust = False
        
        if all_robust:
            print("   ✅ 所有关键函数都具有健壮性")
        else:
            print("   ⚠️ 部分函数需要改进健壮性")
        
        return all_robust
        
    except Exception as e:
        print(f"   ❌ 检查函数健壮性时出错: {e}")
        return False

def generate_test_report(results):
    """生成测试报告"""
    print("\n" + "="*60)
    print("📊 模型属性访问修复测试报告")
    print("="*60)
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    
    print(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
    
    print("\n📋 详细结果:")
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} {test_name.replace('_', ' ').title()}")
    
    overall_status = "✅ 全部通过" if passed_tests == total_tests else "⚠️ 部分失败" if passed_tests > 0 else "❌ 全部失败"
    print(f"\n🎯 总体状态: {overall_status}")
    
    if passed_tests == total_tests:
        print("\n🎉 恭喜！模型属性访问错误已完全修复！")
        print("💡 系统现在应该能够安全地访问模型属性，不会出现undefined错误。")
    else:
        print("\n⚠️ 仍有部分问题需要解决。")
        print("💡 请检查失败的测试项目并进行相应修复。")
    
    print("="*60)
    
    return passed_tests == total_tests

def main():
    """主函数"""
    print("🧪 AI Policy Analyzer - 模型属性访问修复测试")
    print("="*60)
    print("测试目标: 验证'Cannot read properties of undefined (reading 'accuracy')'错误修复效果")
    print()
    
    # 执行所有测试
    test_results = {
        'model_property_safety': test_model_property_safety(),
        'fallback_model_definitions': test_fallback_model_definitions(),
        'error_handling_improvements': test_error_handling_improvements(),
        'function_robustness': test_function_robustness()
    }
    
    # 生成测试报告
    all_passed = generate_test_report(test_results)
    
    # 保存测试结果
    try:
        report_data = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'test_results': test_results,
            'overall_success': all_passed,
            'summary': {
                'total_tests': len(test_results),
                'passed_tests': sum(1 for result in test_results.values() if result),
                'success_rate': (sum(1 for result in test_results.values() if result) / len(test_results)) * 100
            }
        }
        
        with open('model_property_fix_test_report.json', 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 测试报告已保存: model_property_fix_test_report.json")
        
    except Exception as e:
        print(f"\n⚠️ 保存测试报告失败: {e}")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
