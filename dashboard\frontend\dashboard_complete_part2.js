/**
 * Load dashboard data
 */
async function loadDashboardData() {
    try {
        showLoading(true);
        
        // Fetch dashboard overview data
        let totalDocuments = 0;
        let totalAnalyses = 0;
        let totalOrganizations = 0;
        let lastUpdated = new Date();
        
        try {
            // Try to fetch from API
            const response = await fetch(`${API_CONFIG.analytics}/dashboard/overview`);
            if (response.ok) {
                const data = await response.json();
                totalDocuments = data.total_documents || 0;
                totalAnalyses = data.total_analyses || 0;
                totalOrganizations = data.total_organizations || 0;
                lastUpdated = new Date(data.last_updated) || new Date();
            } else {
                throw new Error('API unavailable');
            }
        } catch (error) {
            console.warn('Failed to fetch dashboard overview data, using sample values');
            
            // Use sample values
            totalDocuments = 1287;
            totalAnalyses = 952;
            totalOrganizations = 342;
            lastUpdated = new Date();
            
            // Show notification for API issue
            showNotification('Dashboard API is not available. Showing sample data.', 'warning');
        }
        
        // Update UI
        safeUpdateElement('total-documents', 'textContent', totalDocuments);
        safeUpdateElement('total-analyses', 'textContent', totalAnalyses);
        safeUpdateElement('total-organizations', 'textContent', totalOrganizations);
        safeUpdateElement('last-updated', 'textContent', formatDate(lastUpdated));
        
        // Load charts and recent activity
        await loadDashboardCharts();
        loadRecentActivity();
    } catch (error) {
        console.error('Error loading dashboard data:', error);
        showNotification('Failed to load dashboard data. Please try again.', 'danger');
    } finally {
        showLoading(false);
    }
}

/**
 * Load dashboard charts
 */
async function loadDashboardCharts() {
    try {
        let orgTypes = [];
        let policyData = {};
        
        try {
            // Try to fetch data from API
            const facetsResponse = await fetch(`${API_CONFIG.search}/search/facets`);
            if (facetsResponse.ok) {
                const facetsData = await facetsResponse.json();
                orgTypes = facetsData.organization_types || [];
                
                const policyResponse = await fetch(`${API_CONFIG.visualization}/policy/distribution`);
                if (policyResponse.ok) {
                    policyData = await policyResponse.json();
                }
            } else {
                throw new Error('API unavailable');
            }
        } catch (error) {
            console.warn('Failed to fetch chart data, using sample values');
            
            // Use sample data for org types
            orgTypes = [
                { name: 'Corporate', count: 150 },
                { name: 'Academic', count: 95 },
                { name: 'Nonprofit', count: 82 },
                { name: 'Government', count: 15 }
            ];
            
            // Use sample data for policy preferences
            policyData = {
                labels: ['Self-Regulation', 'Co-Regulation', 'Government Oversight'],
                datasets: [
                    { 
                        label: 'Policy Preference',
                        data: [45, 30, 25]
                    }
                ]
            };
        }
        
        // Create/update charts
        createOrgTypeChart(orgTypes);
        createPolicyChart(policyData);
        createSentimentTrendChart();
        
    } catch (error) {
        console.error('Error loading dashboard charts:', error);
        showNotification('Failed to load charts. Using sample data.', 'warning');
        
        // Load with sample data as fallback
        createOrgTypeChart([
            { name: 'Corporate', count: 150 },
            { name: 'Academic', count: 95 },
            { name: 'Nonprofit', count: 82 },
            { name: 'Government', count: 15 }
        ]);
        
        createPolicyChart({
            labels: ['Self-Regulation', 'Co-Regulation', 'Government Oversight'],
            datasets: [{ label: 'Policy Preference', data: [45, 30, 25] }]
        });
        
        createSentimentTrendChart();
    }
}

/**
 * Create Organization Type Chart
 */
function createOrgTypeChart(orgTypes) {
    // Fix bug: Check if orgTypes is valid array
    if (!Array.isArray(orgTypes) || orgTypes.length === 0) {
        orgTypes = [
            { name: 'Corporate', count: 150 },
            { name: 'Academic', count: 95 },
            { name: 'Nonprofit', count: 82 },
            { name: 'Government', count: 15 }
        ];
    }
    
    const ctx = document.getElementById('orgTypeChart');
    if (!ctx) return;
    
    // Clean up any existing chart to avoid memory leaks
    if (dashboardState.charts.orgTypeChart) {
        dashboardState.charts.orgTypeChart.destroy();
    }
    
    // Prepare data
    const labels = orgTypes.map(item => item.name);
    const counts = orgTypes.map(item => item.count);
    const backgroundColors = [
        'rgba(13, 110, 253, 0.7)', // blue
        'rgba(32, 201, 151, 0.7)', // teal
        'rgba(111, 66, 193, 0.7)', // purple
        'rgba(253, 126, 20, 0.7)'  // orange
    ];
    
    // Create chart
    dashboardState.charts.orgTypeChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'Organization Types',
                data: counts,
                backgroundColor: backgroundColors,
                borderColor: backgroundColors.map(color => color.replace('0.7', '1')),
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        display: true,
                        drawBorder: false
                    }
                },
                x: {
                    grid: {
                        display: false,
                        drawBorder: false
                    }
                }
            }
        }
    });
}

/**
 * Create Policy Preferences Chart
 */
function createPolicyChart(policyData) {
    const ctx = document.getElementById('policyChart');
    if (!ctx) return;
    
    // Clean up any existing chart
    if (dashboardState.charts.policyChart) {
        dashboardState.charts.policyChart.destroy();
    }
    
    // Default data if none is provided
    const labels = policyData?.labels || ['Self-Regulation', 'Co-Regulation', 'Government Oversight'];
    const data = policyData?.datasets?.[0]?.data || [45, 30, 25];
    
    // Create chart
    dashboardState.charts.policyChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: [
                    'rgba(13, 110, 253, 0.7)',  // blue
                    'rgba(32, 201, 151, 0.7)',  // teal
                    'rgba(253, 126, 20, 0.7)'   // orange
                ],
                borderColor: [
                    'rgba(13, 110, 253, 1)',
                    'rgba(32, 201, 151, 1)',
                    'rgba(253, 126, 20, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            },
            cutout: '70%'
        }
    });
}

/**
 * Create Sentiment Trend Chart
 */
function createSentimentTrendChart() {
    const ctx = document.getElementById('sentimentTrendChart');
    if (!ctx) return;
    
    // Clean up any existing chart
    if (dashboardState.charts.sentimentTrendChart) {
        dashboardState.charts.sentimentTrendChart.destroy();
    }
    
    // Set up time period buttons
    setupTimePeriodButtons();
    
    // Load sentiment data for selected period
    loadSentimentTrendData(dashboardState.sentimentPeriod);
}

/**
 * Set up time period buttons for sentiment chart
 */
function setupTimePeriodButtons() {
    const periodButtons = document.querySelectorAll('.btn-time-period');
    if (!periodButtons.length) return;
    
    periodButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Update active state
            periodButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Get period
            const period = this.getAttribute('data-period');
            if (period) {
                dashboardState.sentimentPeriod = period;
                loadSentimentTrendData(period);
            }
        });
    });
    
    // Set initial active button
    const activeButton = document.querySelector(`.btn-time-period[data-period="${dashboardState.sentimentPeriod}"]`);
    if (activeButton) {
        activeButton.classList.add('active');
    }
}

/**
 * Load sentiment trend data for a specific time period
 */
async function loadSentimentTrendData(period) {
    try {
        let sentimentData;
        
        try {
            // Try to fetch from API
            const response = await fetch(`${API_CONFIG.visualization}/sentiment/trend?period=${period}`);
            if (response.ok) {
                sentimentData = await response.json();
            } else {
                throw new Error('API unavailable');
            }
        } catch (error) {
            console.warn('Failed to fetch sentiment trend data, using sample values');
            
            // Generate sample data
            const labels = [];
            const positive = [];
            const neutral = [];
            const negative = [];
            
            let dateFormat;
            let pointCount;
            
            switch (period) {
                case 'week':
                    dateFormat = { weekday: 'short' };
                    pointCount = 7;
                    for (let i = 6; i >= 0; i--) {
                        const date = new Date();
                        date.setDate(date.getDate() - i);
                        labels.push(date.toLocaleDateString('en-US', dateFormat));
                    }
                    break;
                case 'month':
                    pointCount = 30;
                    for (let i = 29; i >= 0; i--) {
                        const date = new Date();
                        date.setDate(date.getDate() - i);
                        labels.push(date.getDate().toString());
                    }
                    break;
                case 'year':
                    dateFormat = { month: 'short' };
                    pointCount = 12;
                    for (let i = 11; i >= 0; i--) {
                        const date = new Date();
                        date.setMonth(date.getMonth() - i);
                        labels.push(date.toLocaleDateString('en-US', dateFormat));
                    }
                    break;
                default:
                    pointCount = 12;
                    labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            }
            
            // Generate random data with some trend
            let posBase = Math.random() * 0.2 + 0.5; // 0.5-0.7
            let neuBase = Math.random() * 0.2 + 0.2; // 0.2-0.4
            let negBase = Math.random() * 0.1 + 0.1; // 0.1-0.2
            
            for (let i = 0; i < pointCount; i++) {
                positive.push((posBase + (Math.random() * 0.1 - 0.05)).toFixed(2));
                neutral.push((neuBase + (Math.random() * 0.1 - 0.05)).toFixed(2));
                negative.push((negBase + (Math.random() * 0.1 - 0.05)).toFixed(2));
                
                // Slightly adjust base values to create a trend
                posBase += (Math.random() * 0.02 - 0.01);
                neuBase += (Math.random() * 0.02 - 0.01);
                negBase += (Math.random() * 0.01 - 0.005);
                
                // Keep within bounds
                posBase = Math.min(Math.max(posBase, 0.4), 0.7);
                neuBase = Math.min(Math.max(neuBase, 0.1), 0.4);
                negBase = Math.min(Math.max(negBase, 0.05), 0.25);
            }
            
            sentimentData = {
                labels: labels,
                datasets: [
                    {
                        label: 'Positive',
                        data: positive,
                        borderColor: 'rgba(32, 201, 151, 1)',
                        backgroundColor: 'rgba(32, 201, 151, 0.1)',
                        fill: true
                    },
                    {
                        label: 'Neutral',
                        data: neutral,
                        borderColor: 'rgba(13, 110, 253, 1)',
                        backgroundColor: 'rgba(13, 110, 253, 0.1)',
                        fill: true
                    },
                    {
                        label: 'Negative',
                        data: negative,
                        borderColor: 'rgba(220, 53, 69, 1)',
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        fill: true
                    }
                ]
            };
        }
        
        updateSentimentTrendChart(sentimentData);
        
    } catch (error) {
        console.error('Error loading sentiment trend data:', error);
        
        // Still show something even if there's an error
        updateSentimentTrendChart({
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [
                {
                    label: 'Positive',
                    data: [0.55, 0.58, 0.62, 0.59, 0.63, 0.67, 0.65, 0.64, 0.66, 0.68, 0.65, 0.63],
                    borderColor: 'rgba(32, 201, 151, 1)',
                    backgroundColor: 'rgba(32, 201, 151, 0.1)',
                    fill: true
                },
                {
                    label: 'Neutral',
                    data: [0.25, 0.24, 0.22, 0.25, 0.23, 0.20, 0.22, 0.23, 0.22, 0.20, 0.22, 0.24],
                    borderColor: 'rgba(13, 110, 253, 1)',
                    backgroundColor: 'rgba(13, 110, 253, 0.1)',
                    fill: true
                },
                {
                    label: 'Negative',
                    data: [0.20, 0.18, 0.16, 0.16, 0.14, 0.13, 0.13, 0.13, 0.12, 0.12, 0.13, 0.13],
                    borderColor: 'rgba(220, 53, 69, 1)',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    fill: true
                }
            ]
        });
    }
}
