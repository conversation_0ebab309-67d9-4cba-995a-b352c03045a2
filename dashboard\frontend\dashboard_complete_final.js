/**
 * AI Policy Analyzer Dashboard
 * A modern dashboard for visualizing and analyzing AI policy data
 * 
 * Version: 2.0.0
 * Last Update: 2025-08-13
 */

// Define API configuration - This was missing in the original code
const API_CONFIG = {
    visualization: 'http://localhost:5001/api',
    search: 'http://localhost:5002/api',
    history: 'http://localhost:5003/api',
    batch: 'http://localhost:5007/api',
    analytics: 'http://localhost:5005/api/analytics'
};

// Initialize dashboard state - This was missing or incomplete
const dashboardState = {
    currentSection: 'dashboard',
    sidebarCollapsed: false,
    searchResults: [],
    historyData: [],
    historySortDirection: 'desc',
    charts: {
        orgTypeChart: null,
        policyChart: null,
        sentimentTrendChart: null
    },
    sentimentPeriod: 'month',
    theme: localStorage.getItem('dashboard_theme') || 'light'
};

/**
 * Show/hide loading overlay
 */
function showLoading(show = true) {
    const loader = document.getElementById('loadingOverlay');
    if (!loader) return;
    
    loader.style.display = show ? 'flex' : 'none';
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    const notifications = document.getElementById('notifications');
    if (!notifications) return;
    
    const id = 'notification-' + Date.now();
    const notification = document.createElement('div');
    notification.id = id;
    notification.className = `alert alert-${type} alert-dismissible fade show`;
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    notifications.appendChild(notification);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        const element = document.getElementById(id);
        if (element) {
            element.classList.remove('show');
            setTimeout(() => element.remove(), 300);
        }
    }, 5000);
}

/**
 * Format date
 */
function formatDate(date) {
    if (!date) return 'N/A';
    
    // Check if date is already a Date object, if not convert it
    const dateObj = date instanceof Date ? date : new Date(date);
    
    // Format options
    const options = { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };
    
    try {
        return dateObj.toLocaleDateString('en-US', options);
    } catch (e) {
        console.error('Date formatting error:', e);
        return 'Invalid Date';
    }
}

/**
 * Safe update element
 */
function safeUpdateElement(id, property, value) {
    const element = document.getElementById(id);
    if (element) {
        if (property === 'innerHTML' || property === 'textContent') {
            element[property] = value;
        } else if (property === 'value') {
            element.value = value;
        } else if (property === 'src' && element instanceof HTMLImageElement) {
            element.src = value;
        } else if (property === 'class' || property === 'className') {
            element.className = value;
        } else {
            console.warn(`Unsupported property '${property}' for element '${id}'`);
        }
    } else {
        console.warn(`Element '${id}' not found`);
    }
}

/**
 * Setup sidebar
 */
function setupSidebar() {
    const sidebar = document.querySelector('.sidebar');
    const mainContent = document.querySelector('.main-content');
    const sidebarToggle = document.getElementById('sidebarToggle');
    
    if (!sidebar || !mainContent) {
        console.error('Sidebar elements not found');
        return;
    }
    
    // Check for stored preference
    dashboardState.sidebarCollapsed = localStorage.getItem('sidebar_collapsed') === 'true';
    
    // Initialize sidebar state
    if (dashboardState.sidebarCollapsed) {
        sidebar.classList.add('collapsed');
        mainContent.classList.add('expanded');
        if (sidebarToggle) sidebarToggle.style.display = 'block';
    }
    
    // Toggle sidebar if button exists
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');
            
            dashboardState.sidebarCollapsed = sidebar.classList.contains('collapsed');
            localStorage.setItem('sidebar_collapsed', dashboardState.sidebarCollapsed);
            
            // Show/hide toggle button based on sidebar state
            sidebarToggle.style.display = sidebar.classList.contains('collapsed') ? 'block' : 'none';
        });
    }
    
    // Setup navigation links
    const navLinks = document.querySelectorAll('.nav-item');
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            navLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
            
            const section = this.getAttribute('data-section');
            if (section) {
                showSection(section);
            }
            
            // Close sidebar on mobile when link is clicked
            if (window.innerWidth < 768) {
                sidebar.classList.add('collapsed');
                mainContent.classList.add('expanded');
                dashboardState.sidebarCollapsed = true;
                localStorage.setItem('sidebar_collapsed', 'true');
            }
        });
    });
}

/**
 * Show a section and hide others
 */
function showSection(sectionName) {
    const sections = document.querySelectorAll('.content-section');
    
    sections.forEach(section => {
        section.style.display = section.id === `${sectionName}-section` ? 'block' : 'none';
    });
    
    // Update current section and load data
    dashboardState.currentSection = sectionName;
    loadSectionData(sectionName);
}

/**
 * Setup navigation
 */
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Update active state
            navLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
            
            // Get target section
            const targetSection = this.getAttribute('data-section') || 'dashboard';
            
            // Show section
            showSection(targetSection);
        });
    });
    
    // Handle top search
    const searchForm = document.getElementById('topSearchForm');
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            performSearch();
        });
    }
    
    // Initialize with dashboard section
    const dashboardLink = document.querySelector(`.list-group-item[data-section="dashboard"]`);
    if (dashboardLink) {
        dashboardLink.classList.add('active');
        showSection('dashboard');
    }
}

/**
 * Load data for a specific section
 */
function loadSectionData(sectionName) {
    switch (sectionName) {
        case 'dashboard':
            loadDashboardData();
            break;
        case 'search':
            // Search is loaded on demand
            break;
        case 'analysis':
            displayAnalysisSection();
            break;
        case 'upload':
            // Upload section is static
            break;
        case 'visualizations':
            // Load visualizations
            break;
        case 'sentiment-lab':
            loadSentimentLab();
            break;
        case 'network':
            loadNetworkAnalysis();
            break;
        case 'policy-simulator':
            loadPolicySimulator();
            break;
        case 'history':
            loadHistoricalData();
            break;
        default:
            console.log(`No data loader for section: ${sectionName}`);
    }
}
/**
 * Load dashboard data
 */
async function loadDashboardData() {
    try {
        showLoading(true);
        
        // Fetch dashboard overview data
        let totalDocuments = 0;
        let totalAnalyses = 0;
        let totalOrganizations = 0;
        let lastUpdated = new Date();
        
        try {
            // Try to fetch from API
            const response = await fetch(`${API_CONFIG.analytics}/dashboard/overview`);
            if (response.ok) {
                const data = await response.json();
                totalDocuments = data.total_documents || 0;
                totalAnalyses = data.total_analyses || 0;
                totalOrganizations = data.total_organizations || 0;
                lastUpdated = new Date(data.last_updated) || new Date();
            } else {
                throw new Error('API unavailable');
            }
        } catch (error) {
            console.warn('Failed to fetch dashboard overview data, using sample values');
            
            // Use sample values
            totalDocuments = 1287;
            totalAnalyses = 952;
            totalOrganizations = 342;
            lastUpdated = new Date();
            
            // Show notification for API issue
            showNotification('Dashboard API is not available. Showing sample data.', 'warning');
        }
        
        // Update UI
        safeUpdateElement('total-documents', 'textContent', totalDocuments);
        safeUpdateElement('total-analyses', 'textContent', totalAnalyses);
        safeUpdateElement('total-organizations', 'textContent', totalOrganizations);
        safeUpdateElement('last-updated', 'textContent', formatDate(lastUpdated));
        
        // Load charts and recent activity
        await loadDashboardCharts();
        loadRecentActivity();
    } catch (error) {
        console.error('Error loading dashboard data:', error);
        showNotification('Failed to load dashboard data. Please try again.', 'danger');
    } finally {
        showLoading(false);
    }
}

/**
 * Load dashboard charts
 */
async function loadDashboardCharts() {
    try {
        let orgTypes = [];
        let policyData = {};
        
        try {
            // Try to fetch data from API
            const facetsResponse = await fetch(`${API_CONFIG.search}/search/facets`);
            if (facetsResponse.ok) {
                const facetsData = await facetsResponse.json();
                orgTypes = facetsData.organization_types || [];
                
                const policyResponse = await fetch(`${API_CONFIG.visualization}/policy/distribution`);
                if (policyResponse.ok) {
                    policyData = await policyResponse.json();
                }
            } else {
                throw new Error('API unavailable');
            }
        } catch (error) {
            console.warn('Failed to fetch chart data, using sample values');
            
            // Use sample data for org types
            orgTypes = [
                { name: 'Corporate', count: 150 },
                { name: 'Academic', count: 95 },
                { name: 'Nonprofit', count: 82 },
                { name: 'Government', count: 15 }
            ];
            
            // Use sample data for policy preferences
            policyData = {
                labels: ['Self-Regulation', 'Co-Regulation', 'Government Oversight'],
                datasets: [
                    { 
                        label: 'Policy Preference',
                        data: [45, 30, 25]
                    }
                ]
            };
        }
        
        // Create/update charts
        createOrgTypeChart(orgTypes);
        createPolicyChart(policyData);
        createSentimentTrendChart();
        
    } catch (error) {
        console.error('Error loading dashboard charts:', error);
        showNotification('Failed to load charts. Using sample data.', 'warning');
        
        // Load with sample data as fallback
        createOrgTypeChart([
            { name: 'Corporate', count: 150 },
            { name: 'Academic', count: 95 },
            { name: 'Nonprofit', count: 82 },
            { name: 'Government', count: 15 }
        ]);
        
        createPolicyChart({
            labels: ['Self-Regulation', 'Co-Regulation', 'Government Oversight'],
            datasets: [{ label: 'Policy Preference', data: [45, 30, 25] }]
        });
        
        createSentimentTrendChart();
    }
}

/**
 * Create Organization Type Chart
 */
function createOrgTypeChart(orgTypes) {
    // Fix bug: Check if orgTypes is valid array
    if (!Array.isArray(orgTypes) || orgTypes.length === 0) {
        orgTypes = [
            { name: 'Corporate', count: 150 },
            { name: 'Academic', count: 95 },
            { name: 'Nonprofit', count: 82 },
            { name: 'Government', count: 15 }
        ];
    }
    
    const ctx = document.getElementById('orgTypeChart');
    if (!ctx) return;
    
    // Clean up any existing chart to avoid memory leaks
    if (dashboardState.charts.orgTypeChart) {
        dashboardState.charts.orgTypeChart.destroy();
    }
    
    // Prepare data
    const labels = orgTypes.map(item => item.name);
    const counts = orgTypes.map(item => item.count);
    const backgroundColors = [
        'rgba(13, 110, 253, 0.7)', // blue
        'rgba(32, 201, 151, 0.7)', // teal
        'rgba(111, 66, 193, 0.7)', // purple
        'rgba(253, 126, 20, 0.7)'  // orange
    ];
    
    // Create chart
    dashboardState.charts.orgTypeChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'Organization Types',
                data: counts,
                backgroundColor: backgroundColors,
                borderColor: backgroundColors.map(color => color.replace('0.7', '1')),
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        display: true,
                        drawBorder: false
                    }
                },
                x: {
                    grid: {
                        display: false,
                        drawBorder: false
                    }
                }
            }
        }
    });
}

/**
 * Create Policy Preferences Chart
 */
function createPolicyChart(policyData) {
    const ctx = document.getElementById('policyChart');
    if (!ctx) return;
    
    // Clean up any existing chart
    if (dashboardState.charts.policyChart) {
        dashboardState.charts.policyChart.destroy();
    }
    
    // Default data if none is provided
    const labels = policyData?.labels || ['Self-Regulation', 'Co-Regulation', 'Government Oversight'];
    const data = policyData?.datasets?.[0]?.data || [45, 30, 25];
    
    // Create chart
    dashboardState.charts.policyChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: [
                    'rgba(13, 110, 253, 0.7)',  // blue
                    'rgba(32, 201, 151, 0.7)',  // teal
                    'rgba(253, 126, 20, 0.7)'   // orange
                ],
                borderColor: [
                    'rgba(13, 110, 253, 1)',
                    'rgba(32, 201, 151, 1)',
                    'rgba(253, 126, 20, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            },
            cutout: '70%'
        }
    });
}

/**
 * Create Sentiment Trend Chart
 */
function createSentimentTrendChart() {
    const ctx = document.getElementById('sentimentTrendChart');
    if (!ctx) return;
    
    // Clean up any existing chart
    if (dashboardState.charts.sentimentTrendChart) {
        dashboardState.charts.sentimentTrendChart.destroy();
    }
    
    // Set up time period buttons
    setupTimePeriodButtons();
    
    // Load sentiment data for selected period
    loadSentimentTrendData(dashboardState.sentimentPeriod);
}

/**
 * Set up time period buttons for sentiment chart
 */
function setupTimePeriodButtons() {
    const periodButtons = document.querySelectorAll('.btn-time-period');
    if (!periodButtons.length) return;
    
    periodButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Update active state
            periodButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Get period
            const period = this.getAttribute('data-period');
            if (period) {
                dashboardState.sentimentPeriod = period;
                loadSentimentTrendData(period);
            }
        });
    });
    
    // Set initial active button
    const activeButton = document.querySelector(`.btn-time-period[data-period="${dashboardState.sentimentPeriod}"]`);
    if (activeButton) {
        activeButton.classList.add('active');
    }
}

/**
 * Load sentiment trend data for a specific time period
 */
async function loadSentimentTrendData(period) {
    try {
        let sentimentData;
        
        try {
            // Try to fetch from API
            const response = await fetch(`${API_CONFIG.visualization}/sentiment/trend?period=${period}`);
            if (response.ok) {
                sentimentData = await response.json();
            } else {
                throw new Error('API unavailable');
            }
        } catch (error) {
            console.warn('Failed to fetch sentiment trend data, using sample values');
            
            // Generate sample data
            const labels = [];
            const positive = [];
            const neutral = [];
            const negative = [];
            
            let dateFormat;
            let pointCount;
            
            switch (period) {
                case 'week':
                    dateFormat = { weekday: 'short' };
                    pointCount = 7;
                    for (let i = 6; i >= 0; i--) {
                        const date = new Date();
                        date.setDate(date.getDate() - i);
                        labels.push(date.toLocaleDateString('en-US', dateFormat));
                    }
                    break;
                case 'month':
                    pointCount = 30;
                    for (let i = 29; i >= 0; i--) {
                        const date = new Date();
                        date.setDate(date.getDate() - i);
                        labels.push(date.getDate().toString());
                    }
                    break;
                case 'year':
                    dateFormat = { month: 'short' };
                    pointCount = 12;
                    for (let i = 11; i >= 0; i--) {
                        const date = new Date();
                        date.setMonth(date.getMonth() - i);
                        labels.push(date.toLocaleDateString('en-US', dateFormat));
                    }
                    break;
                default:
                    pointCount = 12;
                    labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            }
            
            // Generate random data with some trend
            let posBase = Math.random() * 0.2 + 0.5; // 0.5-0.7
            let neuBase = Math.random() * 0.2 + 0.2; // 0.2-0.4
            let negBase = Math.random() * 0.1 + 0.1; // 0.1-0.2
            
            for (let i = 0; i < pointCount; i++) {
                positive.push((posBase + (Math.random() * 0.1 - 0.05)).toFixed(2));
                neutral.push((neuBase + (Math.random() * 0.1 - 0.05)).toFixed(2));
                negative.push((negBase + (Math.random() * 0.1 - 0.05)).toFixed(2));
                
                // Slightly adjust base values to create a trend
                posBase += (Math.random() * 0.02 - 0.01);
                neuBase += (Math.random() * 0.02 - 0.01);
                negBase += (Math.random() * 0.01 - 0.005);
                
                // Keep within bounds
                posBase = Math.min(Math.max(posBase, 0.4), 0.7);
                neuBase = Math.min(Math.max(neuBase, 0.1), 0.4);
                negBase = Math.min(Math.max(negBase, 0.05), 0.25);
            }
            
            sentimentData = {
                labels: labels,
                datasets: [
                    {
                        label: 'Positive',
                        data: positive,
                        borderColor: 'rgba(32, 201, 151, 1)',
                        backgroundColor: 'rgba(32, 201, 151, 0.1)',
                        fill: true
                    },
                    {
                        label: 'Neutral',
                        data: neutral,
                        borderColor: 'rgba(13, 110, 253, 1)',
                        backgroundColor: 'rgba(13, 110, 253, 0.1)',
                        fill: true
                    },
                    {
                        label: 'Negative',
                        data: negative,
                        borderColor: 'rgba(220, 53, 69, 1)',
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        fill: true
                    }
                ]
            };
        }
        
        updateSentimentTrendChart(sentimentData);
        
    } catch (error) {
        console.error('Error loading sentiment trend data:', error);
        
        // Still show something even if there's an error
        updateSentimentTrendChart({
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [
                {
                    label: 'Positive',
                    data: [0.55, 0.58, 0.62, 0.59, 0.63, 0.67, 0.65, 0.64, 0.66, 0.68, 0.65, 0.63],
                    borderColor: 'rgba(32, 201, 151, 1)',
                    backgroundColor: 'rgba(32, 201, 151, 0.1)',
                    fill: true
                },
                {
                    label: 'Neutral',
                    data: [0.25, 0.24, 0.22, 0.25, 0.23, 0.20, 0.22, 0.23, 0.22, 0.20, 0.22, 0.24],
                    borderColor: 'rgba(13, 110, 253, 1)',
                    backgroundColor: 'rgba(13, 110, 253, 0.1)',
                    fill: true
                },
                {
                    label: 'Negative',
                    data: [0.20, 0.18, 0.16, 0.16, 0.14, 0.13, 0.13, 0.13, 0.12, 0.12, 0.13, 0.13],
                    borderColor: 'rgba(220, 53, 69, 1)',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    fill: true
                }
            ]
        });
    }
}
/**
 * Update Sentiment Trend Chart with data
 */
function updateSentimentTrendChart(sentimentData) {
    const ctx = document.getElementById('sentimentTrendChart');
    if (!ctx) return;
    
    // Clean up any existing chart
    if (dashboardState.charts.sentimentTrendChart) {
        dashboardState.charts.sentimentTrendChart.destroy();
    }
    
    // Create chart with the data
    dashboardState.charts.sentimentTrendChart = new Chart(ctx, {
        type: 'line',
        data: sentimentData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 1,
                    ticks: {
                        callback: function(value) {
                            return (value * 100) + '%';
                        }
                    },
                    grid: {
                        display: true,
                        drawBorder: false
                    }
                },
                x: {
                    grid: {
                        display: false,
                        drawBorder: false
                    }
                }
            }
        }
    });
}

/**
 * Load recent activity
 */
function loadRecentActivity() {
    const recentActivityElement = document.getElementById('recent-activity');
    if (!recentActivityElement) return;
    
    // Try to fetch recent activity from API
    fetch(`${API_CONFIG.history}/activity/recent`)
        .then(response => {
            if (!response.ok) throw new Error('API unavailable');
            return response.json();
        })
        .then(data => {
            displayRecentActivity(data.activities || []);
        })
        .catch(error => {
            console.warn('Failed to fetch recent activity, using sample values');
            
            // Use sample data
            const sampleActivities = [
                {
                    id: 'act-1',
                    type: 'analysis',
                    organization: 'Google LLC',
                    document_type: 'AI Policy',
                    timestamp: new Date(new Date().setHours(new Date().getHours() - 2)).toISOString()
                },
                {
                    id: 'act-2',
                    type: 'upload',
                    organization: 'OpenAI',
                    document_type: 'Safety Guidelines',
                    timestamp: new Date(new Date().setHours(new Date().getHours() - 5)).toISOString()
                },
                {
                    id: 'act-3',
                    type: 'search',
                    query: 'regulatory frameworks',
                    results: 24,
                    timestamp: new Date(new Date().setHours(new Date().getHours() - 6)).toISOString()
                },
                {
                    id: 'act-4',
                    type: 'analysis',
                    organization: 'Microsoft Corporation',
                    document_type: 'Policy Statement',
                    timestamp: new Date(new Date().setHours(new Date().getHours() - 8)).toISOString()
                },
                {
                    id: 'act-5',
                    type: 'upload',
                    organization: '1Day Sooner',
                    document_type: 'Position Paper',
                    timestamp: new Date(new Date().setDate(new Date().getDate() - 1)).toISOString()
                }
            ];
            
            displayRecentActivity(sampleActivities);
        });
}

/**
 * Display recent activity
 */
function displayRecentActivity(activities) {
    const recentActivityElement = document.getElementById('recent-activity');
    if (!recentActivityElement) return;
    
    if (!activities || activities.length === 0) {
        recentActivityElement.innerHTML = '<div class="text-center text-muted py-3">No recent activity</div>';
        return;
    }
    
    let activityHTML = '';
    
    activities.forEach(activity => {
        const time = formatDate(new Date(activity.timestamp));
        let content = '';
        let icon = '';
        
        switch (activity.type) {
            case 'analysis':
                icon = '<div class="activity-icon bg-primary"><i class="fas fa-chart-bar"></i></div>';
                content = `<strong>Analyzed</strong> ${activity.organization} ${activity.document_type}`;
                break;
            case 'upload':
                icon = '<div class="activity-icon bg-success"><i class="fas fa-file-upload"></i></div>';
                content = `<strong>Uploaded</strong> ${activity.organization} ${activity.document_type}`;
                break;
            case 'search':
                icon = '<div class="activity-icon bg-info"><i class="fas fa-search"></i></div>';
                content = `<strong>Searched</strong> for "${activity.query}" (${activity.results} results)`;
                break;
            default:
                icon = '<div class="activity-icon bg-secondary"><i class="fas fa-bell"></i></div>';
                content = `<strong>${activity.type}</strong> ${JSON.stringify(activity).slice(0, 50)}...`;
        }
        
        activityHTML += `
            <div class="activity-item">
                ${icon}
                <div class="activity-content">
                    <div class="activity-title">${content}</div>
                    <div class="activity-time">${time}</div>
                </div>
            </div>
        `;
    });
    
    recentActivityElement.innerHTML = activityHTML;
}

/**
 * Set up file upload functionality
 */
function setupFileUpload() {
    const dropArea = document.getElementById('drop-area');
    const fileInput = document.getElementById('fileInput');
    
    if (!dropArea || !fileInput) return;
    
    // Prevent default behaviors
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, preventDefaults, false);
    });
    
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    // Highlight drop area when file is dragged over it
    ['dragenter', 'dragover'].forEach(eventName => {
        dropArea.addEventListener(eventName, highlight, false);
    });
    
    ['dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, unhighlight, false);
    });
    
    function highlight() {
        dropArea.classList.add('highlight');
    }
    
    function unhighlight() {
        dropArea.classList.remove('highlight');
    }
    
    // Handle dropped files
    dropArea.addEventListener('drop', handleDrop, false);
    
    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        
        handleFiles(files);
    }
    
    // Handle selected files
    fileInput.addEventListener('change', function() {
        handleFiles(this.files);
    });
    
    // Click anywhere in drop area to trigger file input
    dropArea.addEventListener('click', function() {
        fileInput.click();
    });
}

/**
 * Handle uploaded files
 */
function handleFiles(files) {
    if (!files || files.length === 0) return;
    
    console.log(`📁 Processing ${files.length} file(s)...`);
    
    const uploadProgress = document.getElementById('uploadProgress');
    const progressBar = document.getElementById('progressBar');
    const uploadStatus = document.getElementById('uploadStatus');
    
    if (!uploadProgress || !progressBar || !uploadStatus) return;
    
    uploadProgress.style.display = 'block';
    
    // Simulate upload progress
    let progress = 0;
    const interval = setInterval(() => {
        progress += 10;
        progressBar.style.width = progress + '%';
        progressBar.textContent = progress + '%';
        
        if (progress >= 100) {
            clearInterval(interval);
            
            // Show completion status
            uploadStatus.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i> 
                    Successfully uploaded ${files.length} file(s). Analysis will begin shortly.
                </div>
            `;
            
            // Add to upload history
            addToUploadHistory(files);
            
            // Reset after 3 seconds
            setTimeout(() => {
                uploadProgress.style.display = 'none';
                progressBar.style.width = '0%';
                progressBar.textContent = '';
                uploadStatus.innerHTML = '';
            }, 3000);
        }
    }, 200);
}

/**
 * Add uploaded files to history
 */
function addToUploadHistory(files) {
    const uploadHistory = document.getElementById('uploadHistory');
    if (!uploadHistory) return;
    
    // Clear "No recent uploads" message if present
    if (uploadHistory.innerHTML.includes('No recent uploads')) {
        uploadHistory.innerHTML = '';
    }
    
    // Add each file to history
    Array.from(files).forEach(file => {
        const row = document.createElement('tr');
        const now = new Date();
        
        row.innerHTML = `
            <td>${file.name}</td>
            <td>${now.toLocaleDateString()} ${now.toLocaleTimeString()}</td>
            <td><span class="badge bg-success">Processed</span></td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="viewAnalysis('${file.name}')">
                    <i class="fas fa-eye me-1"></i> View
                </button>
            </td>
        `;
        
        uploadHistory.prepend(row);
    });
}

/**
 * Setup search functionality
 */
function setupSearch() {
    // Search suggestions
    const searchInput = document.getElementById('searchInput');
    
    if (!searchInput) return;
    
    // Fix bug: Ensure the search input event listener is properly set
    searchInput.addEventListener('input', function() {
        const query = this.value;
        if (query.length > 2) {
            // Could implement search suggestions here
            console.log('Search suggestions for:', query);
        }
    });

    // Fix bug: Add form submission handling for search form
    const searchForm = document.getElementById('searchForm');
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            performSearch();
        });
    }
}

/**
 * Perform search
 */
async function performSearch() {
    // Fix bug: Get search input value more robustly
    const searchInput = document.getElementById('searchInput');
    if (!searchInput) {
        showNotification('Search input not found', 'error');
        return;
    }

    const query = searchInput.value.trim();
    const orgTypeFilter = document.getElementById('orgTypeFilter')?.value || '';
    const sectorFilter = document.getElementById('sectorFilter')?.value || '';
    const analysisFilter = document.getElementById('analysisFilter')?.value || '';
    
    // Navigate to search section if not already there
    navigateToSection('search');
    
    if (!query && !orgTypeFilter && !sectorFilter && !analysisFilter) {
        showNotification('Please enter a search query or select filters.', 'warning');
        return;
    }
    
    try {
        showLoading(true);
        
        let searchUrl = `${API_CONFIG.search}/search`;
        const params = new URLSearchParams();
        
        if (query) params.append('query', query);
        if (orgTypeFilter) params.append('organization_type', orgTypeFilter);
        if (sectorFilter) params.append('sector', sectorFilter);
        
        if (params.toString()) {
            searchUrl += '?' + params.toString();
        }
        
        const response = await fetch(searchUrl);
        const data = await response.json();
        
        if (response.ok) {
            displaySearchResults(data);
        } else {
            throw new Error(data.error || 'Search failed');
        }
        
    } catch (error) {
        console.error('Search error:', error);
        
        // Show sample results instead
        displaySampleResults();
        
        showNotification('Search API not available. Showing sample results.', 'info');
    } finally {
        showLoading(false);
    }
}

/**
 * Navigate to a specific section
 */
function navigateToSection(sectionName) {
    const navLink = document.querySelector(`.list-group-item[data-section="${sectionName}"]`);
    if (navLink) {
        navLink.click();
    }
}
/**
 * Display search results
 */
function displaySearchResults(data) {
    const resultsContainer = document.getElementById('searchResults');
    if (!resultsContainer) return;
    
    // Fix bug: Check if data is properly formatted
    if (!data || (!data.results && !Array.isArray(data))) {
        displaySampleResults();
        return;
    }

    // Support both data formats: object with results array or direct array
    const results = Array.isArray(data) ? data : (data.results || []);
    
    if (results.length === 0) {
        // Show empty state if no results found
        resultsContainer.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> 
                No results found. Please try a different search query or filters.
            </div>
        `;
        return;
    }
    
    let resultsHTML = `
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5>Search Results (${data.total_results || results.length})</h5>
            <small class="text-muted">Query: "${data.query || document.getElementById('searchInput')?.value || 'filtered search'}"</small>
        </div>
    `;
    
    resultsHTML += createResultsTable(results);
    
    resultsContainer.innerHTML = resultsHTML;
    dashboardState.searchResults = results;
}

/**
 * Display sample search results
 */
function displaySampleResults() {
    const sampleResults = [
        {
            document_id: 'google_llc_analysis',
            organization_name: 'Google LLC',
            organization_type: 'Corporate',
            sector: 'Technology',  
            document_type: 'AI Policy Statement',
            relevance_score: '95%'
        },
        {
            document_id: '1day_sooner_analysis',
            organization_name: '1Day Sooner',
            organization_type: 'Nonprofit',
            sector: 'Research',
            document_type: 'AI RFI Response',
            relevance_score: '92%'
        },
        {
            document_id: 'microsoft_corporation_analysis',
            organization_name: 'Microsoft Corporation',
            organization_type: 'Corporate',
            sector: 'Technology',
            document_type: 'Policy Framework',
            relevance_score: '88%'
        },
        {
            document_id: '3c_analysis',
            organization_name: '3C',
            organization_type: 'Academic',
            sector: 'Research',
            document_type: 'Position Paper',
            relevance_score: '85%'
        },
        {
            document_id: 'openai_analysis',
            organization_name: 'OpenAI',
            organization_type: 'Corporate',
            sector: 'AI Research',
            document_type: 'Safety Guidelines',
            relevance_score: '90%'
        }
    ];
    
    const resultsContainer = document.getElementById('searchResults');
    if (!resultsContainer) return;
    
    let resultsHTML = `
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5>Sample Analysis Results (${sampleResults.length})</h5>
            <small class="text-muted">Click "View" to see detailed analysis</small>
        </div>
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i> 
            These are sample results from the analysis database. Search functionality requires API connections.
        </div>
    `;
    
    resultsHTML += createResultsTable(sampleResults);
    
    resultsContainer.innerHTML = resultsHTML;
    dashboardState.searchResults = sampleResults;
}

/**
 * Create a results table from data
 */
function createResultsTable(results) {
    // Fix bug: Handle case when results is not an array
    if (!Array.isArray(results)) {
        console.error("Expected results to be an array but got:", typeof results);
        return '<div class="alert alert-danger">Invalid results format</div>';
    }

    let tableHTML = `
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Organization</th>
                        <th>Type</th>
                        <th>Sector</th>
                        <th>Document Type</th>
                        <th>Relevance</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    results.forEach(result => {
        // Fix bug: Handle potential missing fields with default values
        const orgName = result.organization_name || 'Unknown';
        const orgType = result.organization_type || 'N/A';
        const sector = result.sector || 'N/A';
        const docType = result.document_type || 'Document';
        const relevance = result.relevance_score || 'N/A';
        const docId = result.document_id || `doc_${Math.random().toString(36).substr(2, 9)}`;
        
        tableHTML += `
            <tr>
                <td><strong>${orgName}</strong></td>
                <td><span class="badge bg-secondary">${orgType}</span></td>
                <td>${sector}</td>
                <td>${docType}</td>
                <td><span class="badge bg-primary">${relevance}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewAnalysis('${docId}')">
                        <i class="fas fa-eye me-1"></i> View
                    </button>
                </td>
            </tr>
        `;
    });
    
    tableHTML += `
                </tbody>
            </table>
        </div>
    `;
    
    return tableHTML;
}

/**
 * Toggle filter panel
 */
function toggleFilters() {
    const filterPanel = document.getElementById('filterPanel');
    if (filterPanel) {
        filterPanel.style.display = filterPanel.style.display === 'none' ? 'block' : 'none';
    }
}

/**
 * View analysis for a document
 */
function viewAnalysis(documentId) {
    // Navigate to analysis section
    navigateToSection('analysis');
    
    // Fix bug: Handle case when searchResults is not initialized
    if (!dashboardState.searchResults || !Array.isArray(dashboardState.searchResults)) {
        dashboardState.searchResults = [];
    }
    
    // Find the document in search results
    const document = dashboardState.searchResults.find(doc => doc.document_id === documentId);
    
    if (!document) {
        console.warn(`Document not found: ${documentId}`);
        // Generate a generic document with this ID for display purposes
        const genericDocument = {
            document_id: documentId,
            organization_name: documentId.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' '),
            organization_type: 'Unknown',
            sector: 'Unknown',
            document_type: 'Document',
            relevance_score: 'N/A'
        };
        
        // Show loading while fetching analysis
        showLoading(true);
        
        // Simulate API call delay
        setTimeout(() => {
            // Generate sample analysis data
            const analysisData = generateSampleAnalysis(genericDocument);
            
            // Display analysis
            displayAnalysisResults(analysisData);
            
            // Hide loading
            showLoading(false);
        }, 1000);
        
        return;
    }
    
    // Show loading while fetching analysis
    showLoading(true);
    
    // Try to fetch from API first
    fetch(`${API_CONFIG.analytics}/analysis/${documentId}`)
        .then(response => {
            if (!response.ok) throw new Error('API unavailable');
            return response.json();
        })
        .then(analysisData => {
            // Display analysis from API
            displayAnalysisResults(analysisData);
        })
        .catch(error => {
            console.warn(`Failed to fetch analysis for document ${documentId}, using sample data`);
            
            // Generate sample analysis data as fallback
            const analysisData = generateSampleAnalysis(document);
            
            // Display analysis
            displayAnalysisResults(analysisData);
        })
        .finally(() => {
            // Hide loading
            showLoading(false);
        });
}

/**
 * Generate sample analysis data for a document
 */
function generateSampleAnalysis(document) {
    return {
        document_id: document.document_id,
        organization_name: document.organization_name,
        organization_type: document.organization_type,
        sector: document.sector,
        document_type: document.document_type,
        analysis_timestamp: new Date().toISOString(),
        sentiment_analysis: {
            overall_sentiment: Math.random() > 0.6 ? 'Positive' : (Math.random() > 0.5 ? 'Neutral' : 'Negative'),
            sentiment_scores: {
                positive: (Math.random() * 0.6 + 0.3).toFixed(2),
                neutral: (Math.random() * 0.3).toFixed(2),
                negative: (Math.random() * 0.3).toFixed(2)
            },
            key_phrases: [
                'artificial intelligence',
                'policy regulation',
                'ethics framework',
                'responsible development',
                'governance mechanisms'
            ]
        },
        policy_analysis: {
            dominant_preference: Math.random() > 0.5 ? 'Self-Regulation' : (Math.random() > 0.5 ? 'Co-Regulation' : 'Government Oversight'),
            regulatory_stance: Math.random() > 0.6 ? 'Supportive' : (Math.random() > 0.5 ? 'Neutral' : 'Cautious'),
            key_concerns: [
                'Safety',
                'Privacy',
                'Transparency',
                'Accountability',
                'Fairness'
            ]
        }
    };
}

/**
 * Load Sentiment Lab section
 */
function loadSentimentLab() {
    const sentimentLabSection = document.getElementById('sentiment-lab-section');
    if (!sentimentLabSection) return;
    
    sentimentLabSection.innerHTML = `
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="h4 mb-0">Sentiment Analysis Lab</h2>
            <button class="btn btn-sm btn-outline-primary" onclick="runSentimentAnalysis()">
                <i class="fas fa-play me-1"></i> Run Analysis
            </button>
        </div>
        
        <div class="row g-3">
            <div class="col-md-6">
                <div class="card shadow-sm h-100">
                    <div class="card-header bg-white">
                        <h5 class="card-title mb-0">Input Text</h5>
                    </div>
                    <div class="card-body">
                        <textarea id="sentimentInput" class="form-control" rows="10" placeholder="Paste AI policy text here for sentiment analysis..."></textarea>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card shadow-sm h-100">
                    <div class="card-header bg-white">
                        <h5 class="card-title mb-0">Analysis Results</h5>
                    </div>
                    <div class="card-body">
                        <div id="sentimentResults">
                            <div class="text-center text-muted py-5">
                                <i class="fas fa-chart-bar fa-3x mb-3"></i>
                                <p>Enter text and click "Run Analysis" to see sentiment results</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="card-title mb-0">Key Phrases & Entities</h5>
                    </div>
                    <div class="card-body">
                        <div id="keyPhrases">
                            <div class="text-center text-muted py-5">
                                <p>Key phrases will appear here after analysis</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Initialize sentiment lab
    initializeSentimentLab();
}

/**
 * Initialize Sentiment Lab
 */
function initializeSentimentLab() {
    console.log('Initializing Sentiment Lab...');
    
    // Fix bug: Add sample text button for easy testing
    const sentimentInput = document.getElementById('sentimentInput');
    if (sentimentInput) {
        // Create sample text button
        const sampleButton = document.createElement('button');
        sampleButton.className = 'btn btn-sm btn-outline-secondary mt-2';
        sampleButton.innerHTML = '<i class="fas fa-file-alt me-1"></i> Load Sample Text';
        sampleButton.onclick = function() {
            sentimentInput.value = "Our AI policy framework emphasizes responsible innovation and ethical development. We believe in a balanced approach to AI governance that combines industry self-regulation with appropriate government oversight. Safety and transparency are our primary concerns, while ensuring AI systems remain beneficial and fair to all stakeholders. We commit to regular auditing and continuous improvement of our AI systems.";
        };
        
        // Add button after the textarea
        sentimentInput.parentNode.appendChild(sampleButton);
    }
}
/**
 * Run Sentiment Analysis
 */
function runSentimentAnalysis() {
    const sentimentInput = document.getElementById('sentimentInput');
    const sentimentResults = document.getElementById('sentimentResults');
    const keyPhrases = document.getElementById('keyPhrases');
    
    if (!sentimentInput || !sentimentResults || !keyPhrases) return;
    
    const text = sentimentInput.value.trim();
    
    if (!text) {
        showNotification('Please enter text for analysis', 'warning');
        return;
    }
    
    // Show loading
    showLoading(true);
    
    // Try to call the sentiment API first
    fetch(`${API_CONFIG.analytics}/sentiment/analyze`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text: text })
    })
    .then(response => {
        if (!response.ok) throw new Error('API unavailable');
        return response.json();
    })
    .then(data => {
        displaySentimentResults(data);
    })
    .catch(error => {
        console.warn('Failed to analyze sentiment via API, using sample analysis');
        
        // Generate sample sentiment analysis
        const sentiment = Math.random() > 0.6 ? 'positive' : (Math.random() > 0.5 ? 'neutral' : 'negative');
        const positive = sentiment === 'positive' ? (Math.random() * 0.4 + 0.6).toFixed(2) : (Math.random() * 0.3).toFixed(2);
        const neutral = sentiment === 'neutral' ? (Math.random() * 0.4 + 0.4).toFixed(2) : (Math.random() * 0.3).toFixed(2);
        const negative = sentiment === 'negative' ? (Math.random() * 0.4 + 0.5).toFixed(2) : (Math.random() * 0.3).toFixed(2);
        
        // Create phrases based on the input text
        const phrases = extractPhrases(text);
        
        const sampleData = {
            sentiment: sentiment,
            scores: {
                positive: positive,
                neutral: neutral,
                negative: negative
            },
            key_phrases: phrases
        };
        
        displaySentimentResults(sampleData);
    })
    .finally(() => {
        showLoading(false);
        showNotification('Sentiment analysis completed', 'success');
    });
}

/**
 * Extract sample phrases from text
 * This is a simple simulation of key phrase extraction
 */
function extractPhrases(text) {
    // Simplistic phrase extraction for the sample
    const phrases = [];
    
    // Common AI policy related terms to look for
    const policyTerms = [
        'artificial intelligence', 'machine learning', 'policy framework', 
        'ethical considerations', 'regulatory approach', 'governance model',
        'transparency requirements', 'risk assessment', 'data privacy',
        'safety measures', 'accountability', 'fairness', 'bias', 
        'responsible AI', 'oversight', 'regulation', 'compliance',
        'human-centered', 'explainable AI', 'innovation'
    ];
    
    // Look for these terms in the text
    const lowerText = text.toLowerCase();
    policyTerms.forEach(term => {
        if (lowerText.includes(term)) {
            phrases.push(term);
        }
    });
    
    // If we found less than 3 phrases, add some generic ones
    if (phrases.length < 3) {
        const genericPhrases = [
            'artificial intelligence',
            'policy framework',
            'ethical considerations',
            'regulatory approach',
            'governance model'
        ];
        
        while (phrases.length < 5 && genericPhrases.length > 0) {
            const randomIndex = Math.floor(Math.random() * genericPhrases.length);
            const phrase = genericPhrases.splice(randomIndex, 1)[0];
            if (!phrases.includes(phrase)) {
                phrases.push(phrase);
            }
        }
    }
    
    // Limit to 5 phrases
    return phrases.slice(0, 5);
}

/**
 * Display sentiment analysis results
 */
function displaySentimentResults(data) {
    const sentimentResults = document.getElementById('sentimentResults');
    const keyPhrases = document.getElementById('keyPhrases');
    
    if (!sentimentResults || !keyPhrases || !data) return;
    
    // Fix bug: Normalize data structure in case API returns different format
    const sentiment = data.sentiment || data.overall_sentiment || 'neutral';
    const scores = data.scores || data.sentiment_scores || {
        positive: 0.33,
        neutral: 0.33,
        negative: 0.34
    };
    const phrases = data.key_phrases || data.keyphrases || data.entities || [];
    
    // Display results
    sentimentResults.innerHTML = `
        <div class="text-center mb-4">
            <h3 class="h5">Overall Sentiment: <span class="badge bg-${sentiment === 'positive' ? 'success' : (sentiment === 'neutral' ? 'info' : 'danger')}">${sentiment.toUpperCase()}</span></h3>
        </div>
        
        <div class="mb-3">
            <label class="form-label d-flex justify-content-between">
                <span>Positive</span>
                <span>${scores.positive}</span>
            </label>
            <div class="progress">
                <div class="progress-bar bg-success" role="progressbar" style="width: ${scores.positive * 100}%" aria-valuenow="${scores.positive * 100}" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
        </div>
        
        <div class="mb-3">
            <label class="form-label d-flex justify-content-between">
                <span>Neutral</span>
                <span>${scores.neutral}</span>
            </label>
            <div class="progress">
                <div class="progress-bar bg-info" role="progressbar" style="width: ${scores.neutral * 100}%" aria-valuenow="${scores.neutral * 100}" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
        </div>
        
        <div class="mb-3">
            <label class="form-label d-flex justify-content-between">
                <span>Negative</span>
                <span>${scores.negative}</span>
            </label>
            <div class="progress">
                <div class="progress-bar bg-danger" role="progressbar" style="width: ${scores.negative * 100}%" aria-valuenow="${scores.negative * 100}" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
        </div>
    `;
    
    // Display key phrases
    if (phrases && phrases.length > 0) {
        keyPhrases.innerHTML = `
            <div class="d-flex flex-wrap gap-2">
                ${phrases.map(phrase => `<span class="badge bg-primary">${phrase}</span>`).join('')}
            </div>
        `;
    } else {
        keyPhrases.innerHTML = `
            <div class="text-center text-muted py-3">
                <p>No key phrases detected</p>
            </div>
        `;
    }
}

/**
 * Load Network Analysis section
 */
function loadNetworkAnalysis() {
    const networkSection = document.getElementById('network-section');
    if (!networkSection) return;
    
    networkSection.innerHTML = `
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="h4 mb-0">Network Analysis</h2>
            <button class="btn btn-sm btn-outline-primary" onclick="generateNetworkAnalysis()">
                <i class="fas fa-project-diagram me-1"></i> Generate Network
            </button>
        </div>
        
        <div class="row g-3">
            <div class="col-md-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="card-title mb-0">Organization Network</h5>
                    </div>
                    <div class="card-body">
                        <div id="networkGraph" style="height: 500px;">
                            <div class="text-center text-muted py-5">
                                <i class="fas fa-project-diagram fa-3x mb-3"></i>
                                <p>Click "Generate Network" to visualize organization relationships</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="card-title mb-0">Network Insights</h5>
                    </div>
                    <div class="card-body">
                        <div id="networkInsights">
                            <div class="text-center text-muted py-4">
                                <p>Network insights will appear here after generating the network</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Initialize network analysis
    initializeNetworkAnalysis();
}

/**
 * Initialize Network Analysis
 */
function initializeNetworkAnalysis() {
    console.log('Initializing Network Analysis...');
    // In a real implementation, would load network visualization library here
}

/**
 * Generate Network Analysis
 */
function generateNetworkAnalysis() {
    const networkGraph = document.getElementById('networkGraph');
    const networkInsights = document.getElementById('networkInsights');
    
    if (!networkGraph || !networkInsights) return;
    
    // Show loading
    showLoading(true);
    
    // Try to fetch network data from API first
    fetch(`${API_CONFIG.analytics}/network/organizations`)
        .then(response => {
            if (!response.ok) throw new Error('API unavailable');
            return response.json();
        })
        .then(data => {
            // Display network from API data
            // In a real implementation, this would use a visualization library
        })
        .catch(error => {
            console.warn('Failed to generate network via API, showing placeholder');
            
            // In a real implementation, we would load a proper network visualization
            // For now, just display a placeholder image
            networkGraph.innerHTML = `
                <div class="text-center">
                    <img src="frontend/assets/network_placeholder.svg" alt="Network Graph" class="img-fluid" style="max-height: 480px;">
                </div>
            `;
            
            // Display sample insights
            networkInsights.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>Key Communities</h6>
                        <ul class="list-group list-group-flush mb-3">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Technology Corporations
                                <span class="badge bg-primary rounded-pill">12</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Academic Institutions
                                <span class="badge bg-primary rounded-pill">8</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Government Agencies
                                <span class="badge bg-primary rounded-pill">5</span>
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Central Organizations</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                OpenAI
                                <span class="badge bg-success rounded-pill">High</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Microsoft Corporation
                                <span class="badge bg-success rounded-pill">High</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                1Day Sooner
                                <span class="badge bg-info rounded-pill">Medium</span>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-2"></i>
                    This network analysis identified 3 distinct communities and 25 significant connections between organizations.
                </div>
            `;
        })
        .finally(() => {
            // Hide loading
            showLoading(false);
            showNotification('Network analysis generated', 'success');
        });
}
/**
 * Generate placeholder network SVG
 * This creates a simple SVG visualization to use as a placeholder
 */
function generateNetworkPlaceholderSVG() {
    // This function would generate an SVG network visualization placeholder
    // In a complete implementation, this would be replaced with a real network visualization library
    
    // Check if SVG file exists, use it directly if it does
    try {
        const svgFile = 'frontend/assets/network_placeholder.svg';
        fetch(svgFile)
            .then(response => {
                if (!response.ok) throw new Error('SVG file not found');
                console.log('Using existing network placeholder SVG');
                return;
            })
            .catch(error => {
                console.warn('Network placeholder SVG not found, would generate one here');
                // In a full implementation, would generate and save SVG here
            });
    } catch (error) {
        console.error('Error checking for network SVG file:', error);
    }
}

/**
 * Load Policy Simulator section
 */
function loadPolicySimulator() {
    const policySimSection = document.getElementById('policy-simulator-section');
    if (!policySimSection) return;
    
    policySimSection.innerHTML = `
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="h4 mb-0">Policy Simulator</h2>
            <button class="btn btn-sm btn-primary" onclick="runSimulation()">
                <i class="fas fa-play-circle me-1"></i> Run Simulation
            </button>
        </div>
        
        <div class="row g-3">
            <div class="col-md-5">
                <div class="card shadow-sm h-100">
                    <div class="card-header bg-white">
                        <h5 class="card-title mb-0">Simulation Parameters</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="simPolicyType" class="form-label">Policy Type</label>
                            <select id="simPolicyType" class="form-select">
                                <option value="self-regulation">Industry Self-Regulation</option>
                                <option value="co-regulation">Co-Regulation</option>
                                <option value="government">Government Oversight</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="simScope" class="form-label">Regulatory Scope</label>
                            <select id="simScope" class="form-select">
                                <option value="narrow">Narrow (specific AI applications)</option>
                                <option value="moderate" selected>Moderate (high-risk applications)</option>
                                <option value="broad">Broad (all AI systems)</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="simEnforcement" class="form-label">Enforcement Mechanism</label>
                            <select id="simEnforcement" class="form-select">
                                <option value="voluntary">Voluntary Compliance</option>
                                <option value="audits">Regular Audits</option>
                                <option value="penalties">Penalties for Non-Compliance</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="simTimeframe" class="form-label">Implementation Timeframe</label>
                            <select id="simTimeframe" class="form-select">
                                <option value="short">Short-term (1 year)</option>
                                <option value="medium" selected>Medium-term (2-3 years)</option>
                                <option value="long">Long-term (5+ years)</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Key Priorities</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="safety" id="prioritySafety" checked>
                                <label class="form-check-label" for="prioritySafety">Safety & Security</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="transparency" id="priorityTransparency" checked>
                                <label class="form-check-label" for="priorityTransparency">Transparency & Explainability</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="fairness" id="priorityFairness">
                                <label class="form-check-label" for="priorityFairness">Fairness & Non-discrimination</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="accountability" id="priorityAccountability">
                                <label class="form-check-label" for="priorityAccountability">Accountability & Liability</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="innovation" id="priorityInnovation">
                                <label class="form-check-label" for="priorityInnovation">Innovation & Growth</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-7">
                <div class="card shadow-sm h-100">
                    <div class="card-header bg-white">
                        <h5 class="card-title mb-0">Simulation Results</h5>
                    </div>
                    <div class="card-body">
                        <div id="simulationResults">
                            <div class="text-center text-muted py-5">
                                <i class="fas fa-chart-line fa-3x mb-3"></i>
                                <p>Configure parameters and click "Run Simulation" to see results</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-12 mt-2">
                <div class="card shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="card-title mb-0">Policy Impact Insights</h5>
                    </div>
                    <div class="card-body">
                        <div id="policyInsights">
                            <div class="text-center text-muted py-4">
                                <p>Policy insights will appear here after running the simulation</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Initialize policy simulator
    initializePolicySimulator();
}

/**
 * Initialize Policy Simulator
 */
function initializePolicySimulator() {
    console.log('Initializing Policy Simulator...');
    // Add any initialization code here
    
    // Fix bug: Add event listeners for parameter changes
    const paramSelects = [
        document.getElementById('simPolicyType'),
        document.getElementById('simScope'),
        document.getElementById('simEnforcement'),
        document.getElementById('simTimeframe')
    ];
    
    paramSelects.forEach(select => {
        if (select) {
            select.addEventListener('change', function() {
                // Could provide real-time feedback as parameters change
                console.log(`Parameter changed: ${select.id} = ${select.value}`);
            });
        }
    });
}

/**
 * Run Policy Simulation
 */
function runSimulation() {
    const simulationResults = document.getElementById('simulationResults');
    const policyInsights = document.getElementById('policyInsights');
    
    if (!simulationResults || !policyInsights) return;
    
    // Get parameter values
    const policyType = document.getElementById('simPolicyType')?.value || 'self-regulation';
    const scope = document.getElementById('simScope')?.value || 'moderate';
    const enforcement = document.getElementById('simEnforcement')?.value || 'voluntary';
    const timeframe = document.getElementById('simTimeframe')?.value || 'medium';
    
    // Get priorities
    const priorities = [];
    ['safety', 'transparency', 'fairness', 'accountability', 'innovation'].forEach(priority => {
        const checkbox = document.getElementById(`priority${priority.charAt(0).toUpperCase() + priority.slice(1)}`);
        if (checkbox && checkbox.checked) {
            priorities.push(priority);
        }
    });
    
    // Show loading
    showLoading(true);
    
    // Try to call the simulation API first
    fetch(`${API_CONFIG.analytics}/policy/simulate`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            policy_type: policyType,
            scope: scope,
            enforcement: enforcement,
            timeframe: timeframe,
            priorities: priorities
        })
    })
    .then(response => {
        if (!response.ok) throw new Error('API unavailable');
        return response.json();
    })
    .then(data => {
        // Display simulation results from API
        displaySimulationResults(data);
    })
    .catch(error => {
        console.warn('Failed to run simulation via API, generating sample results');
        
        // Generate sample simulation results
        const sampleResults = generateSampleSimulationResults(
            policyType, scope, enforcement, timeframe, priorities
        );
        
        // Display sample results
        displaySimulationResults(sampleResults);
    })
    .finally(() => {
        // Hide loading
        showLoading(false);
        showNotification('Simulation completed', 'success');
    });
}

/**
 * Generate sample simulation results
 */
function generateSampleSimulationResults(policyType, scope, enforcement, timeframe, priorities) {
    // Generate different impact levels based on parameters
    const policyEffectiveness = calculatePolicyEffectiveness(policyType, scope, enforcement);
    const innovationImpact = calculateInnovationImpact(policyType, scope, enforcement);
    const complianceCost = calculateComplianceCost(scope, enforcement, timeframe);
    const publicTrust = calculatePublicTrust(policyType, scope, enforcement, priorities);
    
    // Generate organization reactions based on parameters
    const organizationReactions = generateOrganizationReactions(policyType, scope, enforcement);
    
    // Generate key insights
    const insights = generatePolicyInsights(
        policyType, scope, enforcement, timeframe, priorities,
        policyEffectiveness, innovationImpact, complianceCost, publicTrust
    );
    
    return {
        policy_effectiveness: policyEffectiveness,
        innovation_impact: innovationImpact,
        compliance_cost: complianceCost,
        public_trust: publicTrust,
        organization_reactions: organizationReactions,
        insights: insights
    };
}

/**
 * Calculate policy effectiveness
 */
function calculatePolicyEffectiveness(policyType, scope, enforcement) {
    let score = 50; // Start with a mid-range score
    
    // Policy type impact
    switch (policyType) {
        case 'government':
            score += 20;
            break;
        case 'co-regulation':
            score += 10;
            break;
        case 'self-regulation':
            score -= 10;
            break;
    }
    
    // Scope impact
    switch (scope) {
        case 'broad':
            score += 15;
            break;
        case 'moderate':
            score += 5;
            break;
        case 'narrow':
            score -= 5;
            break;
    }
    
    // Enforcement impact
    switch (enforcement) {
        case 'penalties':
            score += 20;
            break;
        case 'audits':
            score += 10;
            break;
        case 'voluntary':
            score -= 15;
            break;
    }
    
    // Keep score within 0-100 range
    return Math.min(Math.max(score, 0), 100);
}

/**
 * Calculate innovation impact
 */
function calculateInnovationImpact(policyType, scope, enforcement) {
    let score = 50; // Start with a mid-range score
    
    // Policy type impact (inverse relationship with restrictiveness)
    switch (policyType) {
        case 'government':
            score -= 20;
            break;
        case 'co-regulation':
            score -= 5;
            break;
        case 'self-regulation':
            score += 15;
            break;
    }
    
    // Scope impact (inverse relationship with broadness)
    switch (scope) {
        case 'broad':
            score -= 15;
            break;
        case 'moderate':
            score -= 5;
            break;
        case 'narrow':
            score += 10;
            break;
    }
    
    // Enforcement impact (inverse relationship with strictness)
    switch (enforcement) {
        case 'penalties':
            score -= 15;
            break;
        case 'audits':
            score -= 5;
            break;
        case 'voluntary':
            score += 15;
            break;
    }
    
    // Keep score within 0-100 range
    return Math.min(Math.max(score, 0), 100);
}

/**
 * Calculate compliance cost
 */
function calculateComplianceCost(scope, enforcement, timeframe) {
    let score = 50; // Start with a mid-range score
    
    // Scope impact (direct relationship with broadness)
    switch (scope) {
        case 'broad':
            score += 20;
            break;
        case 'moderate':
            score += 10;
            break;
        case 'narrow':
            score -= 10;
            break;
    }
    
    // Enforcement impact (direct relationship with strictness)
    switch (enforcement) {
        case 'penalties':
            score += 20;
            break;
        case 'audits':
            score += 10;
            break;
        case 'voluntary':
            score -= 15;
            break;
    }
    
    // Timeframe impact (inverse relationship with implementation time)
    switch (timeframe) {
        case 'short':
            score += 15;
            break;
        case 'medium':
            score += 0;
            break;
        case 'long':
            score -= 15;
            break;
    }
    
    // Keep score within 0-100 range
    return Math.min(Math.max(score, 0), 100);
}

/**
 * Calculate public trust
 */
function calculatePublicTrust(policyType, scope, enforcement, priorities) {
    let score = 50; // Start with a mid-range score
    
    // Policy type impact
    switch (policyType) {
        case 'government':
            score += 15;
            break;
        case 'co-regulation':
            score += 5;
            break;
        case 'self-regulation':
            score -= 10;
            break;
    }
    
    // Scope impact
    switch (scope) {
        case 'broad':
            score += 10;
            break;
        case 'moderate':
            score += 5;
            break;
        case 'narrow':
            score -= 5;
            break;
    }
    
    // Enforcement impact
    switch (enforcement) {
        case 'penalties':
            score += 15;
            break;
        case 'audits':
            score += 5;
            break;
        case 'voluntary':
            score -= 10;
            break;
    }
    
    // Priorities impact
    if (priorities.includes('safety')) score += 10;
    if (priorities.includes('transparency')) score += 10;
    if (priorities.includes('fairness')) score += 5;
    if (priorities.includes('accountability')) score += 10;
    if (priorities.includes('innovation')) score -= 5;
    
    // Normalize based on number of priorities
    const prioritiesScore = priorities.length > 0 ? score * (3 / priorities.length) : score;
    
    // Keep score within 0-100 range
    return Math.min(Math.max(prioritiesScore, 0), 100);
}

/**
 * Generate organization reactions
 */
function generateOrganizationReactions(policyType, scope, enforcement) {
    const reactions = {
        corporate: policyType === 'self-regulation' ? 'Supportive' : (policyType === 'co-regulation' ? 'Neutral' : 'Resistant'),
        academic: policyType === 'government' ? 'Supportive' : (scope === 'narrow' ? 'Concerned' : 'Neutral'),
        nonprofit: priorities => priorities.includes('safety') && priorities.includes('accountability') ? 'Supportive' : 'Mixed',
        government: policyType === 'government' ? 'Supportive' : 'Concerned'
    };
    
    return reactions;
}

/**
 * Generate policy insights
 */
function generatePolicyInsights(
    policyType, scope, enforcement, timeframe, priorities,
    policyEffectiveness, innovationImpact, complianceCost, publicTrust
) {
    const insights = [];
    
    // Effectiveness insight
    if (policyEffectiveness > 75) {
        insights.push('The policy framework is likely to be highly effective in achieving regulatory goals.');
    } else if (policyEffectiveness < 40) {
        insights.push('The policy framework may struggle to achieve its intended regulatory goals.');
    }
    
    // Innovation insight
    if (innovationImpact < 40) {
        insights.push('This approach could significantly hamper innovation and technological development.');
    } else if (innovationImpact > 75) {
        insights.push('This framework provides a favorable environment for continued innovation.');
    }
    
    // Cost insight
    if (complianceCost > 75) {
        insights.push('Implementation costs are likely to be substantial, especially for smaller organizations.');
    } else if (complianceCost < 40) {
        insights.push('Compliance costs are projected to be manageable across the industry.');
    }
    
    // Trust insight
    if (publicTrust > 75) {
        insights.push('This approach could significantly boost public trust in AI systems.');
    } else if (publicTrust < 40) {
        insights.push('Public trust may remain low under this regulatory framework.');
    }
    
    // Timeframe insight
    if (timeframe === 'short' && complianceCost > 60) {
        insights.push('The short implementation timeframe may create compliance challenges.');
    } else if (timeframe === 'long' && policyEffectiveness > 60) {
        insights.push('While effective, the long implementation timeline may delay important protections.');
    }
    
    // Add a balanced insight
    insights.push(`A ${policyType} approach with ${enforcement} enforcement represents a ${policyEffectiveness > 60 ? 'strong' : 'moderate'} policy framework with ${innovationImpact > 60 ? 'favorable' : 'some'} implications for innovation.`);
    
    return insights;
}
/**
 * Display simulation results
 */
function displaySimulationResults(data) {
    const simulationResults = document.getElementById('simulationResults');
    const policyInsights = document.getElementById('policyInsights');
    
    if (!simulationResults || !policyInsights) return;
    
    // Display impact metrics
    simulationResults.innerHTML = `
        <div class="mb-4">
            <h6 class="mb-3">Impact Assessment</h6>
            <div class="row g-3">
                <div class="col-md-6">
                    <label class="form-label d-flex justify-content-between">
                        <span>Policy Effectiveness</span>
                        <span>${data.policy_effectiveness}%</span>
                    </label>
                    <div class="progress mb-3">
                        <div class="progress-bar bg-primary" role="progressbar" style="width: ${data.policy_effectiveness}%" 
                            aria-valuenow="${data.policy_effectiveness}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    
                    <label class="form-label d-flex justify-content-between">
                        <span>Innovation Impact</span>
                        <span>${data.innovation_impact}%</span>
                    </label>
                    <div class="progress">
                        <div class="progress-bar bg-info" role="progressbar" style="width: ${data.innovation_impact}%" 
                            aria-valuenow="${data.innovation_impact}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <label class="form-label d-flex justify-content-between">
                        <span>Compliance Cost</span>
                        <span>${data.compliance_cost}%</span>
                    </label>
                    <div class="progress mb-3">
                        <div class="progress-bar bg-warning" role="progressbar" style="width: ${data.compliance_cost}%" 
                            aria-valuenow="${data.compliance_cost}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    
                    <label class="form-label d-flex justify-content-between">
                        <span>Public Trust</span>
                        <span>${data.public_trust}%</span>
                    </label>
                    <div class="progress">
                        <div class="progress-bar bg-success" role="progressbar" style="width: ${data.public_trust}%" 
                            aria-valuenow="${data.public_trust}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mb-3">
            <h6 class="mb-2">Expected Organization Reactions</h6>
            <div class="row g-2">
                <div class="col-md-6">
                    <div class="card bg-light mb-2">
                        <div class="card-body py-2 px-3">
                            <small class="text-muted">Corporate</small>
                            <p class="mb-0"><strong>${data.organization_reactions?.corporate || 'Mixed'}</strong></p>
                        </div>
                    </div>
                    <div class="card bg-light">
                        <div class="card-body py-2 px-3">
                            <small class="text-muted">Academic</small>
                            <p class="mb-0"><strong>${data.organization_reactions?.academic || 'Neutral'}</strong></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card bg-light mb-2">
                        <div class="card-body py-2 px-3">
                            <small class="text-muted">Nonprofit</small>
                            <p class="mb-0"><strong>${data.organization_reactions?.nonprofit || 'Supportive'}</strong></p>
                        </div>
                    </div>
                    <div class="card bg-light">
                        <div class="card-body py-2 px-3">
                            <small class="text-muted">Government</small>
                            <p class="mb-0"><strong>${data.organization_reactions?.government || 'Concerned'}</strong></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Display insights
    if (data.insights && data.insights.length > 0) {
        let insightsHTML = '<div class="list-group">';
        
        data.insights.forEach(insight => {
            insightsHTML += `
                <div class="list-group-item">
                    <div class="d-flex w-100 align-items-center">
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        <p class="mb-0">${insight}</p>
                    </div>
                </div>
            `;
        });
        
        insightsHTML += '</div>';
        policyInsights.innerHTML = insightsHTML;
    } else {
        policyInsights.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                No specific insights were generated for this policy configuration.
            </div>
        `;
    }
}

/**
 * Load Historical Data section
 */
function loadHistoricalData() {
    const historySection = document.getElementById('historical-section');
    if (!historySection) return;
    
    historySection.innerHTML = `
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="h4 mb-0">Historical Data</h2>
            <div>
                <button class="btn btn-sm btn-outline-primary me-2" onclick="exportHistoricalData()">
                    <i class="fas fa-download me-1"></i> Export CSV
                </button>
                <button class="btn btn-sm btn-outline-secondary" onclick="refreshHistoricalData()">
                    <i class="fas fa-sync-alt me-1"></i> Refresh
                </button>
            </div>
        </div>
        
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Historical Analysis Records</h5>
                    <button class="btn btn-sm btn-link p-0" type="button" data-bs-toggle="collapse" data-bs-target="#historyFilters">
                        <i class="fas fa-filter"></i> Filters
                    </button>
                </div>
            </div>
            
            <div class="collapse" id="historyFilters">
                <div class="card-body border-bottom">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">Date Range</label>
                            <select id="historyDateRange" class="form-select">
                                <option value="7">Last 7 Days</option>
                                <option value="30" selected>Last 30 Days</option>
                                <option value="90">Last 90 Days</option>
                                <option value="365">Last Year</option>
                                <option value="all">All Time</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Organization Type</label>
                            <select id="historyOrgType" class="form-select">
                                <option value="">All Types</option>
                                <option value="Corporate">Corporate</option>
                                <option value="Academic">Academic</option>
                                <option value="Nonprofit">Nonprofit</option>
                                <option value="Government">Government</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Document Type</label>
                            <select id="historyDocType" class="form-select">
                                <option value="">All Documents</option>
                                <option value="Policy">Policy Statements</option>
                                <option value="Guidelines">Guidelines</option>
                                <option value="Paper">Position Papers</option>
                                <option value="Response">RFI Responses</option>
                            </select>
                        </div>
                        <div class="col-12 text-end">
                            <button class="btn btn-sm btn-primary" onclick="applyHistoricalFilters()">
                                <i class="fas fa-check me-1"></i> Apply Filters
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card-body">
                <div class="table-responsive">
                    <table id="historicalDataTable" class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th onclick="sortHistoricalData('date')">Date <i class="fas fa-sort ms-1"></i></th>
                                <th onclick="sortHistoricalData('organization')">Organization <i class="fas fa-sort ms-1"></i></th>
                                <th onclick="sortHistoricalData('type')">Type <i class="fas fa-sort ms-1"></i></th>
                                <th onclick="sortHistoricalData('document')">Document <i class="fas fa-sort ms-1"></i></th>
                                <th onclick="sortHistoricalData('sentiment')">Sentiment <i class="fas fa-sort ms-1"></i></th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="historicalDataBody">
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p class="mt-2 mb-0">Loading historical data...</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div id="dataInfo" class="text-muted small">Showing 0 records</div>
                    <nav aria-label="Historical data navigation">
                        <ul id="historyPagination" class="pagination pagination-sm mb-0">
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1">Previous</a>
                            </li>
                            <li class="page-item active"><a class="page-link" href="#">1</a></li>
                            <li class="page-item disabled">
                                <a class="page-link" href="#">Next</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    `;
    
    // Initialize historical data
    initializeHistoricalData();
}

/**
 * Initialize Historical Data
 */
function initializeHistoricalData() {
    console.log('Initializing Historical Data...');
    
    // Fix bug: Add toggle for filter panel
    const filterToggle = document.querySelector('[data-bs-toggle="collapse"][data-bs-target="#historyFilters"]');
    if (filterToggle) {
        filterToggle.addEventListener('click', function() {
            const icon = this.querySelector('i');
            if (icon) {
                if (icon.classList.contains('fa-filter')) {
                    icon.classList.replace('fa-filter', 'fa-times');
                } else {
                    icon.classList.replace('fa-times', 'fa-filter');
                }
            }
        });
    }
    
    // Load historical data
    loadHistoricalData();
}

/**
 * Load historical data from API or generate sample data
 */
async function loadHistoricalData(filters = {}) {
    const historicalDataBody = document.getElementById('historicalDataBody');
    const dataInfo = document.getElementById('dataInfo');
    const historyPagination = document.getElementById('historyPagination');
    
    if (!historicalDataBody || !dataInfo || !historyPagination) return;
    
    try {
        let historicalData;
        
        // Build query string from filters
        const queryParams = new URLSearchParams();
        
        if (filters.dateRange) queryParams.append('date_range', filters.dateRange);
        if (filters.orgType) queryParams.append('organization_type', filters.orgType);
        if (filters.docType) queryParams.append('document_type', filters.docType);
        if (filters.page) queryParams.append('page', filters.page);
        if (filters.sort) queryParams.append('sort', filters.sort);
        if (filters.sortDir) queryParams.append('sort_direction', filters.sortDir);
        
        try {
            // Try to fetch from API
            const response = await fetch(`${API_CONFIG.history}/historical?${queryParams.toString()}`);
            
            if (!response.ok) throw new Error('API unavailable');
            
            const data = await response.json();
            historicalData = data;
        } catch (error) {
            console.warn('Failed to fetch historical data, using sample values');
            
            // Generate sample data
            const sampleData = generateSampleHistoricalData();
            
            // Apply filters to sample data if provided
            let filteredData = [...sampleData];
            
            if (filters.orgType) {
                filteredData = filteredData.filter(item => 
                    item.organization_type.toLowerCase().includes(filters.orgType.toLowerCase())
                );
            }
            
            if (filters.docType) {
                filteredData = filteredData.filter(item => 
                    item.document_type.toLowerCase().includes(filters.docType.toLowerCase())
                );
            }
            
            // Apply sorting
            if (filters.sort) {
                filteredData.sort((a, b) => {
                    let valA, valB;
                    
                    switch (filters.sort) {
                        case 'date':
                            valA = new Date(a.date);
                            valB = new Date(b.date);
                            break;
                        case 'organization':
                            valA = a.organization_name;
                            valB = b.organization_name;
                            break;
                        case 'type':
                            valA = a.organization_type;
                            valB = b.organization_type;
                            break;
                        case 'document':
                            valA = a.document_type;
                            valB = b.document_type;
                            break;
                        case 'sentiment':
                            valA = a.sentiment;
                            valB = b.sentiment;
                            break;
                        default:
                            valA = new Date(a.date);
                            valB = new Date(b.date);
                    }
                    
                    // Determine sort direction
                    const direction = filters.sortDir === 'asc' ? 1 : -1;
                    
                    if (valA < valB) return -1 * direction;
                    if (valA > valB) return 1 * direction;
                    return 0;
                });
            }
            
            // Simulate pagination
            const itemsPerPage = 10;
            const page = filters.page || 1;
            const startIndex = (page - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            
            const paginatedData = filteredData.slice(startIndex, endIndex);
            const totalPages = Math.ceil(filteredData.length / itemsPerPage);
            
            historicalData = {
                data: paginatedData,
                total: filteredData.length,
                page: page,
                total_pages: totalPages,
                items_per_page: itemsPerPage
            };
        }
        
        // Render the data
        displayHistoricalData(historicalData);
        
    } catch (error) {
        console.error('Error loading historical data:', error);
        
        historicalDataBody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center py-3 text-danger">
                    <i class="fas fa-exclamation-circle fa-lg mb-2"></i>
                    <p class="mb-0">Error loading data. Please try again later.</p>
                </td>
            </tr>
        `;
        
        dataInfo.textContent = 'Error loading data';
    }
}

/**
 * Generate sample historical data
 */
function generateSampleHistoricalData() {
    const orgNames = [
        'Google LLC', 'OpenAI', 'Microsoft Corporation', '1Day Sooner',
        'Stanford University', 'Partnership on AI', 'DeepMind', 'IBM Research',
        'MIT CSAIL', 'Electronic Frontier Foundation', 'AI Now Institute', 'Facebook AI',
        'Future of Life Institute', 'Oxford University', 'NIST', 'Allen Institute for AI'
    ];
    
    const orgTypes = ['Corporate', 'Academic', 'Nonprofit', 'Government'];
    
    const docTypes = [
        'Policy Statement', 'Guidelines', 'Position Paper', 'Research Report',
        'Framework Document', 'RFI Response', 'Ethics Principles', 'Recommendations'
    ];
    
    const sentiments = ['Positive', 'Neutral', 'Negative'];
    
    const sampleData = [];
    
    // Generate 50 sample records
    for (let i = 0; i < 50; i++) {
        // Create date between now and 365 days ago
        const date = new Date();
        date.setDate(date.getDate() - Math.floor(Math.random() * 365));
        
        const orgIndex = Math.floor(Math.random() * orgNames.length);
        const orgName = orgNames[orgIndex];
        
        // Assign org type based on name
        let orgType;
        if (orgName.includes('University') || orgName.includes('Institute') || orgName.includes('MIT') || orgName.includes('Stanford') || orgName.includes('Oxford')) {
            orgType = 'Academic';
        } else if (orgName.includes('Partnership') || orgName.includes('Foundation') || orgName.includes('1Day Sooner') || orgName.includes('EFF')) {
            orgType = 'Nonprofit';
        } else if (orgName.includes('NIST')) {
            orgType = 'Government';
        } else {
            orgType = 'Corporate';
        }
        
        const docType = docTypes[Math.floor(Math.random() * docTypes.length)];
        const sentiment = sentiments[Math.floor(Math.random() * sentiments.length)];
        
        sampleData.push({
            id: `doc-${i+1}`,
            date: date.toISOString(),
            organization_name: orgName,
            organization_type: orgType,
            document_type: docType,
            sentiment: sentiment
        });
    }
    
    // Sort by date descending (newest first)
    sampleData.sort((a, b) => new Date(b.date) - new Date(a.date));
    
    return sampleData;
}

/**
 * Display historical data
 */
function displayHistoricalData(data) {
    const historicalDataBody = document.getElementById('historicalDataBody');
    const dataInfo = document.getElementById('dataInfo');
    const historyPagination = document.getElementById('historyPagination');
    
    if (!historicalDataBody || !dataInfo || !historyPagination) return;
    
    // Store data in dashboard state for pagination/sorting
    dashboardState.historicalData = data;
    
    const items = data.data || [];
    
    if (items.length === 0) {
        historicalDataBody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center py-3">
                    <i class="fas fa-search fa-lg mb-2"></i>
                    <p class="mb-0">No matching records found</p>
                </td>
            </tr>
        `;
        
        dataInfo.textContent = 'No records found';
        historyPagination.innerHTML = '';
        return;
    }
    
    // Render table rows
    let tableContent = '';
    
    items.forEach(item => {
        const formattedDate = formatDate(new Date(item.date));
        const sentimentBadgeColor = item.sentiment === 'Positive' ? 'success' : (item.sentiment === 'Negative' ? 'danger' : 'info');
        
        tableContent += `
            <tr>
                <td>${formattedDate}</td>
                <td><strong>${item.organization_name}</strong></td>
                <td><span class="badge bg-secondary">${item.organization_type}</span></td>
                <td>${item.document_type}</td>
                <td><span class="badge bg-${sentimentBadgeColor}">${item.sentiment}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewHistoricalItem('${item.id}')">
                        <i class="fas fa-eye me-1"></i> View
                    </button>
                </td>
            </tr>
        `;
    });
    
    historicalDataBody.innerHTML = tableContent;
    
    // Update data info
    const startItem = ((data.page - 1) * data.items_per_page) + 1;
    const endItem = Math.min(startItem + items.length - 1, data.total);
    dataInfo.textContent = `Showing ${startItem}-${endItem} of ${data.total} records`;
    
    // Create pagination
    createHistoricalPagination(data.page, data.total_pages);
}
/**
 * Create pagination for historical data
 */
function createHistoricalPagination(currentPage, totalPages) {
    const historyPagination = document.getElementById('historyPagination');
    if (!historyPagination) return;
    
    let paginationHTML = '';
    
    // Previous button
    paginationHTML += `
        <li class="page-item ${currentPage <= 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1}); return false;" tabindex="-1">Previous</a>
        </li>
    `;
    
    // Page numbers
    // Show up to 5 page numbers, centered around current page if possible
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, startPage + 4);
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="changePage(${i}); return false;">${i}</a>
            </li>
        `;
    }
    
    // Next button
    paginationHTML += `
        <li class="page-item ${currentPage >= totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1}); return false;">Next</a>
        </li>
    `;
    
    historyPagination.innerHTML = paginationHTML;
}

/**
 * Change page in historical data
 */
function changePage(page) {
    // Get current filters
    const dateRange = document.getElementById('historyDateRange')?.value || '30';
    const orgType = document.getElementById('historyOrgType')?.value || '';
    const docType = document.getElementById('historyDocType')?.value || '';
    
    // Update with new page
    loadHistoricalData({
        dateRange: dateRange,
        orgType: orgType,
        docType: docType,
        page: page,
        sort: dashboardState.historicalSort,
        sortDir: dashboardState.historicalSortDir
    });
}

/**
 * Apply filters to historical data
 */
function applyHistoricalFilters() {
    const dateRange = document.getElementById('historyDateRange')?.value || '30';
    const orgType = document.getElementById('historyOrgType')?.value || '';
    const docType = document.getElementById('historyDocType')?.value || '';
    
    // Reset to page 1 when applying filters
    loadHistoricalData({
        dateRange: dateRange,
        orgType: orgType,
        docType: docType,
        page: 1,
        sort: dashboardState.historicalSort,
        sortDir: dashboardState.historicalSortDir
    });
}

/**
 * Sort historical data
 */
function sortHistoricalData(column) {
    // Toggle sort direction if same column is clicked again
    if (dashboardState.historicalSort === column) {
        dashboardState.historicalSortDir = dashboardState.historicalSortDir === 'asc' ? 'desc' : 'asc';
    } else {
        dashboardState.historicalSort = column;
        dashboardState.historicalSortDir = 'desc'; // Default to descending for first click
    }
    
    // Get current filters
    const dateRange = document.getElementById('historyDateRange')?.value || '30';
    const orgType = document.getElementById('historyOrgType')?.value || '';
    const docType = document.getElementById('historyDocType')?.value || '';
    const page = dashboardState.historicalData?.page || 1;
    
    // Update with sort
    loadHistoricalData({
        dateRange: dateRange,
        orgType: orgType,
        docType: docType,
        page: page,
        sort: dashboardState.historicalSort,
        sortDir: dashboardState.historicalSortDir
    });
}

/**
 * Refresh historical data
 */
function refreshHistoricalData() {
    // Get current filters
    const dateRange = document.getElementById('historyDateRange')?.value || '30';
    const orgType = document.getElementById('historyOrgType')?.value || '';
    const docType = document.getElementById('historyDocType')?.value || '';
    
    // Show notification
    showNotification('Refreshing historical data...', 'info');
    
    // Reset to page 1 when refreshing
    loadHistoricalData({
        dateRange: dateRange,
        orgType: orgType,
        docType: docType,
        page: 1,
        sort: dashboardState.historicalSort,
        sortDir: dashboardState.historicalSortDir
    });
}

/**
 * Export historical data to CSV
 */
function exportHistoricalData() {
    try {
        // Get current data
        const items = dashboardState.historicalData?.data || [];
        
        if (items.length === 0) {
            showNotification('No data to export', 'warning');
            return;
        }
        
        // Create CSV header row
        let csv = 'Date,Organization,Type,Document,Sentiment\n';
        
        // Add data rows
        items.forEach(item => {
            const formattedDate = formatDate(new Date(item.date));
            const row = [
                formattedDate,
                item.organization_name,
                item.organization_type,
                item.document_type,
                item.sentiment
            ].map(cell => `"${cell}"`).join(',');
            
            csv += row + '\n';
        });
        
        // Create download link
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.setAttribute('href', url);
        link.setAttribute('download', 'historical_data.csv');
        link.style.visibility = 'hidden';
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        showNotification('Data exported to CSV', 'success');
    } catch (error) {
        console.error('Error exporting data:', error);
        showNotification('Failed to export data', 'danger');
    }
}

/**
 * View historical item
 */
function viewHistoricalItem(itemId) {
    console.log(`Viewing historical item: ${itemId}`);
    
    // Show loading
    showLoading(true);
    
    // Try to fetch item data from API
    fetch(`${API_CONFIG.history}/item/${itemId}`)
        .then(response => {
            if (!response.ok) throw new Error('API unavailable');
            return response.json();
        })
        .then(data => {
            // Display item from API
            displayHistoricalItemView(data);
        })
        .catch(error => {
            console.warn(`Failed to fetch historical item ${itemId}, using sample data`);
            
            // Generate sample analysis data as fallback
            const sampleItem = {
                id: itemId,
                date: new Date().toISOString(),
                organization_name: itemId.includes('_') ? 
                    itemId.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ') : 
                    `Organization ${itemId.substring(itemId.length - 3)}`,
                organization_type: ['Corporate', 'Academic', 'Nonprofit', 'Government'][Math.floor(Math.random() * 4)],
                document_type: ['Policy Statement', 'Guidelines', 'Position Paper', 'RFI Response'][Math.floor(Math.random() * 4)],
                sentiment: ['Positive', 'Neutral', 'Negative'][Math.floor(Math.random() * 3)],
                analysis: {
                    sentiment_scores: {
                        positive: (Math.random() * 0.6 + 0.3).toFixed(2),
                        neutral: (Math.random() * 0.3).toFixed(2),
                        negative: (Math.random() * 0.3).toFixed(2)
                    },
                    policy_stance: {
                        self_regulation: (Math.random() * 0.6).toFixed(2),
                        co_regulation: (Math.random() * 0.5).toFixed(2),
                        government_oversight: (Math.random() * 0.4).toFixed(2)
                    },
                    key_phrases: [
                        'artificial intelligence',
                        'policy framework',
                        'regulatory approach',
                        'ethics guidelines',
                        'safety measures'
                    ],
                    summary: `This sample document from ${itemId.includes('_') ? 
                        itemId.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ') : 
                        `Organization ${itemId.substring(itemId.length - 3)}`} outlines a comprehensive approach to AI governance, emphasizing the importance of balancing innovation with appropriate safeguards. The document highlights key priorities including safety, transparency, and accountability.`
                }
            };
            
            // Display sample item
            displayHistoricalItemView(sampleItem);
        })
        .finally(() => {
            // Hide loading
            showLoading(false);
        });
}

/**
 * Display historical item view
 */
function displayHistoricalItemView(item) {
    // First navigate to analysis section
    navigateToSection('analysis');
    
    const analysisResultsContainer = document.getElementById('analysis-results');
    if (!analysisResultsContainer) return;
    
    const sentimentBadgeColor = item.sentiment === 'Positive' ? 'success' : (item.sentiment === 'Negative' ? 'danger' : 'info');
    const formattedDate = formatDate(new Date(item.date));
    
    // Prepare analysis content
    let analysisContent = `
        <div class="d-flex justify-content-between align-items-start mb-4">
            <div>
                <h2 class="h4 mb-1">${item.organization_name}</h2>
                <div class="text-muted">
                    <span class="badge bg-secondary me-2">${item.organization_type}</span>
                    <span>${item.document_type}</span>
                    <span class="mx-2">�?/span>
                    <span>${formattedDate}</span>
                </div>
            </div>
            <span class="badge bg-${sentimentBadgeColor} fs-6">${item.sentiment}</span>
        </div>
    `;
    
    // Add summary if available
    if (item.analysis && item.analysis.summary) {
        analysisContent += `
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">Summary</h5>
                </div>
                <div class="card-body">
                    <p class="mb-0">${item.analysis.summary}</p>
                </div>
            </div>
        `;
    }
    
    // Add sentiment analysis if available
    if (item.analysis && item.analysis.sentiment_scores) {
        const scores = item.analysis.sentiment_scores;
        
        analysisContent += `
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">Sentiment Analysis</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label d-flex justify-content-between">
                            <span>Positive</span>
                            <span>${scores.positive}</span>
                        </label>
                        <div class="progress">
                            <div class="progress-bar bg-success" role="progressbar" style="width: ${scores.positive * 100}%" 
                                aria-valuenow="${scores.positive * 100}" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label d-flex justify-content-between">
                            <span>Neutral</span>
                            <span>${scores.neutral}</span>
                        </label>
                        <div class="progress">
                            <div class="progress-bar bg-info" role="progressbar" style="width: ${scores.neutral * 100}%" 
                                aria-valuenow="${scores.neutral * 100}" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                    
                    <div class="mb-0">
                        <label class="form-label d-flex justify-content-between">
                            <span>Negative</span>
                            <span>${scores.negative}</span>
                        </label>
                        <div class="progress">
                            <div class="progress-bar bg-danger" role="progressbar" style="width: ${scores.negative * 100}%" 
                                aria-valuenow="${scores.negative * 100}" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    // Add policy analysis if available
    if (item.analysis && item.analysis.policy_stance) {
        const stance = item.analysis.policy_stance;
        
        analysisContent += `
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">Policy Analysis</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label d-flex justify-content-between">
                            <span>Self-Regulation</span>
                            <span>${stance.self_regulation}</span>
                        </label>
                        <div class="progress">
                            <div class="progress-bar bg-primary" role="progressbar" style="width: ${stance.self_regulation * 100}%" 
                                aria-valuenow="${stance.self_regulation * 100}" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label d-flex justify-content-between">
                            <span>Co-Regulation</span>
                            <span>${stance.co_regulation}</span>
                        </label>
                        <div class="progress">
                            <div class="progress-bar bg-info" role="progressbar" style="width: ${stance.co_regulation * 100}%" 
                                aria-valuenow="${stance.co_regulation * 100}" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                    
                    <div class="mb-0">
                        <label class="form-label d-flex justify-content-between">
                            <span>Government Oversight</span>
                            <span>${stance.government_oversight}</span>
                        </label>
                        <div class="progress">
                            <div class="progress-bar bg-secondary" role="progressbar" style="width: ${stance.government_oversight * 100}%" 
                                aria-valuenow="${stance.government_oversight * 100}" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    // Add key phrases if available
    if (item.analysis && item.analysis.key_phrases && item.analysis.key_phrases.length > 0) {
        analysisContent += `
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">Key Phrases</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex flex-wrap gap-2">
                        ${item.analysis.key_phrases.map(phrase => `<span class="badge bg-primary">${phrase}</span>`).join('')}
                    </div>
                </div>
            </div>
        `;
    }
    
    // Add export button
    analysisContent += `
        <div class="text-end mt-4">
            <button class="btn btn-outline-primary" onclick="exportAnalysis('${item.id}')">
                <i class="fas fa-file-export me-1"></i> Export Analysis
            </button>
        </div>
    `;
    
    // Display in the analysis section
    analysisResultsContainer.innerHTML = analysisContent;
}

/**
 * Export analysis
 */
function exportAnalysis(itemId) {
    console.log(`Exporting analysis for item: ${itemId}`);
    showNotification('Analysis export is not implemented yet', 'info');
    // This would be implemented in a full version to export analysis to PDF or other formats
}

/**
 * Display default analysis section
 */
function displayDefaultAnalysisSection() {
    const analysisResultsContainer = document.getElementById('analysis-results');
    if (!analysisResultsContainer) return;
    
    analysisResultsContainer.innerHTML = `
        <div class="text-center text-muted py-5">
            <i class="fas fa-chart-bar fa-3x mb-3"></i>
            <h3 class="h4 mb-3">Analysis View</h3>
            <p class="mb-4">Select an item from Search or Historical Data to view detailed analysis</p>
            <button class="btn btn-primary" onclick="navigateToSection('search')">
                <i class="fas fa-search me-1"></i> Go to Search
            </button>
        </div>
    `;
}

/**
 * Initialize dashboard
 */
function initializeDashboard() {
    console.log('Initializing dashboard...');
    
    // Fix bug: Generate the network placeholder SVG if it's needed
    generateNetworkPlaceholderSVG();
    
    // Setup sidebar toggler
    setupSidebar();
    
    // Setup section navigation
    setupNavigation();
    
    // Setup search functionality
    setupSearch();
    
    // Setup file upload
    setupFileUpload();
    
    // Load initial dashboard data
    loadDashboardData();
    
    // Display default analysis section
    displayDefaultAnalysisSection();
}

// Initialize dashboard when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Check if all required elements and scripts are loaded
    const requiredElements = [
        document.getElementById('dashboard-section'),
        document.getElementById('total-documents'),
        document.getElementById('sidebarToggle'),
        document.querySelector('.sidebar'),
        document.querySelector('.main-content')
    ];
    
    const missingElements = requiredElements.filter(el => !el);
    if (missingElements.length > 0) {
        console.error('Some required elements are missing. Dashboard initialization may fail.');
        console.error('Missing elements:', missingElements.map(el => el ? 'found' : 'missing'));
    } else {
        initializeDashboard();
    }
});
