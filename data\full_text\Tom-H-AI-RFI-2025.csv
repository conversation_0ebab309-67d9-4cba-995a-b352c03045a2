﻿content_type,page_number,content,character_count,word_count,metadata_field,metadata_value
document_info,0,Document: Tom-H-AI-RFI-2025.pdf,0,0,filename,Tom-H-AI-RFI-2025.pdf
metadata,0,"Comments Received in Response To: Request for Information on the Development of an Artificial Intelligence (AI) Action Plan (""Plan"")",132,19,title,"Comments Received in Response To: Request for Information on the Development of an Artificial Intelligence (AI) Action Plan (""Plan"")"
metadata,0,Adobe Acrobat Pro (64-bit) 24.5.20399,37,5,creator,Adobe Acrobat Pro (64-bit) 24.5.20399
metadata,0,Adobe Acrobat Pro (64-bit) 24.5.20399,37,5,producer,Adobe Acrobat Pro (64-bit) 24.5.20399
metadata,0,D:20250415130459-04'00',23,1,creation_date,D:20250415130459-04'00'
metadata,0,D:20250415130459-04'00',23,1,modification_date,D:20250415130459-04'00'
document_stats,0,"Total pages: 1, Total characters: 2536, Total words: 392",2536,392,document_stats,"pages:1,chars:2536,words:392"
full_text,0,"Tom H Thank you for the opportunity to comment on the development of an Artificial Intelligence (AI) Action Plan. The rapid evolution of AI technology demands that we ensure advancements do not compromise user privacy or the protection of intellectual property ( IP) rights. The rate at which AI technologies are advancing in capability is too significant to gate from use in any contexts. The lack of control over flow of information when using an AI tool not only jeopardizes user privacy and IP but also severely res tricts critical research opportunities especially in clinical settings where patient data is involved. As it stands, the amount of information that the AI tools and/or their company extracts from user input is not transparent whatsoever. The current lack o f transparency regarding the data extracted from user inputs is deeply concerning. AI tools should include a verifiable private mode that prevents data from being transmitted, stored, or used for training by the company or any other entities unless the u ser explicitly opts in. This option must be easily accessible and provide complete control to the user, ensuring that both individuals and organizations can safeguard their sensitive information. Moreover, complete legal assurance is essential for the hand ling of personal, proprietary, and clinical data. Without robust privacy controls, there is a significant risk that confidential information may be exposed to unauthorized parties. In the case of clinical data, for instance, the absence of clear safeguards prevents researchers and clinicians from confidently using AI tools. Compliance with HIPAA guidelines is crucial here; any AI tool handling patient information must guarantee that such data is fully protected and not compromised in any way. Aligning these privacy measures with existing legal frameworks - such as GDPR and HIPAA - would provide a strong legal and ethical foundation for these requirements. The present lack of these measures include restricted research opportunities, stifled innovation, and a general fear amongst individuals and organizations of data misuse that could ultimately hinder the broader adoption of AI technologies. In conclusion, I urge the agency to mandate that all AI tools offer a complete, and legally VERIFIABLE private mode. This requirement will not only protect user data but also foster responsible innovation across all sectors, ensuring that the benefits of AI technology can be fully realized without compromising privacy or security. Thank you.",2536,392,full_document_text,
page_text,1,"Tom H Thank you for the opportunity to comment on the development of an Artificial Intelligence (AI) Action Plan. The rapid evolution of AI technology demands that we ensure advancements do not compromise user privacy or the protection of intellectual property ( IP) rights. The rate at which AI technologies are advancing in capability is too significant to gate from use in any contexts. The lack of control over flow of information when using an AI tool not only jeopardizes user privacy and IP but also severely res tricts critical research opportunities especially in clinical settings where patient data is involved. As it stands, the amount of information that the AI tools and/or their company extracts from user input is not transparent whatsoever. The current lack o f transparency regarding the data extracted from user inputs is deeply concerning. AI tools should include a verifiable private mode that prevents data from being transmitted, stored, or used for training by the company or any other entities unless the u ser explicitly opts in. This option must be easily accessible and provide complete control to the user, ensuring that both individuals and organizations can safeguard their sensitive information. Moreover, complete legal assurance is essential for the hand ling of personal, proprietary, and clinical data. Without robust privacy controls, there is a significant risk that confidential information may be exposed to unauthorized parties. In the case of clinical data, for instance, the absence of clear safeguards prevents researchers and clinicians from confidently using AI tools. Compliance with HIPAA guidelines is crucial here; any AI tool handling patient information must guarantee that such data is fully protected and not compromised in any way. Aligning these privacy measures with existing legal frameworks - such as GDPR and HIPAA - would provide a strong legal and ethical foundation for these requirements. The present lack of these measures include restricted research opportunities, stifled innovation, and a general fear amongst individuals and organizations of data misuse that could ultimately hinder the broader adoption of AI technologies. In conclusion, I urge the agency to mandate that all AI tools offer a complete, and legally VERIFIABLE private mode. This requirement will not only protect user data but also foster responsible innovation across all sectors, ensuring that the benefits of AI technology can be fully realized without compromising privacy or security. Thank you.",2536,392,page_content,page_1
