<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Policy Simulator Module Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .stakeholder-item {
            padding: 8px;
            border-radius: 6px;
            transition: all 0.3s ease;
        }
        
        .stakeholder-item:hover {
            background-color: #f8f9fa;
        }
        
        .metric-item {
            padding: 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px;
            margin-bottom: 15px;
        }
        
        .insight-item {
            padding: 8px;
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            border-radius: 4px;
            margin-bottom: 8px;
        }
        
        .simulation-results {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-cogs me-2"></i>
            Policy Simulator Module Test
        </h1>
        
        <!-- Simulation Configuration -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cogs"></i> Simulation Configuration</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label for="simulationType" class="form-label">Simulation Type</label>
                        <select class="form-select" id="simulationType">
                            <option value="regulatory">Regulatory Change</option>
                            <option value="compliance">Compliance Requirements</option>
                            <option value="innovation">Innovation Policy</option>
                            <option value="market">Market Intervention</option>
                            <option value="international">International Cooperation</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="policyIntensity" class="form-label">Policy Intensity</label>
                        <select class="form-select" id="policyIntensity">
                            <option value="minimal">Minimal Change (+5%)</option>
                            <option value="moderate">Moderate Change (+15%)</option>
                            <option value="significant">Significant Change (+30%)</option>
                            <option value="major">Major Overhaul (+50%)</option>
                            <option value="revolutionary">Revolutionary (+100%)</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="simulationTimeframe" class="form-label">Timeframe</label>
                        <select class="form-select" id="simulationTimeframe">
                            <option value="6m">6 Months</option>
                            <option value="1y" selected>1 Year</option>
                            <option value="2y">2 Years</option>
                            <option value="5y">5 Years</option>
                            <option value="10y">10 Years</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-primary mt-4" onclick="runPolicySimulation()">
                            <i class="fas fa-rocket"></i> Run Simulation
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Affected Sectors and Stakeholders -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-industry"></i> Affected Sectors</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="techSector" checked>
                                    <label class="form-check-label" for="techSector">
                                        <i class="fas fa-microchip me-2"></i>Technology
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="financeSector">
                                    <label class="form-check-label" for="financeSector">
                                        <i class="fas fa-dollar-sign me-2"></i>Finance
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="healthSector">
                                    <label class="form-check-label" for="healthSector">
                                        <i class="fas fa-heartbeat me-2"></i>Healthcare
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="educationSector">
                                    <label class="form-check-label" for="educationSector">
                                        <i class="fas fa-graduation-cap me-2"></i>Education
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="transportSector">
                                    <label class="form-check-label" for="transportSector">
                                        <i class="fas fa-car me-2"></i>Transportation
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="energySector">
                                    <label class="form-check-label" for="energySector">
                                        <i class="fas fa-bolt me-2"></i>Energy
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="manufacturingSector">
                                    <label class="form-check-label" for="manufacturingSector">
                                        <i class="fas fa-industry me-2"></i>Manufacturing
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="retailSector">
                                    <label class="form-check-label" for="retailSector">
                                        <i class="fas fa-shopping-cart me-2"></i>Retail
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-users"></i> Key Stakeholders</h6>
                    </div>
                    <div class="card-body">
                        <div id="stakeholderImpact">
                            <div class="stakeholder-item mb-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-building text-primary me-2"></i>Large Corporations</span>
                                    <span class="badge bg-warning" id="corporateImpact">Medium Impact</span>
                                </div>
                            </div>
                            <div class="stakeholder-item mb-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-rocket text-success me-2"></i>Startups & SMEs</span>
                                    <span class="badge bg-danger" id="startupImpact">High Impact</span>
                                </div>
                            </div>
                            <div class="stakeholder-item mb-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-university text-info me-2"></i>Academic Institutions</span>
                                    <span class="badge bg-success" id="academicImpact">Low Impact</span>
                                </div>
                            </div>
                            <div class="stakeholder-item mb-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-landmark text-warning me-2"></i>Government Agencies</span>
                                    <span class="badge bg-info" id="governmentImpact">Positive Impact</span>
                                </div>
                            </div>
                            <div class="stakeholder-item mb-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-user-friends text-secondary me-2"></i>General Public</span>
                                    <span class="badge bg-primary" id="publicImpact">Mixed Impact</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Simulation Results Dashboard -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0"><i class="fas fa-chart-area"></i> Policy Impact Projection</h6>
                        <div>
                            <button class="btn btn-outline-secondary btn-sm" onclick="exportSimulationResults()">
                                <i class="fas fa-download"></i> Export
                            </button>
                            <button class="btn btn-outline-primary btn-sm" onclick="resetSimulation()">
                                <i class="fas fa-undo"></i> Reset
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="simulationResults">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-cogs fa-3x mb-3"></i>
                                <p>Configure simulation parameters and click "Run Simulation" to see projected outcomes</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-tachometer-alt"></i> Simulation Metrics</h6>
                    </div>
                    <div class="card-body">
                        <div id="simulationMetrics">
                            <div class="metric-item mb-3">
                                <h6>Overall Impact Score</h6>
                                <div class="d-flex justify-content-between mb-1">
                                    <span>Economic Impact</span>
                                    <span class="badge bg-success" id="economicImpactScore">+12.5%</span>
                                </div>
                                <div class="progress mb-2" style="height: 8px;">
                                    <div class="progress-bar bg-success" id="economicImpactBar" style="width: 62.5%"></div>
                                </div>
                                <div class="d-flex justify-content-between mb-1">
                                    <span>Innovation Index</span>
                                    <span class="badge bg-info" id="innovationScore">+8.3%</span>
                                </div>
                                <div class="progress mb-2" style="height: 8px;">
                                    <div class="progress-bar bg-info" id="innovationBar" style="width: 54.15%"></div>
                                </div>
                                <div class="d-flex justify-content-between mb-1">
                                    <span>Compliance Cost</span>
                                    <span class="badge bg-warning" id="complianceScore">+15.7%</span>
                                </div>
                                <div class="progress mb-2" style="height: 8px;">
                                    <div class="progress-bar bg-warning" id="complianceBar" style="width: 65.7%"></div>
                                </div>
                            </div>
                            
                            <div class="metric-item mb-3">
                                <h6>Risk Assessment</h6>
                                <div class="d-flex justify-content-between mb-1">
                                    <span>Implementation Risk</span>
                                    <span class="badge bg-danger" id="implementationRisk">Medium</span>
                                </div>
                                <div class="d-flex justify-content-between mb-1">
                                    <span>Market Disruption</span>
                                    <span class="badge bg-warning" id="marketDisruption">Low</span>
                                </div>
                                <div class="d-flex justify-content-between mb-1">
                                    <span>Public Acceptance</span>
                                    <span class="badge bg-success" id="publicAcceptance">High</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Controls -->
        <div class="test-section">
            <h2><i class="fas fa-cogs me-2"></i>Test Controls</h2>
            <div class="row">
                <div class="col-md-6">
                    <h6>Quick Test Simulations</h6>
                    <button class="btn btn-outline-primary me-2 mb-2" onclick="testSimulation('regulatory', 'significant')">
                        <i class="fas fa-gavel"></i> Test Regulatory Change
                    </button>
                    <button class="btn btn-outline-success me-2 mb-2" onclick="testSimulation('innovation', 'major')">
                        <i class="fas fa-lightbulb"></i> Test Innovation Policy
                    </button>
                    <button class="btn btn-outline-info me-2 mb-2" onclick="testSimulation('market', 'moderate')">
                        <i class="fas fa-chart-line"></i> Test Market Intervention
                    </button>
                    <button class="btn btn-outline-warning me-2 mb-2" onclick="testMultiSector()">
                        <i class="fas fa-industry"></i> Test Multi-Sector Impact
                    </button>
                </div>
                <div class="col-md-6">
                    <h6>Test Results</h6>
                    <div id="testResults" class="alert alert-info">
                        <p><strong>Test Status:</strong> Ready to test policy simulation functionality</p>
                        <ul>
                            <li>✅ Multi-type policy simulation</li>
                            <li>✅ Stakeholder impact analysis</li>
                            <li>✅ Time series projection</li>
                            <li>✅ Risk assessment metrics</li>
                            <li>✅ Interactive sector selection</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="dashboard/frontend/dashboard.js"></script>
    <script>
        // Test functions
        function testSimulation(type, intensity) {
            console.log(`🧪 Testing simulation: ${type} with ${intensity} intensity`);
            
            document.getElementById('simulationType').value = type;
            document.getElementById('policyIntensity').value = intensity;
            
            runPolicySimulation();
            
            const testResults = document.getElementById('testResults');
            testResults.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle"></i> Simulation Test: ${type}</h6>
                    <p>✅ Policy type changed to ${type}</p>
                    <p>✅ Intensity set to ${intensity}</p>
                    <p>✅ Simulation executed successfully</p>
                    <p><strong>Try:</strong> Explore the impact projection and risk assessment</p>
                </div>
            `;
        }
        
        function testMultiSector() {
            console.log('🧪 Testing multi-sector impact...');
            
            // Select multiple sectors
            const sectors = ['techSector', 'financeSector', 'healthSector', 'educationSector'];
            sectors.forEach(sectorId => {
                const checkbox = document.getElementById(sectorId);
                if (checkbox) checkbox.checked = true;
            });
            
            // Update stakeholder impact
            updateStakeholderImpact();
            
            // Run simulation
            runPolicySimulation();
            
            const testResults = document.getElementById('testResults');
            testResults.innerHTML = `
                <div class="alert alert-info">
                    <h6><i class="fas fa-industry"></i> Multi-Sector Test</h6>
                    <p>✅ Multiple sectors selected</p>
                    <p>✅ Stakeholder impacts updated</p>
                    <p>✅ Cross-sector analysis executed</p>
                    <p><strong>Try:</strong> Compare single vs multi-sector impacts</p>
                </div>
            `;
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Policy Simulator Test Page Loaded');
            
            // Initialize policy simulator functionality
            if (typeof loadPolicySimulator === 'function') {
                loadPolicySimulator();
            } else {
                console.warn('Policy Simulator functions not loaded. Make sure dashboard.js is included.');
            }
        });
    </script>
</body>
</html>
