@echo off
echo ========================================
echo AI Policy Analyzer - 完整系统启动
echo ========================================
echo.

REM 设置窗口标题
title AI Policy Analyzer - System Launcher

REM 检查Python是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安装或不在PATH中
    pause
    exit /b 1
)

echo ✅ Python 环境检查通过
echo.

REM 检查必要文件
if not exist "dashboard\backend\port_config.py" (
    echo ❌ 端口配置文件不存在
    pause
    exit /b 1
)

echo ✅ 配置文件检查通过
echo.

REM 显示端口配置
echo 📋 端口配置:
echo    前端服务器: 8028
echo    Visualization API: 5001
echo    Search API: 5002
echo    Historical Data API: 5003
echo    Advanced Analytics API: 5005
echo    Enhanced Batch Processor: 5007
echo.

REM 启动后端API服务
echo 🚀 启动后端API服务...
echo.

echo 启动 Visualization API (端口 5001)...
start "Visualization API" cmd /k "cd /d dashboard\backend && python visualization_api.py"
timeout /t 2 /nobreak >nul

echo 启动 Search API (端口 5002)...
start "Search API" cmd /k "cd /d dashboard\backend && python search_endpoints.py"
timeout /t 2 /nobreak >nul

echo 启动 Historical Data API (端口 5003)...
start "Historical Data API" cmd /k "cd /d dashboard\backend && python historical_data_endpoints.py"
timeout /t 2 /nobreak >nul

echo 启动 Advanced Analytics API (端口 5005)...
start "Advanced Analytics API" cmd /k "cd /d dashboard\backend && python advanced_analytics_api.py"
timeout /t 2 /nobreak >nul

echo 启动 Enhanced Batch Processor (端口 5007)...
start "Enhanced Batch Processor" cmd /k "cd /d dashboard\backend && python enhanced_batch_processor.py"
timeout /t 2 /nobreak >nul

echo.
echo 🌐 启动前端服务器...
echo 启动前端服务器 (端口 8028)...
start "Frontend Server" cmd /k "cd /d dashboard\frontend && python server.py"
timeout /t 3 /nobreak >nul

echo.
echo ⏳ 等待所有服务启动完成...
timeout /t 5 /nobreak >nul

echo.
echo ========================================
echo 🎉 系统启动完成！
echo ========================================
echo.
echo 🌐 访问地址:
echo    📱 主仪表板: http://localhost:8028
echo    📱 直接访问: http://localhost:8028/index.html
echo.
echo 🔧 API服务健康检查:
echo    Visualization API: http://localhost:5001/api/visualize/health
echo    Search API: http://localhost:5002/api/search/health
echo    Historical Data API: http://localhost:5003/api/historical/health
echo    Advanced Analytics API: http://localhost:5005/api/analytics/health
echo    Enhanced Batch Processor: http://localhost:5007/api/batch/health
echo.
echo 📝 注意事项:
echo    - 每个服务都在独立的命令行窗口中运行
echo    - 关闭对应窗口即可停止相应服务
echo    - 建议等待5-10秒让所有服务完全启动
echo.
echo ⌨️ 按任意键运行系统健康检查...
pause >nul

echo.
echo 🔍 运行系统健康检查...
python comprehensive_system_health_check.py

echo.
echo 📊 系统健康检查完成！
echo.
echo ⌨️ 按任意键退出启动器...
pause >nul
