<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Policy Analyzer Dashboard</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="frontend/styles.css">
</head>
<body>
    <div class="d-flex" id="wrapper">
        <!-- Sidebar -->
        <div class="border-end bg-white" id="sidebar-wrapper">
            <div class="sidebar-heading border-bottom bg-light">
                <img src="frontend/assets/logo.svg" alt="AI Policy Analyzer" class="logo-img">
                <div>AI Policy Analyzer</div>
            </div>
            <div class="list-group list-group-flush">
                <a href="#" class="list-group-item list-group-item-action active" data-section="dashboard">
                    <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                </a>
                <a href="#" class="list-group-item list-group-item-action" data-section="search">
                    <i class="fas fa-search me-2"></i> Search
                </a>
                <a href="#" class="list-group-item list-group-item-action" data-section="analysis">
                    <i class="fas fa-chart-pie me-2"></i> Analysis
                </a>
                <a href="#" class="list-group-item list-group-item-action" data-section="visualizations">
                    <i class="fas fa-chart-bar me-2"></i> Visualizations
                </a>
                <a href="#" class="list-group-item list-group-item-action" data-section="upload">
                    <i class="fas fa-upload me-2"></i> Upload
                </a>
                <a href="#" class="list-group-item list-group-item-action" data-section="sentiment-lab">
                    <i class="fas fa-brain me-2"></i> Sentiment Lab
                </a>
                <a href="#" class="list-group-item list-group-item-action" data-section="network">
                    <i class="fas fa-project-diagram me-2"></i> Network Analysis
                </a>
                <a href="#" class="list-group-item list-group-item-action" data-section="policy-simulator">
                    <i class="fas fa-flask me-2"></i> Policy Simulator
                </a>
                <a href="#" class="list-group-item list-group-item-action" data-section="historical">
                    <i class="fas fa-history me-2"></i> Historical Data
                </a>
            </div>
        </div>
        
        <!-- Page content wrapper -->
        <div id="page-content-wrapper">
            <!-- Top navigation -->
            <nav class="navbar navbar-expand-lg navbar-light bg-light border-bottom">
                <div class="container-fluid">
                    <button class="btn btn-primary" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="d-flex flex-grow-1 ms-3">
                        <input type="text" class="form-control" placeholder="Search policies, organizations..." id="searchInput">
                        <button class="btn btn-outline-primary ms-2" onclick="performSearch()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    <div class="dropdown ms-3">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="userMenu" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userMenu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>Settings</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-question-circle me-2"></i>Help</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </div>
                </div>
            </nav>

            <!-- Page content -->
            <div class="container-fluid">
                <!-- Loading overlay -->
                <div id="loading-overlay">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
                
                <!-- Notification area -->
                <div id="notification-area"></div>
                
                <!-- Dashboard Section -->
                <div id="dashboard-section" class="content-section active">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2 class="h4 mb-0">Dashboard Overview</h2>
                        <button class="btn btn-sm btn-outline-primary" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt me-1"></i> Refresh
                        </button>
                    </div>
                    
                    <div class="row g-3">
                        <!-- Key Metrics -->
                        <div class="col-md-12">
                            <div class="card shadow-sm h-100">
                                <div class="card-body p-0">
                                    <div class="row g-0">
                                        <div class="col-md-4 border-end">
                                            <div class="p-3 text-center">
                                                <h3 class="display-5" id="total-documents">--</h3>
                                                <p class="text-muted mb-0">Total Documents</p>
                                            </div>
                                        </div>
                                        <div class="col-md-4 border-end">
                                            <div class="p-3 text-center">
                                                <h3 class="display-5" id="total-analyses">--</h3>
                                                <p class="text-muted mb-0">Total Analyses</p>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="p-3 text-center">
                                                <h3 class="display-5" id="total-organizations">--</h3>
                                                <p class="text-muted mb-0">Organizations</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Policy Distribution -->
                        <div class="col-md-6">
                            <div class="card shadow-sm h-100">
                                <div class="card-header bg-white border-bottom-0">
                                    <h5 class="card-title mb-0">Policy Preference Distribution</h5>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container">
                                        <canvas id="policyChart" height="240"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Organization Types -->
                        <div class="col-md-6">
                            <div class="card shadow-sm h-100">
                                <div class="card-header bg-white border-bottom-0">
                                    <h5 class="card-title mb-0">Organization Types</h5>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container">
                                        <canvas id="orgTypeChart" height="240"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Sentiment Trend -->
                        <div class="col-md-8">
                            <div class="card shadow-sm h-100">
                                <div class="card-header bg-white border-bottom-0 d-flex justify-content-between align-items-center">
                                    <h5 class="card-title mb-0">Sentiment Trend</h5>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-secondary active" data-period="week">Week</button>
                                        <button type="button" class="btn btn-outline-secondary" data-period="month">Month</button>
                                        <button type="button" class="btn btn-outline-secondary" data-period="year">Year</button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="chart-container">
                                        <canvas id="sentimentTrendChart" height="240"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Activity -->
                        <div class="col-md-4">
                            <div class="card shadow-sm h-100">
                                <div class="card-header bg-white border-bottom-0">
                                    <h5 class="card-title mb-0">Recent Activity</h5>
                                </div>
                                <div class="card-body p-0">
                                    <div id="recent-activity">
                                        <!-- Activity content will be loaded here -->
                                        <div class="d-flex justify-content-center p-4">
                                            <div class="spinner-border spinner-border-sm text-secondary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Top Keywords -->
                        <div class="col-md-12">
                            <div class="card shadow-sm h-100">
                                <div class="card-header bg-white border-bottom-0">
                                    <h5 class="card-title mb-0">Top Keywords by Sector</h5>
                                </div>
                                <div class="card-body">
                                    <div id="keywordsByIndustry">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <h6 class="text-muted mb-3">Technology</h6>
                                                <div id="tech-keywords" class="d-flex flex-wrap gap-2">
                                                    <span class="badge bg-primary">Responsible AI</span>
                                                    <span class="badge bg-primary">Safety</span>
                                                    <span class="badge bg-primary">Innovation</span>
                                                    <span class="badge bg-primary">Ethics</span>
                                                    <span class="badge bg-primary">Governance</span>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <h6 class="text-muted mb-3">Government</h6>
                                                <div id="govt-keywords" class="d-flex flex-wrap gap-2">
                                                    <span class="badge bg-success">Regulation</span>
                                                    <span class="badge bg-success">Oversight</span>
                                                    <span class="badge bg-success">Standards</span>
                                                    <span class="badge bg-success">Compliance</span>
                                                    <span class="badge bg-success">Framework</span>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <h6 class="text-muted mb-3">Academic</h6>
                                                <div id="academic-keywords" class="d-flex flex-wrap gap-2">
                                                    <span class="badge bg-info">Research</span>
                                                    <span class="badge bg-info">Alignment</span>
                                                    <span class="badge bg-info">Long-term</span>
                                                    <span class="badge bg-info">Technical</span>
                                                    <span class="badge bg-info">Publication</span>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <h6 class="text-muted mb-3">Non-profit</h6>
                                                <div id="nonprofit-keywords" class="d-flex flex-wrap gap-2">
                                                    <span class="badge bg-warning">Ethics</span>
                                                    <span class="badge bg-warning">Rights</span>
                                                    <span class="badge bg-warning">Equity</span>
                                                    <span class="badge bg-warning">Access</span>
                                                    <span class="badge bg-warning">Global</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Search Section -->
                <div id="search-section" class="content-section">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2 class="h4 mb-0">Policy Search</h2>
                        <button class="btn btn-sm btn-outline-secondary" onclick="toggleFilters()">
                            <i class="fas fa-filter me-1"></i> Filters
                        </button>
                    </div>
                    
                    <!-- Filter Panel -->
                    <div class="card mb-4 shadow-sm" id="filterPanel" style="display: none;">
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <label class="form-label">Organization Type</label>
                                    <select class="form-select" id="orgTypeFilter">
                                        <option value="">All Types</option>
                                        <option value="Corporate">Corporate</option>
                                        <option value="Academic">Academic</option>
                                        <option value="Government">Government</option>
                                        <option value="Nonprofit">Nonprofit</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Sector</label>
                                    <select class="form-select" id="sectorFilter">
                                        <option value="">All Sectors</option>
                                        <option value="Technology">Technology</option>
                                        <option value="Research">Research</option>
                                        <option value="Government">Government</option>
                                        <option value="Healthcare">Healthcare</option>
                                        <option value="Education">Education</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Analysis Type</label>
                                    <select class="form-select" id="analysisFilter">
                                        <option value="">All Analysis</option>
                                        <option value="Sentiment">Sentiment</option>
                                        <option value="Policy">Policy</option>
                                        <option value="Keyword">Keyword</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Results Area -->
                    <div class="card shadow-sm">
                        <div class="card-body p-0">
                            <div id="searchResults" class="p-3">
                                <div class="text-center text-muted py-5">
                                    <i class="fas fa-search fa-3x mb-3"></i>
                                    <p>Enter a search query to find AI policy documents and analysis</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Analysis Section -->
                <div id="analysis-section" class="content-section">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2 class="h4 mb-0">Analysis Tools</h2>
                    </div>
                    
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="card shadow-sm h-100">
                                <div class="card-body">
                                    <h5 class="card-title"><i class="fas fa-robot me-2"></i> Sentiment Analysis</h5>
                                    <p class="card-text">Analyze sentiment patterns in policy documents.</p>
                                    <button class="btn btn-primary" onclick="loadSentimentLab()">
                                        Launch Tool
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card shadow-sm h-100">
                                <div class="card-body">
                                    <h5 class="card-title"><i class="fas fa-project-diagram me-2"></i> Network Analysis</h5>
                                    <p class="card-text">Visualize connections between organizations and policy positions.</p>
                                    <button class="btn btn-primary" onclick="loadNetworkAnalysis()">
                                        Launch Tool
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card shadow-sm h-100">
                                <div class="card-body">
                                    <h5 class="card-title"><i class="fas fa-flask me-2"></i> Policy Simulator</h5>
                                    <p class="card-text">Test different policy scenarios and predict outcomes.</p>
                                    <button class="btn btn-primary" onclick="loadPolicySimulator()">
                                        Launch Tool
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card shadow-sm h-100">
                                <div class="card-body">
                                    <h5 class="card-title"><i class="fas fa-chart-line me-2"></i> Trend Analysis</h5>
                                    <p class="card-text">Track policy sentiment and preference changes over time.</p>
                                    <button class="btn btn-primary" onclick="loadTrendAnalysis()">
                                        Launch Tool
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Upload Section -->
                <div id="upload-section" class="content-section">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2 class="h4 mb-0">Upload Documents</h2>
                    </div>
                    
                    <div class="card shadow-sm">
                        <div class="card-body">
                            <div id="uploadArea" class="upload-area text-center p-5">
                                <i class="fas fa-cloud-upload-alt fa-4x text-primary mb-3"></i>
                                <h5>Drag & drop files here</h5>
                                <p class="text-muted">or click to browse</p>
                                <input type="file" id="fileInput" multiple style="display: none">
                            </div>
                            
                            <div id="uploadProgress" class="mt-4" style="display: none;">
                                <div class="progress mb-2">
                                    <div id="progressBar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                                </div>
                                <div id="uploadStatus"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card shadow-sm mt-4">
                        <div class="card-header bg-white">
                            <h5 class="card-title mb-0">Recently Uploaded</h5>
                        </div>
                        <div class="card-body p-0">
                            <table class="table mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>File Name</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="uploadHistory">
                                    <tr>
                                        <td class="text-muted" colspan="4">No recent uploads</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Visualizations Section -->
                <div id="visualizations-section" class="content-section">
                    <!-- This section will be populated dynamically -->
                </div>

                <!-- Sentiment Lab Section -->
                <div id="sentiment-lab-section" class="content-section">
                    <!-- This section will be populated dynamically -->
                </div>

                <!-- Network Analysis Section -->
                <div id="network-section" class="content-section">
                    <!-- This section will be populated dynamically -->
                </div>

                <!-- Policy Simulator Section -->
                <div id="policy-simulator-section" class="content-section">
                    <!-- This section will be populated dynamically -->
                </div>

                <!-- Historical Data Section -->
                <div id="historical-section" class="content-section">
                    <!-- This section will be populated dynamically -->
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
    <!-- Dashboard Patch for Syntax Errors -->
    <script src="frontend/dashboard_patch.js"></script>
    <!-- Dashboard Fix Scripts -->
    <script src="frontend/fix_dashboard.js"></script>
    <script src="frontend/syntax_fix.js"></script>
    <!-- Dashboard JS -->
    <script src="frontend/dashboard_complete_final.js"></script>
    <!-- Dashboard Enhancements -->
    <script src="frontend/dashboard_enhancements.js"></script>
    <!-- Dashboard Initialization Fix -->
    <script src="frontend/dashboard_init_fix.js"></script>
</body>
</html>
