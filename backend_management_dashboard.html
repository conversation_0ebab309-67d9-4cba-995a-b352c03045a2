<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backend Management Dashboard - AI Policy Analysis</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .management-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 0;
            margin-bottom: 30px;
        }
        
        .status-card {
            transition: all 0.3s ease;
            border-left: 4px solid #007bff;
        }
        
        .status-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .status-card.success {
            border-left-color: #28a745;
        }
        
        .status-card.warning {
            border-left-color: #ffc107;
        }
        
        .status-card.danger {
            border-left-color: #dc3545;
        }
        
        .processing-queue {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .queue-item {
            padding: 10px;
            border-bottom: 1px solid #dee2e6;
            transition: background-color 0.3s ease;
        }
        
        .queue-item:hover {
            background-color: #f8f9fa;
        }
        
        .queue-item.processing {
            background-color: #fff3cd;
            border-left: 3px solid #ffc107;
        }
        
        .queue-item.completed {
            background-color: #d4edda;
            border-left: 3px solid #28a745;
        }
        
        .queue-item.error {
            background-color: #f8d7da;
            border-left: 3px solid #dc3545;
        }
        
        .log-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
            border-radius: 3px;
        }
        
        .log-info {
            color: #0c5460;
        }
        
        .log-success {
            color: #155724;
            background-color: #d4edda;
        }
        
        .log-warning {
            color: #856404;
            background-color: #fff3cd;
        }
        
        .log-error {
            color: #721c24;
            background-color: #f8d7da;
        }
        
        .metric-value {
            font-size: 2.5em;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .progress-ring {
            width: 120px;
            height: 120px;
            margin: 0 auto;
        }
        
        .progress-ring circle {
            fill: transparent;
            stroke: #e9ecef;
            stroke-width: 8;
        }
        
        .progress-ring .progress {
            stroke: #007bff;
            stroke-linecap: round;
            transition: stroke-dasharray 0.5s ease;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .processing-indicator {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <!-- Management Header -->
    <div class="management-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-server me-3"></i>Backend Management Dashboard</h1>
                    <p class="lead mb-0">Monitor and manage AI Policy Analysis backend processing</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex align-items-center justify-content-end">
                        <span class="me-2">Backend Status:</span>
                        <span class="badge bg-success" id="backend-status">Checking...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- System Status Overview -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card status-card">
                    <div class="card-body text-center">
                        <i class="fas fa-file-alt fa-2x mb-2 text-primary"></i>
                        <div class="metric-value text-primary" id="total-documents">0</div>
                        <div>Total Documents</div>
                        <small class="text-muted">In database</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card status-card success">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x mb-2 text-success"></i>
                        <div class="metric-value text-success" id="processed-documents">0</div>
                        <div>Processed</div>
                        <small class="text-muted">Analysis completed</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card status-card warning">
                    <div class="card-body text-center">
                        <i class="fas fa-cog fa-2x mb-2 text-warning processing-indicator"></i>
                        <div class="metric-value text-warning" id="processing-documents">0</div>
                        <div>Processing</div>
                        <small class="text-muted">Currently analyzing</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card status-card">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-2x mb-2 text-info"></i>
                        <div class="metric-value text-info" id="queued-documents">0</div>
                        <div>Queued</div>
                        <small class="text-muted">Waiting to process</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Processing Progress -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-chart-line"></i> Processing Progress</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Overall Progress</span>
                            <span id="progress-percentage">0%</span>
                        </div>
                        <div class="progress mb-3" style="height: 20px;">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 id="processing-progress" style="width: 0%">
                                <span id="progress-text">0%</span>
                            </div>
                        </div>
                        <div class="row text-center">
                            <div class="col-md-4">
                                <small class="text-muted">Processing Rate</small>
                                <div class="h5" id="processing-rate">0%</div>
                            </div>
                            <div class="col-md-4">
                                <small class="text-muted">Avg Processing Time</small>
                                <div class="h5" id="avg-processing-time">--</div>
                            </div>
                            <div class="col-md-4">
                                <small class="text-muted">ETA Completion</small>
                                <div class="h5" id="eta-completion">--</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-tachometer-alt"></i> System Performance</h6>
                    </div>
                    <div class="card-body text-center">
                        <div class="progress-ring">
                            <svg width="120" height="120">
                                <circle cx="60" cy="60" r="50"></circle>
                                <circle cx="60" cy="60" r="50" class="progress" id="performance-ring"
                                        stroke-dasharray="0 314" transform="rotate(-90 60 60)"></circle>
                            </svg>
                        </div>
                        <div class="mt-2">
                            <div class="h4" id="system-performance">85%</div>
                            <small class="text-muted">System Efficiency</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Management Actions -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-tools"></i> Management Actions</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <button class="btn btn-primary w-100 mb-2" onclick="scanDocuments()">
                                    <i class="fas fa-search"></i> Scan Documents
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-success w-100 mb-2" onclick="startBatchProcessing()">
                                    <i class="fas fa-play"></i> Start Processing
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-info w-100 mb-2" onclick="refreshStatus()">
                                    <i class="fas fa-sync"></i> Refresh Status
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-warning w-100 mb-2" onclick="exportResults()">
                                    <i class="fas fa-download"></i> Export Results
                                </button>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input type="number" class="form-control" id="batchSize" 
                                           placeholder="Batch size" value="100" min="1" max="1000">
                                    <button class="btn btn-outline-secondary" onclick="setBatchSize()">
                                        Set Batch Size
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="input-group">
                                    <select class="form-select" id="processingPriority">
                                        <option value="1">Low Priority</option>
                                        <option value="2" selected>Normal Priority</option>
                                        <option value="3">High Priority</option>
                                    </select>
                                    <button class="btn btn-outline-secondary" onclick="setPriority()">
                                        Set Priority
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Processing Queue and Logs -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0"><i class="fas fa-list"></i> Processing Queue</h6>
                        <button class="btn btn-outline-secondary btn-sm" onclick="refreshQueue()">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                    </div>
                    <div class="card-body p-0">
                        <div class="processing-queue" id="processing-queue">
                            <!-- Queue items will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0"><i class="fas fa-terminal"></i> System Logs</h6>
                        <button class="btn btn-outline-secondary btn-sm" onclick="clearLogs()">
                            <i class="fas fa-trash"></i> Clear
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="log-container" id="system-logs">
                            <div class="log-entry log-info">[INFO] Backend management dashboard initialized</div>
                            <div class="log-entry log-info">[INFO] Connecting to backend system...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analytics Summary -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-chart-bar"></i> Analytics Summary</h6>
                    </div>
                    <div class="card-body">
                        <div id="analytics-summary">
                            <!-- Analytics will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Messages -->
        <div id="status-messages"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="dashboard/frontend/backend_integration.js"></script>
    <script src="backend_management.js"></script>
    <script>
        // Initialize management dashboard when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🖥️ Backend Management Dashboard Loaded');
            initializeManagementDashboard();
        });
    </script>
</body>
</html>
