# PowerShell script to find syntax errors in dashboard JavaScript

# Read the file
$jsFile = "dashboard_complete_final.js"
$content = Get-Content $jsFile -Raw
$lines = Get-Content $jsFile

Write-Host "Checking for common JavaScript syntax errors..."

# Look for specific patterns that cause "Invalid left-hand side in assignment" errors
$lineNum = 0
foreach ($line in $lines) {
    $lineNum++
    
    # Check for assigning to function calls
    if ($line -match '\w+\([^)]*\)\s*=\s*[^=]') {
        Write-Host "Line $lineNum - Potential assignment to function call:"
        Write-Host $line -ForegroundColor Red
    }
    
    # Check for single equals in if conditions
    if ($line -match 'if\s*\([^=!<>]+\s*=\s*[^=]') {
        Write-Host "Line $lineNum - Potential single equals in if condition:"
        Write-Host $line -ForegroundColor Red
    }
    
    # Check for assignment to literals
    if ($line -match '(true|false|null|undefined|\d+)\s*=\s*[^=]') {
        Write-Host "Line $lineNum - Potential assignment to literal:"
        Write-Host $line -ForegroundColor Red
    }
    
    # Check for arrow functions with problematic assignments
    if ($line -match '=>.*\w+\([^)]*\)\s*=\s*[^=]') {
        Write-Host "Line $lineNum - Potential problematic assignment in arrow function:"
        Write-Host $line -ForegroundColor Red
    }
}

# Look for errors in Promise chains
Write-Host "`nChecking for errors in Promise chains..."

$promisePattern = '\.then\(.*\)\..*\('
$matches = [regex]::Matches($content, $promisePattern)
foreach ($match in $matches) {
    $lineNum = ($content.Substring(0, $match.Index) -split "`n").Length
    Write-Host "Line $lineNum - Potential Promise chain issue:"
    Write-Host $lines[$lineNum-1] -ForegroundColor Yellow
}

Write-Host "`nChecking complete."
