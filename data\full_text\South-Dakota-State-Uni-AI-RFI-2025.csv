﻿content_type,page_number,content,character_count,word_count,metadata_field,metadata_value
document_info,0,Document: South-Dakota-State-Uni-AI-RFI-2025.pdf,0,0,filename,South-Dakota-State-Uni-AI-RFI-2025.pdf
metadata,0,"Comments Received in Response To: Request for Information on the Development of an Artificial Intelligence (AI) Action Plan (""Plan"")",132,19,title,"Comments Received in Response To: Request for Information on the Development of an Artificial Intelligence (AI) Action Plan (""Plan"")"
metadata,0,Adobe Acrobat Pro (64-bit) 25.1.20435,37,5,creator,Adobe Acrobat Pro (64-bit) 25.1.20435
metadata,0,Adobe Acrobat Pro (64-bit) 25.1.20435,37,5,producer,Adobe Acrobat Pro (64-bit) 25.1.20435
metadata,0,D:20250415130731-04'00',23,1,creation_date,D:20250415130731-04'00'
metadata,0,D:20250415130731-04'00',23,1,modification_date,D:20250415130731-04'00'
document_stats,0,"Total pages: 14, Total characters: 34429, Total words: 4572",34429,4572,document_stats,"pages:14,chars:34429,words:4572"
full_text,0,"South Dakota State University Page 1 of 14 March 15, 2025 South Dakota Artificial Intelligence Leadership, Excellence, and Dominance Summary This document outlines a framework and a strategic plan for developing an integrated AI ecosystem based on guidance from the Federal Register s Request for Information on the Development of an Artificial Intelligence (AI) Action Plan. Our plan calls for a tight partnership between higher education and the private sector to position the U.S. at the forefront of innovation and ensure long-term dominance in AI. The strategy is built around a two-pronged approach - analyzing AI solutions and initiatives from both an offensive and defensive perspective: Offensive: How can we leverage AI to drive success, maximize its benefits, and stay ahead of the competition? What strategic advantages can AI offer to enhance efficiency, innovation, and decision-making? Defensive: How do we anticipate, recognize, and neutralize threats posed by adversarial AI? What safeguards, countermeasures, and resilience strategies must we implement to protect against misuse, vulnerabilities, and emerging risks? This framework ensures a balanced approach harnessing AI s potential while proactively defending against its challenges. Specifically, the initiatives outlined here stem from South Dakota s commitment to AI and cybersecurity through strategic research, innovative education, and robust industry collaboration. By uniting academia and industry, the plan will achieve transformative breakthroughs in three sectors vital to national security: healthcare, the bioeconomy, and food security all key areas the collective talent of South Dakota is well positioned to contribute to. The collaborative model will ensure rapid deployment of AI technologies into practical applications while maintaining technical leadership, ensuring safeguards, and maintaining economic success. What is unique about this plan A strong university-industry partnership is crucial for driving both the offensive advancements and defensive safeguards of AI in healthcare, biomanufacturing, and food security. But unlike regulatory safeguards or oversight, we propose safeguards by design with private sector continuously in the loop. Universities contribute cutting-edge research, talent development, and learning frameworks, while industry provides opportunities for deployment, discovery of field-induced vulnerabilities, ensuring South Dakota State University Page 2 of 14 March 15, 2025 deployed AI models are robust to real-world applications, providing additional funding, and ensuring scalability. This synergy fosters a balanced approach where AI is leveraged for maximum impact while ensuring resilience against potential threats. Ultimately, such partnerships will not only propel technological progress but also fortify our industries against disruptions and ensuring their long-term sustainability and security in these critical fields. Focal Areas The plan will focus on achieving AI excellence and dominance in three areas which are vital to national security and economic competitiveness. Healthcare Bioeconomy/Biomanufacturing Food Security Healthcare Offensive: AI technologies offer transformative benefits in healthcare by enhancing diagnostics, treatment, and operational efficiency. Machine learning algorithms can analyze vast datasets to detect diseases like cancer at early stages to improve patient outcomes. AI-driven drug discovery accelerates the development of new treatments by predicting molecular interactions faster than traditional methods. Robotic-assisted surgeries enhance precision, reducing recovery times and minimizing risks. Additionally, AI-powered predictive analytics can optimize hospital resource allocation, reducing patient wait times and improving overall healthcare delivery. Personalized medicine, enabled by AI, tailors treatments to individual genetic profiles, ensuring more effective therapies. Defensive: Despite its advantages, AI in healthcare also presents risks and vulnerabilities. Cybersecurity threats, such as ransomware attacks on hospital systems, can compromise patient data and disrupt critical care services. Bias in AI models due to incomplete or unrepresentative datasets can lead to misdiagnoses. Over-reliance on AI-driven automation may lead to workforce displacement and reduced human oversight in critical decision-making. Additionally, regulatory and ethical challenges, including patient privacy concerns and accountability in AI-driven medical decisions, pose barriers to widespread adoption. To mitigate these risks, the healthcare sector must implement robust cybersecurity measures, ensure ethical AI governance, and maintain a balance between human expertise and AI-driven automation. South Dakota State University Page 3 of 14 March 15, 2025 Bioeconomy/Biomanufacturing Offensive: AI is revolutionizing the bioeconomy and biomanufacturing by accelerating innovation, optimizing production processes, and driving sustainability. Machine learning models can analyze complex biological data to design more efficient microbial strains for bio-based production, leading to breakthroughs in pharmaceuticals, biofuels, and biodegradable materials. AI-driven automation enhances precision in bioprocessing, reducing waste and increasing yield efficiency. In synthetic biology, AI enables rapid protein engineering and metabolic pathway optimization, paving the way for new bioproducts with high commercial value. Predictive analytics in supply chain management ensures resource optimization, minimizes disruptions, and enhances biomanufacturing scalability. Defensive: Despite its benefits, AI adoption in bioeconomy and biomanufacturing comes with challenges and risks. Cyberattacks on AI-driven bioprocessing systems could compromise critical production pipelines, leading to supply chain vulnerabilities. Intellectual property theft and biopiracy concerns may arise. As AI accelerates genetic discoveries, ethical and regulatory concerns may become important issues. AI-generated biological designs could also be misused for biosecurity threats, necessitating stricter oversight of synthetic biology applications. Additionally, workforce displacement due to automation and potential biases in AI-driven bioprocess decisions may impact consistent access to bio-based innovations. To mitigate these risks, strong cybersecurity protocols, ethical AI governance, and regulatory frameworks are essential to ensure AI s responsible integration into bioeconomic growth and biomanufacturing. Food Security Offensive: AI can play a crucial role in advancing and ensuring food security by optimizing agricultural practices, reducing waste, and improving supply chain efficiency. Through precision farming, AI-powered sensors and drones can monitor soil health, predict weather patterns, and detect pests or diseases early, allowing farmers to make data-driven decisions that maximize crop yields and profitability. AI-driven analytics can also enhance food distribution by forecasting demand, reducing spoilage, and streamlining logistics to ensure food reaches consumers efficiently. Additionally, AI can contribute to sustainable food production by developing alternative proteins, optimizing resource use, and supporting climate-resilient farming techniques. By integrating AI across the food production and distribution chain, we can enhance global food security, minimize losses, and ensure a stable food supply for growing populations. South Dakota State University Page 4 of 14 March 15, 2025 Defensive: If adversaries threaten or sabotage food security, the consequences could be severe, impacting public health, economic stability, and national security. A deliberate attack such as contaminating food supplies, disrupting supply chains, or cyberattacks on agricultural infrastructure could lead to food shortages, price inflation, and widespread panic. Disruptions in critical systems, such as smart farming equipment, automated food processing plants, or logistics networks, could cripple production and distribution, leaving vulnerable populations without access to essential nutrition. Additionally, bioterrorism threats, such as introducing pathogens into crops or livestock, could devastate food sources and economies. To counter these risks, governments and industries must strengthen food security with AI-driven threat detection, blockchain for supply chain transparency, and robust cybersecurity measures to safeguard critical food infrastructure from malicious attacks. Advancing U.S. Healthcare: Safer, cheaper, and smarter Chronic diseases in America are the leading cause of death and disability affecting 60% of the population with 40% having more than one chronic disease and are the leading driver of the nation s $4.1 trillion annual healthcare costs. This is exacerbated by our aging population, rising costs of healthcare, administrative burden of providing patient care, and healthcare workforce shortage, including doctors, nurses, and other professionals. AI is revolutionizing the healthcare industry, offering numerous opportunities to improve health outcomes and enhance the efficiency of the healthcare workforce. Early Detection and Treatment through Diagnostics, Prediction, and Personalization AI tools can discover abnormalities in assessments and medical imaging with remarkable accuracy and often earlier than human counterparts. Integrated into primary care AI will improve health outcomes, save costs for the patient and healthcare system, and prevent unnecessary care. Examples include heart failure, cancer, age-related macular degeneration, chronic kidney disease, etc. AI algorithms can analyze large volumes of data to predict patient outcomes and identify those at higher risk of developing certain conditions as well as personalizing treatment plans. This predictive capability allows for proactive interventions, potentially preventing diseases before they become severe, such as risk of developing opioid dependency, a flare of a chronic disease, mental health crisis, etc. Personalized treatment plans are tailored to the patient s specific needs, enhancing the effectiveness of therapies and reducing adverse effects. Combining predictive and personalization together, AI can monitor patients vital signs in real-time, alerting healthcare providers to any concerning changes. This capability is particularly beneficial for managing chronic conditions and providing care for patients in remote areas. South Dakota State University Page 5 of 14 March 15, 2025 Healthcare Workforce Efficiency AI can facilitate better communication and collaboration among healthcare teams while also offering evidence-based recommendations and clinical decision making for diagnosis and treatment. AI-powered platforms can aggregate patient data from various sources, providing a comprehensive view of the patient's health. This holistic approach enables different specialists to collaborate more effectively, leading to better-coordinated care. AI can also suggest the most effective treatment options by analyzing patient data and comparing it with vast medical databases. This support can enhance the quality of care and ensure that patients receive the best possible treatments. Administrative, Training, and Workforce Management AI can automate routine tasks such as data entry, appointment scheduling, and medical coding, allowing healthcare professionals to focus more on direct patient care, improving job satisfaction and reducing burnout. AI-powered simulation tools provide realistic training scenarios for medical students and professionals. These tools mimic complex medical conditions, allowing trainees to practice and hone their skills in a safe environment. AI also offers personalized learning experiences, adapting to the learner's pace and knowledge level. To assist in workforce management, AI can predict staffing needs and optimize schedules, ensuring adequately staffed healthcare facilities and reducing the risk of overwork and improving patient care. To address issues proactively, AI identifies trends in workforce performance. If AI is properly integrated in healthcare, citizens will be diagnosed and treated earlier with personalized and comprehensive plans where they live. Furthermore, healthcare professionals clinical decision making and collaboration will be optimized while workforce management and administrative tasks will be streamlined. Together, this leads to efficient and less costly healthcare for patients and systems. Drug repurposing Drug repurposing is finding new applications for existing drugs to treat other health conditions. Instead of developing a new medication from scratch, researchers analyze drugs already approved by the US Food and Drug Administration (FDA) for one condition and see if they might be effective for another. Developing a new drug is a lengthy and expensive process. Drug repurposing can significantly shorten this timeline and reduce costs. Much early-stage research is unnecessary because the drugs have undergone clinical safety testing. Drug repurposing can accelerate the availability of treatments for diseases that lack effective therapies, such as metastatic cancers, Alzheimer s disease and the like. It is also valuable for rare diseases where traditional drug development may not be financially viable. Typically, the US FDA approves about South Dakota State University Page 6 of 14 March 15, 2025 10% of the new drug applications. However, about 30% of repurposed drugs are approved by the FDA. Use of artificial intelligence (AI) in drug repurposing: Large data sets are available on FDA-approved medications, including their chemical structures, interaction targets, toxicity, side effects, clearance from the body, interactions with other drugs, and long- term effects. AI methods can analyze the available biomedical information, determine hidden patterns, and identify potential new uses of one or a mixture of drugs. This process can also incorporate genomics and bioinformatics information, patient electronic medical records, laboratory test results, and recommend personalized treatment approaches. Such individualized treatment regimens will reduce costs, decrease toxicity, and increase the efficacy of the drugs. AI and drug repurposing for cancer treatment: The use of AI in personalizing cancer treatment will significantly advance patient care. By analyzing the available data sets on FDA-approved drugs, AI can predict which medications (singly or in combination) efficiently target and deactivate the molecules necessary for cancer initiation, progression, drug resistance, and metastasis. This increases the likelihood of success in clinical trials and enables a new era in individualized, precision oncology. The AI models will predict the potential side effects for patients, ensuring that each patient receives a treatment plan tailored to their unique needs. Challenges in AI-based drug repurposing: All AI models depend on high-quality data sets from experiments with sufficient scientific rigor and reproducibility. Subsequently, predictions need validation through rigorous experimental testing and clinical trials. Finally, adapting existing medications for treating new indications requires approval by the US FDA. South Dakota State University (SDSU) has the necessary elements in place to lead a wide array of partners and stakeholders in using AI to deliver the promise of good health throughout the rural heart of America. SDSU will leverage its degree programs that feed prepared workforce into multiple health professions, its outreach networks into rural communities, and its collaborations with regional universities, and partnerships with three health systems. South Dakota State University Page 7 of 14 March 15, 2025 Advancing the U.S. Bioeconomy: Manufacture better, cheaper, and faster. Biomanufacturing leverages biological systems to generate new products and technologies. It has historically relied on meticulous and time-consuming processes with relatively low efficiency that (i) increase the cost of research and development, (ii) take a longer time for products and technologies to reach the market, and (iii) produce low yields with less precision, often requiring more extensive downstream processing. The integration of AI in biomanufacturing brings about a transformative advancement offering unprecedented opportunities for innovation, efficiency, and precision thus overcoming these limitations. Research and development (R&D) and production systems efficiency and cost-effectiveness will increase due to application of AI enhanced monitoring, quality control, and yield improvement. AI-powered automation and robotics will streamline manufacturing processes, reduce human error and increase precision and throughput. Overall outcome improvement will be accomplished from AI's ability to analyze vast datasets with machine learning algorithms to support data-driven decision- making at every step from discovery to market. Despite these benefits, the adoption of AI in biomanufacturing faces challenges such as data quality and availability, the need for cutting edge algorithms that can be adapted to the ever-expanding types of datasets, integration with existing systems, regulatory hurdles, and the need for workforce training. AI holds transformative potential in biotechnology manufacturing, offering solutions to some of the industry's most pressing challenges while driving progress and innovation. Desired outcomes from AI adoption in biomanufacturing R&D include novel enzymatic reactions and biological regulators including drugs (arising from advanced predictive algorithms), forecasting efficacy and safety of new compounds and systems, automated screening for biological activity to accelerate the discovery phase by prioritizing compounds based on their predicted activity and reduce false positives, and genomic data analyses to identify novel enzymes in microbes allowing for more efficient biomanufacturing systems. AI platforms facilitate collaboration between researchers by integrating data from different sources and providing tools for data analysis and visualization. AI adoption will increasingly foster innovation and accelerate the pace of research that can be readily translated into successful commercialization. AI-driven outcomes in process optimization will provide continuous monitoring of bioprocesses using sensors and data analytics. Real-time adjustments will serve to maintain consistent quality and yield, predict potential quality issues before they occur, optimize fermentation and cell culture conditions to maximize product yield, and analyze historical process data to identify optimal operating parameters for production. South Dakota State University Page 8 of 14 March 15, 2025 AI-powered automated systems streamline laboratory and production workflows increasing throughput and reducing human error in routine tasks. They enable complex manufacturing tasks with high precision (e.g. bioreactor assembly, precision measurement packaging, and quality sealing), and lead to enhanced efficiency and consistency in production lines. AI systems optimize supply chain management through demand prediction, raw materials resourcing, optimization of transportation routes and schedules, and timely delivery of perishable bioproducts, as well as ensuring scheduled maintenance to reduce downtime and enhance the reliability and efficiency of manufacturing processes. AI systems can speed up regulatory compliance and safety systems through creation and management of documentation (reports and audit trails), organizing and analyzing data and required information, speeding up the approval process for new products. Compliance monitoring will ensure Good Manufacturing Practices (GMP) and reduce waste of resources such as water, energy, and raw materials and improve recycling processes. Policies and actions are needed to improve data quality, quantity on biological manufacturing, and protect the security of many bioprocesses proprietary nature. Policies and actions can also target funding streams to generate data and alleviate the high costs associated with bioproduct innovation and development, and to improve or upgrade compatibility of legacy system hardware and software components with modern AI technologies. Developing regulatory frameworks for AI in biotechnology can reduce the barrier of initial investment for AI infrastructure and promote talent pipelines that can reduce limitations for smaller mid-west regional companies and institutions, promote training programs to meet the significant demand for skilled AI professionals, and guide ethical policies on genetic editing and use of AI to assist product or drug prioritization. Providing personnel training to work with AI systems is critical. This involves not only technical training (an understanding of AI s capabilities and limitations), but also development of robust regional infrastructure, including computing power, data storage, information technology network capabilities, resources for continuous update and retraining of models. SDSU is uniquely positioned to lead stakeholders in the development and deployment of AI applications to the bioeconomy. SDSU s location in the heart of the United States, with leading edge bioproduct R&D capabilities, and availability of abundant crop feedstocks are building blocks for securing national leadership and security. South Dakota State University Page 9 of 14 March 15, 2025 Advancing the U.S. Food security: Safe, Secure, and Resilient. Food security is a critical component of national security and economic stability. Ensuring a safe, secure, and resilient food supply chain is essential for meeting the needs of a growing population, mitigating disruptions, and fostering sustainability. AI presents a transformative opportunity to enhance food production, optimize supply chains, and strengthen resilience against emerging threats. By integrating AI into agricultural and food systems, the U.S. can fortify its food security through predictive analytics, automation, and intelligent decision-making. Enhancing Food Safety with AI AI-driven technologies can play a pivotal role in maintaining food safety by identifying contaminants, predicting foodborne outbreaks, and automating quality control processes. Machine learning algorithms can analyze vast amounts of data from food processing facilities to detect anomalies and prevent contamination before products reach consumers. AI-powered computer vision systems can inspect food items for defects, ensuring higher quality standards and reducing waste. Additionally, AI-enabled biosensors and predictive models can anticipate the spread of foodborne illnesses, allowing for timely interventions and improved public health outcomes. Securing the Food Supply Chain A secure food supply chain is essential to prevent disruptions from cyber threats, economic fluctuations, and geopolitical instability. AI can enhance security by improving transparency and traceability throughout the supply chain. Blockchain technology combined with AI can track food products from farm to table to reduce fraud and ensure authenticity. AI-powered demand forecasting helps balance supply and demand, minimizing food shortages and excess waste. Furthermore, AI-driven risk assessment models can identify vulnerabilities in the supply chain, allowing policymakers and industry leaders to take proactive measures against potential threats such as cyberattacks on agricultural infrastructure or disruptions due to climate change. Building Resilient Food Systems AI contributes to resilience by optimizing agricultural productivity, reducing environmental impact, and increasing adaptability to climate. Precision agriculture, powered by AI, enables farmers to make data-driven decisions about planting, irrigation, and pest control. AI-driven drones and satellite imagery can monitor crop health in real time, allowing for early intervention in cases of disease or environmental stress. Additionally, AI-powered climate models can predict extreme weather events, helping farmers and policymakers develop contingency plans to protect food production. By South Dakota State University Page 10 of 14 March 15, 2025 leveraging AI in alternative protein development and sustainable farming practices, the U.S. can reduce its reliance on traditional agricultural methods and enhance food security in the face of global challenges. The One Health framework, which acknowledges the interconnection between human, animal, and environmental health, further benefits from AI applications. Predictive modeling and monitoring systems powered by AI can forecast zoonotic diseases, enabling rapid preventive measures. This multidisciplinary approach ensures comprehensive protection of public health, agricultural resources, and ecological systems. Investments in AI technologies, supported by robust university research and dynamic public-private partnerships, promise transformative impacts on the nation's agricultural sector. Such collaboration ensures sustainable agricultural practices, economic prosperity for rural communities, robust food safety measures, and a resilient national food supply, ultimately enhancing America's food security and national security. University-Industry Collaboration for AI-Driven Food Security To fully harness the potential of AI in food security, a strong partnership between universities and industry is essential. Universities provide research expertise, data analysis capabilities, and frameworks for AI implementation, while industry contributes to rapid deployment and testing, scalable solutions, infrastructure, and investment. Therefore, collaborative efforts can accelerate innovation in AI-driven agricultural technologies, cybersecurity solutions for the food supply chain, and sustainable biomanufacturing. By fostering a culture of interdisciplinary research and public-private partnerships, the U.S. can remain at the forefront of AI-driven food security solutions. SDSU stands uniquely positioned to lead national efforts in AI-driven precision agriculture and food production. SDSU has already made significant strides in precision agriculture through cutting-edge research, educational initiatives, and strong industry partnerships. The university's precision agriculture degree program the first of its kind in the United States exemplifies its commitment to innovation, integrating advanced technologies such as AI, remote sensing, and data analytics into agriculture to enhance productivity, profitability, and economic sustainability. Leveraging our existing research infrastructure and extensive network of industry and governmental collaborators, SDSU, along with its strategic partners can drive forward the adoption and advancement of AI technologies in agriculture. Through continued investment in applied research, technology transfer, and educational outreach, SDSU is effectively positioned as a national leader in precision agriculture, delivering impactful South Dakota State University Page 11 of 14 March 15, 2025 solutions that bolster food security and strengthen agricultural supply chains across the country. Action Plan: Blueprint for AI Leadership South Dakota: A Build, Test, Deploy Action Plan This action plan outlines a strategic, phased approach Build-Test-Deploy to transform South Dakota into a national leader in AI innovation. Centered on the state s talent pool in these focal areas and catalyzed by dynamic private-sector partnerships, the plan integrates foundational research, hands-on pilot initiatives, and scalable deployment to drive economic growth and societal benefit. Phase 1: Build Cultivating Foundations and Talent The Build phase focuses on creating a robust foundation by harnessing the power of South Dakota s higher education institutions to drive foundational research, curriculum development, and talent cultivation. The aim is to equip students, researchers, and faculty with the latest AI knowledge and skills necessary to address real-world challenges in healthcare, the bioeconomy, and agriculture. Key Initiatives: Interdisciplinary Research Centers: o Objective: Develop AI research hubs that integrate computer science, engineering, security, and domain-specific expertise in agriculture, rural healthcare, and biotechnology at SDSU and sister universities, and partner organizations. o Actions: Establish industry-themed collaborative AI centers linking South Dakota universities. Secure federal and state funding, as well as private-sector investment, to support long-term research projects. Innovative Curriculum Development: o Objective: Modernize academic programs to include and integrate advanced AI modules, emphasizing industry-informed security practices and practical applications. o Actions: Design new interdisciplinary degree programs that blend technical AI skills with sector-specific knowledge. Integrate project-based learning and real-world problem-solving into coursework. South Dakota State University Page 12 of 14 March 15, 2025 Talent Cultivation and Workforce Preparation: oObjective: Build a pipeline of AI talent through training programs, internships, and research assistantships. oActions: Develop AI-talent pipelines through merit-based programs: hackathons, and industry-inspired challenges. Partner with industry to create mentorship programs and co-op opportunities, ensuring students gain hands-on experience. Phase 2: Test Pilot Programs and Innovation Hubs The Test phase leverages pilot programs, incubators, and innovation hubs to validate and refine AI solutions before broad deployment. In this phase, higher education institutions collaborate together and with private sector partners to test innovative ideas in real-world environments, creating a feedback loop that strengthens both research and practical applications. Key Initiatives: Pilot Projects and Field Trials: oObjective: Launch targeted pilot projects in key sectors such as precision agriculture, remote healthcare diagnostics, and sustainable biotechnologies. oActions: Collaborate with farms, healthcare facilities, and biotech companies to deploy AI solutions on a small scale. Utilize data analytics and real-time monitoring to assess impact, efficiency, and scalability. Innovation Hubs and Incubators: oObjective: Establish campus-based innovation hubs that serve as test beds for AI technologies. oActions: Create dedicated spaces within universities where students, researchers, and industry experts co-develop and experiment with AI prototypes. Facilitate technology transfer and knowledge exchange between academic researchers and private-sector entrepreneurs. Feedback and Iteration Mechanisms: oObjective: Implement rigorous testing protocols to refine AI solutions based on stakeholder feedback. oActions: South Dakota State University Page 13 of 14 March 15, 2025 Organize regular review panels and workshops to assess pilot outcomes. Develop iterative processes for rapid prototyping and improvement, ensuring that tested solutions are robust and market ready. Phase 3: Deploy Scaling Impact and Driving Economic Growth The Deploy phase focuses on the broad implementation and scaling of validated AI solutions, transitioning successful pilot projects into fully operational systems that drive statewide impact. In this phase, higher education institutions and private sector partners work together to commercialize innovations, promote technology adoption, and measure outcomes. Key Initiatives: Commercialization and Technology Transfer: oObjective: Transition innovative AI solutions from pilot stages to full-scale deployment across industries. oActions: Develop robust intellectual property frameworks and licensing agreements to support commercialization. Establish startup incubators and accelerators to nurture emerging AI companies, leveraging university research. Deployment and Integration: oObjective: Ensure that effective AI solutions are integrated across key sectors, enhancing operational efficiency and public welfare. oActions: Partner with state agencies and industry leaders to implement AI systems in agriculture, rural healthcare, and the bioeconomy. Leverage and invest further in infrastructure, such as high-speed broadband and cloud computing resources, to support widespread adoption. Evaluation and Continuous Improvement: oObjective: Monitor performance, ensure accountability, and drive continuous improvements in deployed AI systems. oActions: Define clear performance metrics related to research impact, educational outcomes, and sector-specific improvements. Establish an independent oversight committee that includes academic, industry, and government representatives to regularly review progress and recommend enhancements. South Dakota State University Page 14 of 14 March 15, 2025 Wrap up By following a structured Build-Test-Deploy approach, South Dakota can effectively harness the innovative power of higher education talent and catalyze transformative partnerships with the private sector. This action plan not only drives cutting-edge AI research and development but also ensures rapid, ethical, and scalable deployment of solutions that enhance agricultural productivity, improve rural healthcare, and stimulate a flourishing bioeconomy. Ultimately, this blueprint sets the stage for South Dakota to provide national AI leadership in America s heartland, contribute to national security, drive economic growth, ensure public welfare, and maintain technological dominance. Submitted by South Dakota State University Office of the Vice President for Research and Economic Development This document is approved for public dissemination. The document contains no business- proprietary or confidential information. Document contents may be reused by the government in developing the AI Action Plan and associated documents without attribution. This document was developed through an iterative synthesis process, leveraging generative AI technologies alongside human expertise and validation The structure, insights, creative inputs, expert validation, review, refinement, and approval was provided by the committee. This collaboration (human-genAI) ensured accuracy, clarity, and alignment with our objectives.",34429,4572,full_document_text,
page_text,1,"South Dakota State University Page 1 of 14 March 15, 2025 South Dakota Artificial Intelligence Leadership, Excellence, and Dominance Summary This document outlines a framework and a strategic plan for developing an integrated AI ecosystem based on guidance from the Federal Register s Request for Information on the Development of an Artificial Intelligence (AI) Action Plan. Our plan calls for a tight partnership between higher education and the private sector to position the U.S. at the forefront of innovation and ensure long-term dominance in AI. The strategy is built around a two-pronged approach - analyzing AI solutions and initiatives from both an offensive and defensive perspective: Offensive: How can we leverage AI to drive success, maximize its benefits, and stay ahead of the competition? What strategic advantages can AI offer to enhance efficiency, innovation, and decision-making? Defensive: How do we anticipate, recognize, and neutralize threats posed by adversarial AI? What safeguards, countermeasures, and resilience strategies must we implement to protect against misuse, vulnerabilities, and emerging risks? This framework ensures a balanced approach harnessing AI s potential while proactively defending against its challenges. Specifically, the initiatives outlined here stem from South Dakota s commitment to AI and cybersecurity through strategic research, innovative education, and robust industry collaboration. By uniting academia and industry, the plan will achieve transformative breakthroughs in three sectors vital to national security: healthcare, the bioeconomy, and food security all key areas the collective talent of South Dakota is well positioned to contribute to. The collaborative model will ensure rapid deployment of AI technologies into practical applications while maintaining technical leadership, ensuring safeguards, and maintaining economic success. What is unique about this plan A strong university-industry partnership is crucial for driving both the offensive advancements and defensive safeguards of AI in healthcare, biomanufacturing, and food security. But unlike regulatory safeguards or oversight, we propose safeguards by design with private sector continuously in the loop. Universities contribute cutting-edge research, talent development, and learning frameworks, while industry provides opportunities for deployment, discovery of field-induced vulnerabilities, ensuring",2438,335,page_content,page_1
page_text,2,"South Dakota State University Page 2 of 14 March 15, 2025 deployed AI models are robust to real-world applications, providing additional funding, and ensuring scalability. This synergy fosters a balanced approach where AI is leveraged for maximum impact while ensuring resilience against potential threats. Ultimately, such partnerships will not only propel technological progress but also fortify our industries against disruptions and ensuring their long-term sustainability and security in these critical fields. Focal Areas The plan will focus on achieving AI excellence and dominance in three areas which are vital to national security and economic competitiveness. Healthcare Bioeconomy/Biomanufacturing Food Security Healthcare Offensive: AI technologies offer transformative benefits in healthcare by enhancing diagnostics, treatment, and operational efficiency. Machine learning algorithms can analyze vast datasets to detect diseases like cancer at early stages to improve patient outcomes. AI-driven drug discovery accelerates the development of new treatments by predicting molecular interactions faster than traditional methods. Robotic-assisted surgeries enhance precision, reducing recovery times and minimizing risks. Additionally, AI-powered predictive analytics can optimize hospital resource allocation, reducing patient wait times and improving overall healthcare delivery. Personalized medicine, enabled by AI, tailors treatments to individual genetic profiles, ensuring more effective therapies. Defensive: Despite its advantages, AI in healthcare also presents risks and vulnerabilities. Cybersecurity threats, such as ransomware attacks on hospital systems, can compromise patient data and disrupt critical care services. Bias in AI models due to incomplete or unrepresentative datasets can lead to misdiagnoses. Over-reliance on AI-driven automation may lead to workforce displacement and reduced human oversight in critical decision-making. Additionally, regulatory and ethical challenges, including patient privacy concerns and accountability in AI-driven medical decisions, pose barriers to widespread adoption. To mitigate these risks, the healthcare sector must implement robust cybersecurity measures, ensure ethical AI governance, and maintain a balance between human expertise and AI-driven automation.",2335,300,page_content,page_2
page_text,3,"South Dakota State University Page 3 of 14 March 15, 2025 Bioeconomy/Biomanufacturing Offensive: AI is revolutionizing the bioeconomy and biomanufacturing by accelerating innovation, optimizing production processes, and driving sustainability. Machine learning models can analyze complex biological data to design more efficient microbial strains for bio-based production, leading to breakthroughs in pharmaceuticals, biofuels, and biodegradable materials. AI-driven automation enhances precision in bioprocessing, reducing waste and increasing yield efficiency. In synthetic biology, AI enables rapid protein engineering and metabolic pathway optimization, paving the way for new bioproducts with high commercial value. Predictive analytics in supply chain management ensures resource optimization, minimizes disruptions, and enhances biomanufacturing scalability. Defensive: Despite its benefits, AI adoption in bioeconomy and biomanufacturing comes with challenges and risks. Cyberattacks on AI-driven bioprocessing systems could compromise critical production pipelines, leading to supply chain vulnerabilities. Intellectual property theft and biopiracy concerns may arise. As AI accelerates genetic discoveries, ethical and regulatory concerns may become important issues. AI-generated biological designs could also be misused for biosecurity threats, necessitating stricter oversight of synthetic biology applications. Additionally, workforce displacement due to automation and potential biases in AI-driven bioprocess decisions may impact consistent access to bio-based innovations. To mitigate these risks, strong cybersecurity protocols, ethical AI governance, and regulatory frameworks are essential to ensure AI s responsible integration into bioeconomic growth and biomanufacturing. Food Security Offensive: AI can play a crucial role in advancing and ensuring food security by optimizing agricultural practices, reducing waste, and improving supply chain efficiency. Through precision farming, AI-powered sensors and drones can monitor soil health, predict weather patterns, and detect pests or diseases early, allowing farmers to make data-driven decisions that maximize crop yields and profitability. AI-driven analytics can also enhance food distribution by forecasting demand, reducing spoilage, and streamlining logistics to ensure food reaches consumers efficiently. Additionally, AI can contribute to sustainable food production by developing alternative proteins, optimizing resource use, and supporting climate-resilient farming techniques. By integrating AI across the food production and distribution chain, we can enhance global food security, minimize losses, and ensure a stable food supply for growing populations.",2742,344,page_content,page_3
page_text,4,"South Dakota State University Page 4 of 14 March 15, 2025 Defensive: If adversaries threaten or sabotage food security, the consequences could be severe, impacting public health, economic stability, and national security. A deliberate attack such as contaminating food supplies, disrupting supply chains, or cyberattacks on agricultural infrastructure could lead to food shortages, price inflation, and widespread panic. Disruptions in critical systems, such as smart farming equipment, automated food processing plants, or logistics networks, could cripple production and distribution, leaving vulnerable populations without access to essential nutrition. Additionally, bioterrorism threats, such as introducing pathogens into crops or livestock, could devastate food sources and economies. To counter these risks, governments and industries must strengthen food security with AI-driven threat detection, blockchain for supply chain transparency, and robust cybersecurity measures to safeguard critical food infrastructure from malicious attacks. Advancing U.S. Healthcare: Safer, cheaper, and smarter Chronic diseases in America are the leading cause of death and disability affecting 60% of the population with 40% having more than one chronic disease and are the leading driver of the nation s $4.1 trillion annual healthcare costs. This is exacerbated by our aging population, rising costs of healthcare, administrative burden of providing patient care, and healthcare workforce shortage, including doctors, nurses, and other professionals. AI is revolutionizing the healthcare industry, offering numerous opportunities to improve health outcomes and enhance the efficiency of the healthcare workforce. Early Detection and Treatment through Diagnostics, Prediction, and Personalization AI tools can discover abnormalities in assessments and medical imaging with remarkable accuracy and often earlier than human counterparts. Integrated into primary care AI will improve health outcomes, save costs for the patient and healthcare system, and prevent unnecessary care. Examples include heart failure, cancer, age-related macular degeneration, chronic kidney disease, etc. AI algorithms can analyze large volumes of data to predict patient outcomes and identify those at higher risk of developing certain conditions as well as personalizing treatment plans. This predictive capability allows for proactive interventions, potentially preventing diseases before they become severe, such as risk of developing opioid dependency, a flare of a chronic disease, mental health crisis, etc. Personalized treatment plans are tailored to the patient s specific needs, enhancing the effectiveness of therapies and reducing adverse effects. Combining predictive and personalization together, AI can monitor patients vital signs in real-time, alerting healthcare providers to any concerning changes. This capability is particularly beneficial for managing chronic conditions and providing care for patients in remote areas.",3012,407,page_content,page_4
page_text,5,"South Dakota State University Page 5 of 14 March 15, 2025 Healthcare Workforce Efficiency AI can facilitate better communication and collaboration among healthcare teams while also offering evidence-based recommendations and clinical decision making for diagnosis and treatment. AI-powered platforms can aggregate patient data from various sources, providing a comprehensive view of the patient's health. This holistic approach enables different specialists to collaborate more effectively, leading to better-coordinated care. AI can also suggest the most effective treatment options by analyzing patient data and comparing it with vast medical databases. This support can enhance the quality of care and ensure that patients receive the best possible treatments. Administrative, Training, and Workforce Management AI can automate routine tasks such as data entry, appointment scheduling, and medical coding, allowing healthcare professionals to focus more on direct patient care, improving job satisfaction and reducing burnout. AI-powered simulation tools provide realistic training scenarios for medical students and professionals. These tools mimic complex medical conditions, allowing trainees to practice and hone their skills in a safe environment. AI also offers personalized learning experiences, adapting to the learner's pace and knowledge level. To assist in workforce management, AI can predict staffing needs and optimize schedules, ensuring adequately staffed healthcare facilities and reducing the risk of overwork and improving patient care. To address issues proactively, AI identifies trends in workforce performance. If AI is properly integrated in healthcare, citizens will be diagnosed and treated earlier with personalized and comprehensive plans where they live. Furthermore, healthcare professionals clinical decision making and collaboration will be optimized while workforce management and administrative tasks will be streamlined. Together, this leads to efficient and less costly healthcare for patients and systems. Drug repurposing Drug repurposing is finding new applications for existing drugs to treat other health conditions. Instead of developing a new medication from scratch, researchers analyze drugs already approved by the US Food and Drug Administration (FDA) for one condition and see if they might be effective for another. Developing a new drug is a lengthy and expensive process. Drug repurposing can significantly shorten this timeline and reduce costs. Much early-stage research is unnecessary because the drugs have undergone clinical safety testing. Drug repurposing can accelerate the availability of treatments for diseases that lack effective therapies, such as metastatic cancers, Alzheimer s disease and the like. It is also valuable for rare diseases where traditional drug development may not be financially viable. Typically, the US FDA approves about",2909,405,page_content,page_5
page_text,6,"South Dakota State University Page 6 of 14 March 15, 2025 10% of the new drug applications. However, about 30% of repurposed drugs are approved by the FDA. Use of artificial intelligence (AI) in drug repurposing: Large data sets are available on FDA-approved medications, including their chemical structures, interaction targets, toxicity, side effects, clearance from the body, interactions with other drugs, and long- term effects. AI methods can analyze the available biomedical information, determine hidden patterns, and identify potential new uses of one or a mixture of drugs. This process can also incorporate genomics and bioinformatics information, patient electronic medical records, laboratory test results, and recommend personalized treatment approaches. Such individualized treatment regimens will reduce costs, decrease toxicity, and increase the efficacy of the drugs. AI and drug repurposing for cancer treatment: The use of AI in personalizing cancer treatment will significantly advance patient care. By analyzing the available data sets on FDA-approved drugs, AI can predict which medications (singly or in combination) efficiently target and deactivate the molecules necessary for cancer initiation, progression, drug resistance, and metastasis. This increases the likelihood of success in clinical trials and enables a new era in individualized, precision oncology. The AI models will predict the potential side effects for patients, ensuring that each patient receives a treatment plan tailored to their unique needs. Challenges in AI-based drug repurposing: All AI models depend on high-quality data sets from experiments with sufficient scientific rigor and reproducibility. Subsequently, predictions need validation through rigorous experimental testing and clinical trials. Finally, adapting existing medications for treating new indications requires approval by the US FDA. South Dakota State University (SDSU) has the necessary elements in place to lead a wide array of partners and stakeholders in using AI to deliver the promise of good health throughout the rural heart of America. SDSU will leverage its degree programs that feed prepared workforce into multiple health professions, its outreach networks into rural communities, and its collaborations with regional universities, and partnerships with three health systems.",2357,334,page_content,page_6
page_text,7,"South Dakota State University Page 7 of 14 March 15, 2025 Advancing the U.S. Bioeconomy: Manufacture better, cheaper, and faster. Biomanufacturing leverages biological systems to generate new products and technologies. It has historically relied on meticulous and time-consuming processes with relatively low efficiency that (i) increase the cost of research and development, (ii) take a longer time for products and technologies to reach the market, and (iii) produce low yields with less precision, often requiring more extensive downstream processing. The integration of AI in biomanufacturing brings about a transformative advancement offering unprecedented opportunities for innovation, efficiency, and precision thus overcoming these limitations. Research and development (R&D) and production systems efficiency and cost-effectiveness will increase due to application of AI enhanced monitoring, quality control, and yield improvement. AI-powered automation and robotics will streamline manufacturing processes, reduce human error and increase precision and throughput. Overall outcome improvement will be accomplished from AI's ability to analyze vast datasets with machine learning algorithms to support data-driven decision- making at every step from discovery to market. Despite these benefits, the adoption of AI in biomanufacturing faces challenges such as data quality and availability, the need for cutting edge algorithms that can be adapted to the ever-expanding types of datasets, integration with existing systems, regulatory hurdles, and the need for workforce training. AI holds transformative potential in biotechnology manufacturing, offering solutions to some of the industry's most pressing challenges while driving progress and innovation. Desired outcomes from AI adoption in biomanufacturing R&D include novel enzymatic reactions and biological regulators including drugs (arising from advanced predictive algorithms), forecasting efficacy and safety of new compounds and systems, automated screening for biological activity to accelerate the discovery phase by prioritizing compounds based on their predicted activity and reduce false positives, and genomic data analyses to identify novel enzymes in microbes allowing for more efficient biomanufacturing systems. AI platforms facilitate collaboration between researchers by integrating data from different sources and providing tools for data analysis and visualization. AI adoption will increasingly foster innovation and accelerate the pace of research that can be readily translated into successful commercialization. AI-driven outcomes in process optimization will provide continuous monitoring of bioprocesses using sensors and data analytics. Real-time adjustments will serve to maintain consistent quality and yield, predict potential quality issues before they occur, optimize fermentation and cell culture conditions to maximize product yield, and analyze historical process data to identify optimal operating parameters for production.",3023,402,page_content,page_7
page_text,8,"South Dakota State University Page 8 of 14 March 15, 2025 AI-powered automated systems streamline laboratory and production workflows increasing throughput and reducing human error in routine tasks. They enable complex manufacturing tasks with high precision (e.g. bioreactor assembly, precision measurement packaging, and quality sealing), and lead to enhanced efficiency and consistency in production lines. AI systems optimize supply chain management through demand prediction, raw materials resourcing, optimization of transportation routes and schedules, and timely delivery of perishable bioproducts, as well as ensuring scheduled maintenance to reduce downtime and enhance the reliability and efficiency of manufacturing processes. AI systems can speed up regulatory compliance and safety systems through creation and management of documentation (reports and audit trails), organizing and analyzing data and required information, speeding up the approval process for new products. Compliance monitoring will ensure Good Manufacturing Practices (GMP) and reduce waste of resources such as water, energy, and raw materials and improve recycling processes. Policies and actions are needed to improve data quality, quantity on biological manufacturing, and protect the security of many bioprocesses proprietary nature. Policies and actions can also target funding streams to generate data and alleviate the high costs associated with bioproduct innovation and development, and to improve or upgrade compatibility of legacy system hardware and software components with modern AI technologies. Developing regulatory frameworks for AI in biotechnology can reduce the barrier of initial investment for AI infrastructure and promote talent pipelines that can reduce limitations for smaller mid-west regional companies and institutions, promote training programs to meet the significant demand for skilled AI professionals, and guide ethical policies on genetic editing and use of AI to assist product or drug prioritization. Providing personnel training to work with AI systems is critical. This involves not only technical training (an understanding of AI s capabilities and limitations), but also development of robust regional infrastructure, including computing power, data storage, information technology network capabilities, resources for continuous update and retraining of models. SDSU is uniquely positioned to lead stakeholders in the development and deployment of AI applications to the bioeconomy. SDSU s location in the heart of the United States, with leading edge bioproduct R&D capabilities, and availability of abundant crop feedstocks are building blocks for securing national leadership and security.",2718,375,page_content,page_8
page_text,9,"South Dakota State University Page 9 of 14 March 15, 2025 Advancing the U.S. Food security: Safe, Secure, and Resilient. Food security is a critical component of national security and economic stability. Ensuring a safe, secure, and resilient food supply chain is essential for meeting the needs of a growing population, mitigating disruptions, and fostering sustainability. AI presents a transformative opportunity to enhance food production, optimize supply chains, and strengthen resilience against emerging threats. By integrating AI into agricultural and food systems, the U.S. can fortify its food security through predictive analytics, automation, and intelligent decision-making. Enhancing Food Safety with AI AI-driven technologies can play a pivotal role in maintaining food safety by identifying contaminants, predicting foodborne outbreaks, and automating quality control processes. Machine learning algorithms can analyze vast amounts of data from food processing facilities to detect anomalies and prevent contamination before products reach consumers. AI-powered computer vision systems can inspect food items for defects, ensuring higher quality standards and reducing waste. Additionally, AI-enabled biosensors and predictive models can anticipate the spread of foodborne illnesses, allowing for timely interventions and improved public health outcomes. Securing the Food Supply Chain A secure food supply chain is essential to prevent disruptions from cyber threats, economic fluctuations, and geopolitical instability. AI can enhance security by improving transparency and traceability throughout the supply chain. Blockchain technology combined with AI can track food products from farm to table to reduce fraud and ensure authenticity. AI-powered demand forecasting helps balance supply and demand, minimizing food shortages and excess waste. Furthermore, AI-driven risk assessment models can identify vulnerabilities in the supply chain, allowing policymakers and industry leaders to take proactive measures against potential threats such as cyberattacks on agricultural infrastructure or disruptions due to climate change. Building Resilient Food Systems AI contributes to resilience by optimizing agricultural productivity, reducing environmental impact, and increasing adaptability to climate. Precision agriculture, powered by AI, enables farmers to make data-driven decisions about planting, irrigation, and pest control. AI-driven drones and satellite imagery can monitor crop health in real time, allowing for early intervention in cases of disease or environmental stress. Additionally, AI-powered climate models can predict extreme weather events, helping farmers and policymakers develop contingency plans to protect food production. By",2768,371,page_content,page_9
page_text,10,"South Dakota State University Page 10 of 14 March 15, 2025 leveraging AI in alternative protein development and sustainable farming practices, the U.S. can reduce its reliance on traditional agricultural methods and enhance food security in the face of global challenges. The One Health framework, which acknowledges the interconnection between human, animal, and environmental health, further benefits from AI applications. Predictive modeling and monitoring systems powered by AI can forecast zoonotic diseases, enabling rapid preventive measures. This multidisciplinary approach ensures comprehensive protection of public health, agricultural resources, and ecological systems. Investments in AI technologies, supported by robust university research and dynamic public-private partnerships, promise transformative impacts on the nation's agricultural sector. Such collaboration ensures sustainable agricultural practices, economic prosperity for rural communities, robust food safety measures, and a resilient national food supply, ultimately enhancing America's food security and national security. University-Industry Collaboration for AI-Driven Food Security To fully harness the potential of AI in food security, a strong partnership between universities and industry is essential. Universities provide research expertise, data analysis capabilities, and frameworks for AI implementation, while industry contributes to rapid deployment and testing, scalable solutions, infrastructure, and investment. Therefore, collaborative efforts can accelerate innovation in AI-driven agricultural technologies, cybersecurity solutions for the food supply chain, and sustainable biomanufacturing. By fostering a culture of interdisciplinary research and public-private partnerships, the U.S. can remain at the forefront of AI-driven food security solutions. SDSU stands uniquely positioned to lead national efforts in AI-driven precision agriculture and food production. SDSU has already made significant strides in precision agriculture through cutting-edge research, educational initiatives, and strong industry partnerships. The university's precision agriculture degree program the first of its kind in the United States exemplifies its commitment to innovation, integrating advanced technologies such as AI, remote sensing, and data analytics into agriculture to enhance productivity, profitability, and economic sustainability. Leveraging our existing research infrastructure and extensive network of industry and governmental collaborators, SDSU, along with its strategic partners can drive forward the adoption and advancement of AI technologies in agriculture. Through continued investment in applied research, technology transfer, and educational outreach, SDSU is effectively positioned as a national leader in precision agriculture, delivering impactful",2860,360,page_content,page_10
page_text,11,"South Dakota State University Page 11 of 14 March 15, 2025 solutions that bolster food security and strengthen agricultural supply chains across the country. Action Plan: Blueprint for AI Leadership South Dakota: A Build, Test, Deploy Action Plan This action plan outlines a strategic, phased approach Build-Test-Deploy to transform South Dakota into a national leader in AI innovation. Centered on the state s talent pool in these focal areas and catalyzed by dynamic private-sector partnerships, the plan integrates foundational research, hands-on pilot initiatives, and scalable deployment to drive economic growth and societal benefit. Phase 1: Build Cultivating Foundations and Talent The Build phase focuses on creating a robust foundation by harnessing the power of South Dakota s higher education institutions to drive foundational research, curriculum development, and talent cultivation. The aim is to equip students, researchers, and faculty with the latest AI knowledge and skills necessary to address real-world challenges in healthcare, the bioeconomy, and agriculture. Key Initiatives: Interdisciplinary Research Centers: o Objective: Develop AI research hubs that integrate computer science, engineering, security, and domain-specific expertise in agriculture, rural healthcare, and biotechnology at SDSU and sister universities, and partner organizations. o Actions: Establish industry-themed collaborative AI centers linking South Dakota universities. Secure federal and state funding, as well as private-sector investment, to support long-term research projects. Innovative Curriculum Development: o Objective: Modernize academic programs to include and integrate advanced AI modules, emphasizing industry-informed security practices and practical applications. o Actions: Design new interdisciplinary degree programs that blend technical AI skills with sector-specific knowledge. Integrate project-based learning and real-world problem-solving into coursework.",1980,261,page_content,page_11
page_text,12,"South Dakota State University Page 12 of 14 March 15, 2025 Talent Cultivation and Workforce Preparation: oObjective: Build a pipeline of AI talent through training programs, internships, and research assistantships. oActions: Develop AI-talent pipelines through merit-based programs: hackathons, and industry-inspired challenges. Partner with industry to create mentorship programs and co-op opportunities, ensuring students gain hands-on experience. Phase 2: Test Pilot Programs and Innovation Hubs The Test phase leverages pilot programs, incubators, and innovation hubs to validate and refine AI solutions before broad deployment. In this phase, higher education institutions collaborate together and with private sector partners to test innovative ideas in real-world environments, creating a feedback loop that strengthens both research and practical applications. Key Initiatives: Pilot Projects and Field Trials: oObjective: Launch targeted pilot projects in key sectors such as precision agriculture, remote healthcare diagnostics, and sustainable biotechnologies. oActions: Collaborate with farms, healthcare facilities, and biotech companies to deploy AI solutions on a small scale. Utilize data analytics and real-time monitoring to assess impact, efficiency, and scalability. Innovation Hubs and Incubators: oObjective: Establish campus-based innovation hubs that serve as test beds for AI technologies. oActions: Create dedicated spaces within universities where students, researchers, and industry experts co-develop and experiment with AI prototypes. Facilitate technology transfer and knowledge exchange between academic researchers and private-sector entrepreneurs. Feedback and Iteration Mechanisms: oObjective: Implement rigorous testing protocols to refine AI solutions based on stakeholder feedback. oActions:",1830,233,page_content,page_12
page_text,13,"South Dakota State University Page 13 of 14 March 15, 2025 Organize regular review panels and workshops to assess pilot outcomes. Develop iterative processes for rapid prototyping and improvement, ensuring that tested solutions are robust and market ready. Phase 3: Deploy Scaling Impact and Driving Economic Growth The Deploy phase focuses on the broad implementation and scaling of validated AI solutions, transitioning successful pilot projects into fully operational systems that drive statewide impact. In this phase, higher education institutions and private sector partners work together to commercialize innovations, promote technology adoption, and measure outcomes. Key Initiatives: Commercialization and Technology Transfer: oObjective: Transition innovative AI solutions from pilot stages to full-scale deployment across industries. oActions: Develop robust intellectual property frameworks and licensing agreements to support commercialization. Establish startup incubators and accelerators to nurture emerging AI companies, leveraging university research. Deployment and Integration: oObjective: Ensure that effective AI solutions are integrated across key sectors, enhancing operational efficiency and public welfare. oActions: Partner with state agencies and industry leaders to implement AI systems in agriculture, rural healthcare, and the bioeconomy. Leverage and invest further in infrastructure, such as high-speed broadband and cloud computing resources, to support widespread adoption. Evaluation and Continuous Improvement: oObjective: Monitor performance, ensure accountability, and drive continuous improvements in deployed AI systems. oActions: Define clear performance metrics related to research impact, educational outcomes, and sector-specific improvements. Establish an independent oversight committee that includes academic, industry, and government representatives to regularly review progress and recommend enhancements.",1955,245,page_content,page_13
page_text,14,"South Dakota State University Page 14 of 14 March 15, 2025 Wrap up By following a structured Build-Test-Deploy approach, South Dakota can effectively harness the innovative power of higher education talent and catalyze transformative partnerships with the private sector. This action plan not only drives cutting-edge AI research and development but also ensures rapid, ethical, and scalable deployment of solutions that enhance agricultural productivity, improve rural healthcare, and stimulate a flourishing bioeconomy. Ultimately, this blueprint sets the stage for South Dakota to provide national AI leadership in America s heartland, contribute to national security, drive economic growth, ensure public welfare, and maintain technological dominance. Submitted by South Dakota State University Office of the Vice President for Research and Economic Development This document is approved for public dissemination. The document contains no business- proprietary or confidential information. Document contents may be reused by the government in developing the AI Action Plan and associated documents without attribution. This document was developed through an iterative synthesis process, leveraging generative AI technologies alongside human expertise and validation The structure, insights, creative inputs, expert validation, review, refinement, and approval was provided by the committee. This collaboration (human-genAI) ensured accuracy, clarity, and alignment with our objectives.",1489,200,page_content,page_14
