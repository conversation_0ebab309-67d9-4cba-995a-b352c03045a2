#!/usr/bin/env python3
"""
Frontend Server for AI Policy Analyzer Dashboard
Simple HTTP server to serve static frontend files

Author: Claude Code
Date: July 31, 2025
"""

import http.server
import socketserver
import os
import sys
from urllib.parse import urlparse

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """Custom handler to serve frontend files with proper MIME types"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.path.dirname(__file__), **kwargs)
    
    def end_headers(self):
        # Add CORS headers
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_GET(self):
        # Parse the URL
        parsed_path = urlparse(self.path)
        
        # Serve index.html for root path
        if parsed_path.path == '/' or parsed_path.path == '':
            self.path = '/index.html'
        
        # Serve dashboard.js with correct path
        elif parsed_path.path == '/dashboard/frontend/dashboard.js':
            self.path = '/dashboard.js'
        
        return super().do_GET()
    
    def do_OPTIONS(self):
        # Handle preflight requests
        self.send_response(200)
        self.end_headers()

def start_frontend_server():
    """Start the frontend development server"""

    # Import port configuration
    try:
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))
        from port_config import get_port
        PORT = get_port('frontend')
    except ImportError:
        PORT = 8028  # fallback port

    frontend_dir = os.path.dirname(__file__)
    
    # Change to frontend directory
    if os.path.exists(frontend_dir):
        os.chdir(frontend_dir)
        print(f"📁 Serving files from: {frontend_dir}")
    else:
        print(f"❌ Frontend directory not found: {frontend_dir}")
        sys.exit(1)
    
    # Start server
    try:
        with socketserver.TCPServer(("", PORT), CustomHTTPRequestHandler) as httpd:
            print(f"🚀 AI Policy Analyzer Dashboard started!")
            print(f"📱 Frontend URL: http://localhost:{PORT}")
            print(f"🔗 Direct link: http://localhost:{PORT}/index.html")
            print()
            print("📡 API Endpoints (ensure these are running):")
            print("   - Visualization API: http://localhost:5001")
            print("   - Search API: http://localhost:5002") 
            print("   - Historical API: http://localhost:5003")
            print()
            print("Press Ctrl+C to stop the server")
            print("=" * 60)
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")

if __name__ == "__main__":
    start_frontend_server()