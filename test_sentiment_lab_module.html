<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sentiment Lab Module Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .emotion-indicator {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .emotion-indicator:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .sentiment-metric {
            padding: 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px;
            margin-bottom: 15px;
        }
        
        .word-cloud-container {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border: 2px dashed #dee2e6;
            border-radius: 12px;
            padding: 20px;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-flask me-2"></i>
            Sentiment Lab Module Test
        </h1>
        
        <!-- Sentiment Analysis Controls -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cogs"></i> Sentiment Analysis Controls</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label for="sentimentAnalysisType" class="form-label">Analysis Type</label>
                        <select class="form-select" id="sentimentAnalysisType">
                            <option value="basic">Basic Sentiment (3-class)</option>
                            <option value="emotion">Emotion Analysis (8-class)</option>
                            <option value="intensity">Sentiment Intensity</option>
                            <option value="aspect">Aspect-based Sentiment</option>
                            <option value="temporal">Temporal Sentiment</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="sentimentTimeRange" class="form-label">Time Period</label>
                        <select class="form-select" id="sentimentTimeRange">
                            <option value="7d">Last 7 days</option>
                            <option value="30d">Last 30 days</option>
                            <option value="90d" selected>Last 90 days</option>
                            <option value="1y">Last year</option>
                            <option value="all">All time</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="sentimentModel" class="form-label">Sentiment Model</label>
                        <select class="form-select" id="sentimentModel">
                            <option value="bert">BERT-based</option>
                            <option value="roberta">RoBERTa</option>
                            <option value="vader">VADER</option>
                            <option value="textblob">TextBlob</option>
                            <option value="ensemble">Ensemble Model</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-primary mt-4" onclick="runSentimentAnalysis()">
                            <i class="fas fa-play"></i> Run Analysis
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Sentiment Dashboard -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0"><i class="fas fa-chart-area"></i> Sentiment Time Series</h6>
                        <div>
                            <button class="btn btn-outline-secondary btn-sm" onclick="exportSentimentData()">
                                <i class="fas fa-download"></i> Export
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="sentimentTimeSeriesChart" style="height: 400px;"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-tachometer-alt"></i> Sentiment Metrics</h6>
                    </div>
                    <div class="card-body">
                        <div id="sentimentMetrics">
                            <div class="mb-3">
                                <h6>Overall Sentiment</h6>
                                <div class="d-flex justify-content-between mb-1">
                                    <span>Positive</span>
                                    <span class="badge bg-success" id="positivePercent">58.3%</span>
                                </div>
                                <div class="progress mb-2" style="height: 8px;">
                                    <div class="progress-bar bg-success" id="positiveBar" style="width: 58.3%"></div>
                                </div>
                                <div class="d-flex justify-content-between mb-1">
                                    <span>Neutral</span>
                                    <span class="badge bg-secondary" id="neutralPercent">31.2%</span>
                                </div>
                                <div class="progress mb-2" style="height: 8px;">
                                    <div class="progress-bar bg-secondary" id="neutralBar" style="width: 31.2%"></div>
                                </div>
                                <div class="d-flex justify-content-between mb-1">
                                    <span>Negative</span>
                                    <span class="badge bg-danger" id="negativePercent">10.5%</span>
                                </div>
                                <div class="progress mb-2" style="height: 8px;">
                                    <div class="progress-bar bg-danger" id="negativeBar" style="width: 10.5%"></div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <h6>Sentiment Intensity</h6>
                                <div class="d-flex justify-content-between mb-1">
                                    <span>Average Score</span>
                                    <span class="badge bg-info" id="avgSentimentScore">0.67</span>
                                </div>
                                <div class="d-flex justify-content-between mb-1">
                                    <span>Volatility</span>
                                    <span class="badge bg-warning" id="sentimentVolatility">0.23</span>
                                </div>
                                <div class="d-flex justify-content-between mb-1">
                                    <span>Trend</span>
                                    <span class="badge bg-success" id="sentimentTrend">↗ Improving</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Emotion Analysis -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-heart"></i> Emotion Distribution</h6>
                    </div>
                    <div class="card-body">
                        <div id="emotionDistribution">
                            <div class="row text-center mb-3">
                                <div class="col-3">
                                    <div class="emotion-indicator bg-success text-white p-2 rounded mb-2">
                                        <i class="fas fa-smile"></i>
                                        <div><strong>Joy</strong></div>
                                        <small id="joyPercent">42%</small>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="emotion-indicator bg-info text-white p-2 rounded mb-2">
                                        <i class="fas fa-handshake"></i>
                                        <div><strong>Trust</strong></div>
                                        <small id="trustPercent">31%</small>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="emotion-indicator bg-primary text-white p-2 rounded mb-2">
                                        <i class="fas fa-surprise"></i>
                                        <div><strong>Surprise</strong></div>
                                        <small id="surprisePercent">12%</small>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="emotion-indicator bg-secondary text-white p-2 rounded mb-2">
                                        <i class="fas fa-meh"></i>
                                        <div><strong>Anticipation</strong></div>
                                        <small id="anticipationPercent">8%</small>
                                    </div>
                                </div>
                            </div>
                            <div class="row text-center">
                                <div class="col-3">
                                    <div class="emotion-indicator bg-warning text-white p-2 rounded">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        <div><strong>Fear</strong></div>
                                        <small id="fearPercent">18%</small>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="emotion-indicator bg-danger text-white p-2 rounded">
                                        <i class="fas fa-angry"></i>
                                        <div><strong>Anger</strong></div>
                                        <small id="angerPercent">9%</small>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="emotion-indicator bg-dark text-white p-2 rounded">
                                        <i class="fas fa-frown"></i>
                                        <div><strong>Sadness</strong></div>
                                        <small id="sadnessPercent">6%</small>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="emotion-indicator bg-purple text-white p-2 rounded" style="background-color: #6f42c1 !important;">
                                        <i class="fas fa-thumbs-down"></i>
                                        <div><strong>Disgust</strong></div>
                                        <small id="disgustPercent">4%</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-chart-pie"></i> Emotion Radar Chart</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="emotionRadarChart" style="height: 300px;"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sentiment Heatmap and Word Cloud -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-th"></i> Organization Sentiment Heatmap</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="sentimentHeatmap" style="height: 300px;"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-cloud"></i> Sentiment Word Cloud</h6>
                    </div>
                    <div class="card-body">
                        <div id="sentimentWordCloud" class="word-cloud-container">
                            <div class="text-center">
                                <i class="fas fa-cloud fa-3x text-muted mb-3"></i>
                                <p class="text-muted">Sentiment word cloud will appear here</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Controls -->
        <div class="test-section">
            <h2><i class="fas fa-cogs me-2"></i>Test Controls</h2>
            <div class="row">
                <div class="col-md-6">
                    <h6>Quick Test Analyses</h6>
                    <button class="btn btn-outline-primary me-2 mb-2" onclick="testSentimentAnalysis('basic')">
                        <i class="fas fa-chart-bar"></i> Test Basic Sentiment
                    </button>
                    <button class="btn btn-outline-success me-2 mb-2" onclick="testSentimentAnalysis('emotion')">
                        <i class="fas fa-heart"></i> Test Emotion Analysis
                    </button>
                    <button class="btn btn-outline-info me-2 mb-2" onclick="testSentimentModel('ensemble')">
                        <i class="fas fa-brain"></i> Test Ensemble Model
                    </button>
                    <button class="btn btn-outline-warning me-2 mb-2" onclick="testTimeRange('7d')">
                        <i class="fas fa-calendar"></i> Test 7-Day Analysis
                    </button>
                </div>
                <div class="col-md-6">
                    <h6>Test Results</h6>
                    <div id="testResults" class="alert alert-info">
                        <p><strong>Test Status:</strong> Ready to test sentiment functionality</p>
                        <ul>
                            <li>✅ Multi-type sentiment analysis</li>
                            <li>✅ Emotion distribution analysis</li>
                            <li>✅ Time series sentiment tracking</li>
                            <li>✅ Interactive emotion indicators</li>
                            <li>✅ Sentiment word cloud generation</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="dashboard/frontend/dashboard.js"></script>
    <script>
        // Test functions
        function testSentimentAnalysis(analysisType) {
            console.log(`🧪 Testing sentiment analysis: ${analysisType}`);
            document.getElementById('sentimentAnalysisType').value = analysisType;
            runSentimentAnalysis();
            
            const testResults = document.getElementById('testResults');
            testResults.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle"></i> Sentiment Analysis Test: ${analysisType}</h6>
                    <p>✅ Analysis type changed to ${analysisType}</p>
                    <p>✅ Sentiment processing executed</p>
                    <p>✅ Charts and metrics updated</p>
                    <p><strong>Try:</strong> Explore the emotion distribution and time series</p>
                </div>
            `;
        }
        
        function testSentimentModel(model) {
            console.log(`🧪 Testing sentiment model: ${model}`);
            document.getElementById('sentimentModel').value = model;
            runSentimentAnalysis();
            
            const testResults = document.getElementById('testResults');
            testResults.innerHTML = `
                <div class="alert alert-info">
                    <h6><i class="fas fa-brain"></i> Model Test: ${model}</h6>
                    <p>✅ Sentiment model changed to ${model}</p>
                    <p>✅ Analysis recalculated with new model</p>
                    <p>✅ Accuracy and confidence updated</p>
                    <p><strong>Try:</strong> Compare different model results</p>
                </div>
            `;
        }
        
        function testTimeRange(timeRange) {
            console.log(`🧪 Testing time range: ${timeRange}`);
            document.getElementById('sentimentTimeRange').value = timeRange;
            runSentimentAnalysis();
            
            const testResults = document.getElementById('testResults');
            testResults.innerHTML = `
                <div class="alert alert-warning">
                    <h6><i class="fas fa-calendar"></i> Time Range Test: ${timeRange}</h6>
                    <p>✅ Time period changed to ${timeRange}</p>
                    <p>✅ Time series data updated</p>
                    <p>✅ Sentiment trends recalculated</p>
                    <p><strong>Try:</strong> Switch between different time periods</p>
                </div>
            `;
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Sentiment Lab Test Page Loaded');
            
            // Initialize sentiment functionality
            if (typeof loadSentimentLab === 'function') {
                loadSentimentLab();
            } else {
                console.warn('Sentiment functions not loaded. Make sure dashboard.js is included.');
            }
        });
    </script>
</body>
</html>
