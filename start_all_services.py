#!/usr/bin/env python3
"""
AI Policy Analyzer - 统一服务启动器
启动所有必需的API服务和前端服务器
"""

import subprocess
import sys
import os
import time
import signal
from pathlib import Path

# 添加路径以便导入配置
sys.path.append('dashboard/backend')

try:
    from port_config import PORTS, SERVICES
    print("✅ 成功加载端口配置")
except ImportError:
    print("⚠️ 无法加载端口配置，使用默认值")
    PORTS = {
        'frontend': 8028,
        'visualization_api': 5001,
        'search_api': 5002,
        'historical_api': 5003,
        'analysis_api': 5004,
        'analytics_api': 5005,
        'batch_api': 5006,
        'enhanced_batch_api': 5007,
        'analysis_details_api': 5008
    }

class ServiceManager:
    """服务管理器"""
    
    def __init__(self):
        self.processes = {}
        self.backend_dir = Path('dashboard/backend')
        self.frontend_dir = Path('dashboard/frontend')
        
    def start_backend_service(self, script_name, service_name, port):
        """启动后端API服务"""
        try:
            script_path = self.backend_dir / script_name
            if not script_path.exists():
                print(f"❌ 脚本文件不存在: {script_path}")
                return None
            
            print(f"🚀 启动 {service_name} (端口 {port})...")
            
            # 启动服务进程
            process = subprocess.Popen([
                sys.executable, str(script_path)
            ],
            cwd=str(self.backend_dir),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
            )
            
            self.processes[service_name] = {
                'process': process,
                'port': port,
                'script': script_name
            }
            
            print(f"✅ {service_name} 启动成功 (PID: {process.pid})")
            return process
            
        except Exception as e:
            print(f"❌ 启动 {service_name} 失败: {e}")
            return None
    
    def start_frontend_service(self):
        """启动前端服务"""
        try:
            server_script = self.frontend_dir / 'server.py'
            if not server_script.exists():
                print(f"❌ 前端服务器脚本不存在: {server_script}")
                return None
            
            port = PORTS['frontend']
            print(f"🌐 启动前端服务器 (端口 {port})...")
            
            process = subprocess.Popen([
                sys.executable, str(server_script)
            ],
            cwd=str(self.frontend_dir),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
            )
            
            self.processes['frontend'] = {
                'process': process,
                'port': port,
                'script': 'server.py'
            }
            
            print(f"✅ 前端服务器启动成功 (PID: {process.pid})")
            return process
            
        except Exception as e:
            print(f"❌ 启动前端服务器失败: {e}")
            return None
    
    def start_all_services(self):
        """启动所有服务"""
        print("🚀 AI Policy Analyzer - 启动所有服务")
        print("=" * 60)
        
        # 定义要启动的后端服务
        backend_services = [
            {
                'script': 'visualization_api.py',
                'name': 'Visualization API',
                'port': PORTS['visualization_api']
            },
            {
                'script': 'search_endpoints.py',
                'name': 'Search API',
                'port': PORTS['search_api']
            },
            {
                'script': 'historical_data_endpoints.py',
                'name': 'Historical Data API',
                'port': PORTS['historical_api']
            },
            {
                'script': 'advanced_analytics_api.py',
                'name': 'Advanced Analytics API',
                'port': PORTS['analytics_api']
            },
            {
                'script': 'enhanced_batch_processor.py',
                'name': 'Enhanced Batch Processor',
                'port': PORTS['enhanced_batch_api']
            }
        ]
        
        # 启动后端服务
        print("\n🔧 启动后端API服务...")
        for service in backend_services:
            self.start_backend_service(
                service['script'], 
                service['name'], 
                service['port']
            )
            time.sleep(2)  # 等待服务启动
        
        # 启动前端服务
        print("\n🌐 启动前端服务...")
        self.start_frontend_service()
        
        # 等待所有服务启动
        print("\n⏳ 等待服务启动完成...")
        time.sleep(5)
        
        # 显示服务状态
        self.show_service_status()
        
        # 显示访问信息
        self.show_access_info()
    
    def show_service_status(self):
        """显示服务状态"""
        print("\n📊 服务状态:")
        print("-" * 40)
        
        for service_name, info in self.processes.items():
            process = info['process']
            port = info['port']
            
            if process.poll() is None:  # 进程仍在运行
                print(f"✅ {service_name:<20} - 运行中 (端口 {port})")
            else:
                print(f"❌ {service_name:<20} - 已停止 (端口 {port})")
    
    def show_access_info(self):
        """显示访问信息"""
        print("\n🌐 访问地址:")
        print("=" * 60)
        
        frontend_port = PORTS['frontend']
        print(f"📱 主仪表板: http://localhost:{frontend_port}")
        print(f"📱 直接访问: http://localhost:{frontend_port}/index.html")
        
        print("\n🔧 API服务端点:")
        api_services = [
            ('Visualization API', PORTS['visualization_api'], '/api/visualize/health'),
            ('Search API', PORTS['search_api'], '/api/search/health'),
            ('Historical Data API', PORTS['historical_api'], '/api/historical/health'),
            ('Advanced Analytics API', PORTS['analytics_api'], '/api/analytics/health'),
            ('Enhanced Batch Processor', PORTS['enhanced_batch_api'], '/api/batch/health')
        ]
        
        for name, port, health_endpoint in api_services:
            print(f"   {name}: http://localhost:{port}{health_endpoint}")
        
        print("\n⌨️ 按 Ctrl+C 停止所有服务")
        print("=" * 60)
    
    def stop_all_services(self):
        """停止所有服务"""
        print("\n🛑 停止所有服务...")
        
        for service_name, info in self.processes.items():
            process = info['process']
            try:
                if process.poll() is None:  # 进程仍在运行
                    print(f"🛑 停止 {service_name}...")
                    process.terminate()
                    
                    # 等待进程结束
                    try:
                        process.wait(timeout=5)
                        print(f"✅ {service_name} 已停止")
                    except subprocess.TimeoutExpired:
                        print(f"⚠️ 强制终止 {service_name}...")
                        process.kill()
                        process.wait()
                        print(f"✅ {service_name} 已强制终止")
                        
            except Exception as e:
                print(f"❌ 停止 {service_name} 时出错: {e}")
        
        print("👋 所有服务已停止")
    
    def monitor_services(self):
        """监控服务状态"""
        try:
            while True:
                time.sleep(10)  # 每10秒检查一次
                
                # 检查是否有服务意外停止
                stopped_services = []
                for service_name, info in self.processes.items():
                    if info['process'].poll() is not None:
                        stopped_services.append(service_name)
                
                if stopped_services:
                    print(f"\n⚠️ 检测到服务停止: {', '.join(stopped_services)}")
                    
        except KeyboardInterrupt:
            print("\n🛑 收到停止信号...")
            self.stop_all_services()

def main():
    """主函数"""
    manager = ServiceManager()
    
    try:
        # 启动所有服务
        manager.start_all_services()
        
        # 监控服务
        manager.monitor_services()
        
    except KeyboardInterrupt:
        print("\n🛑 收到停止信号...")
    except Exception as e:
        print(f"❌ 系统错误: {e}")
    finally:
        manager.stop_all_services()

if __name__ == '__main__':
    main()
