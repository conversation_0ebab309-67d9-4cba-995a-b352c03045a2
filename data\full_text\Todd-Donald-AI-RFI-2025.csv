﻿content_type,page_number,content,character_count,word_count,metadata_field,metadata_value
document_info,0,Document: <PERSON><PERSON><PERSON>-RFI-2025.pdf,0,0,filename,<PERSON><PERSON><PERSON>-<PERSON>-RFI-2025.pdf
metadata,0,"Comments Received in Response To: Request for Information on the Development of an Artificial Intelligence (AI) Action Plan (""Plan"")",132,19,title,"Comments Received in Response To: Request for Information on the Development of an Artificial Intelligence (AI) Action Plan (""Plan"")"
metadata,0,Adobe Acrobat Pro (64-bit) 24.5.20421,37,5,creator,Adobe Acrobat Pro (64-bit) 24.5.20421
metadata,0,Adobe Acrobat Pro (64-bit) 24.5.20421,37,5,producer,Adobe Acrobat Pro (64-bit) 24.5.20421
metadata,0,D:20250415125942-04'00',23,1,creation_date,D:20250415125942-04'00'
metadata,0,D:20250415125942-04'00',23,1,modification_date,D:20250415125942-04'00'
document_stats,0,"Total pages: 1, Total characters: 943, Total words: 160",943,160,document_stats,"pages:1,chars:943,words:160"
full_text,0,"3/2/2025 via FDMS Todd Donald The most obvious problem with AI is its ""language model training"". Which is based heavily on that which was digitized or created within the past 30 years. Unfortunately, AI cannot discern fact from fiction nor does it have any sense of what real history is . This is especially concerning because for many topics it simply regurgitates what it attempts to identify as culturally acceptable norms and accepted truth's based on popularity weighting regardless of sources. Considering the past 30 years of digital da ta they're using to train it is slanted in the direction of the social engineering progressive left, including theories, opinions, academia, and so on. Additionally, AI cannot and does not return accurate answers based on ONLY that which is evident. The pr oblem is that it's a cool tool, but it SHOULD only weigh its results first on that which we KNOW, then what we THINK not the other way around.",943,160,full_document_text,
page_text,1,"3/2/2025 via FDMS Todd Donald The most obvious problem with AI is its ""language model training"". Which is based heavily on that which was digitized or created within the past 30 years. Unfortunately, AI cannot discern fact from fiction nor does it have any sense of what real history is . This is especially concerning because for many topics it simply regurgitates what it attempts to identify as culturally acceptable norms and accepted truth's based on popularity weighting regardless of sources. Considering the past 30 years of digital da ta they're using to train it is slanted in the direction of the social engineering progressive left, including theories, opinions, academia, and so on. Additionally, AI cannot and does not return accurate answers based on ONLY that which is evident. The pr oblem is that it's a cool tool, but it SHOULD only weigh its results first on that which we KNOW, then what we THINK not the other way around.",943,160,page_content,page_1
