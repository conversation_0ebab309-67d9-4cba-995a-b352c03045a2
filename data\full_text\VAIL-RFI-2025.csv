﻿content_type,page_number,content,character_count,word_count,metadata_field,metadata_value
document_info,0,Document: VAIL-RFI-2025.pdf,0,0,filename,VAIL-RFI-2025.pdf
metadata,0,"Comments Received in Response To: Request for Information on the Development of an Artificial Intelligence (AI) Action Plan (""Plan"")",132,19,title,"Comments Received in Response To: Request for Information on the Development of an Artificial Intelligence (AI) Action Plan (""Plan"")"
metadata,0,Adobe Acrobat Pro (64-bit) 25.1.20432,37,5,creator,Adobe Acrobat Pro (64-bit) 25.1.20432
metadata,0,Adobe Acrobat Pro (64-bit) 25.1.20432,37,5,producer,Adobe Acrobat Pro (64-bit) 25.1.20432
metadata,0,D:20250414230421-04'00',23,1,creation_date,D:20250414230421-04'00'
metadata,0,D:20250414230421-04'00',23,1,modification_date,D:20250414230421-04'00'
document_stats,0,"Total pages: 7, Total characters: 12160, Total words: 1759",12160,1759,document_stats,"pages:7,chars:12160,words:1759"
full_text,0,"Submitted by: VAIL, Inc. Date: March 12, 2025 Statement: This document is approved for public dissemination. The document contains no business-proprietary or conﬁdential information. Document contents may be reused by the government in developing the AI Action Plan and associated documents without attribution. Executive Summary: Trustworthy AI Systems VAIL (V eriﬁable Artiﬁcial Intelligence Layer) is a technology company developing open-source infrastructure that enables veriﬁable and trustworthy AI systems. Our response to this RFI outlines priority policy actions that would advance America's AI leadership by creating a foundation of trust and security for AI applications via a framework that promotes cryptographic veriﬁcation methods. Our proposals ensure that AI systems can be trusted as they proliferate across critical applications in national security, healthcare, and ﬁnance. Our recommendations focus on developing technical infrastructure, AI development standards, and incentive structures that would enable organizations to verify the authenticity, integrity, and behavior of AI systems with mathematical certainty. Present Day Threats The AI industry is experiencing exponential growth, wi th thousands of new models being developed each month. This rapid proliferation creates several critical security and trust challenges: 1. Model Authenticity: How can users verify that they are using the speciﬁc AI model they intend to use, rather than a modiﬁed or compromised version? Subtle changes to a model s design made by a bad actor can have signiﬁcant 1 impact when the model s precise output informs highly consequential decisions such as where to deploy military resources or when to grant access to sensitive data. Due to the highly complex nature of modern AI systems, it is not feasible to detect such model changes through manual inspection. 2.Claims & Disclosures: Even if a user is con ﬁdent they are using the intended version of a model, how can they verify that the model does indeed have the properties it is advertised to possess? Model cards that describe model properties and test results have become a common nutrition label for providing assurances of model specs and performance; however, given the already-rapid pace of model development that we are striving to accelerate, FDA-level regulatory scrutiny would not be advisable. Rather, veriﬁcation technology can be used to validate model card claims in order to ensure accuracy and combat potential errors or misinformation. 3.Training Integrity: How can users verify that a model's training process was not compromised to introduce biases or vulnerabilities? Traditional security measures focused on model inputs and outputs may not detect when a training data pipeline has been tampered with since the model s behavior appears normal in most cases. Furthermore, this compromise may remain undetected until a critical moment. 4.Result Validation: How can users verify that AI-generated outputs truly came from the expected model and input data? A user may be working with a model that a trusted third party is hosting remotely, but how does the user know that the model result coming back to them was not intercepted and modiﬁed in transit? 5.Secure Model Collaboration: As AI systems increasingly work together, how can we ensure secure communication and veri ﬁcation between models? With the rise of AI agents, system-to-system interaction will become more ubiquitous, more fully automated, and more normalized. As this happens, agents will gain access to more sensitive information. A single vulnerable agent in a workﬂow threatens the integrity of the entire system, potentially leaking sensitive information unbeknownst to the user. 2 These challenges are particularly acute in high-stakes environments where AI systems in ﬂuence critical decisions affecting human lives, economic stability, or national security. The Role of Verifiability V eri ﬁable AI is based on cryptographic techniques that allow one party to prove to a relying party that a data computation or transformation was performed correctly. In the context of AI, these techniques include: -Assurance of training data pipeline integrity: V erifying provenance of training data and how the data was processed before being fed into a machine learning model for training. -Model ﬁngerprinting: Creating a unique model identi ﬁer for a given AI model that allows the user to verify that they are using the expected model without needing to access the model's proprietary details. -Training veri ﬁcation: Generating cryptographic proofs that a model was trained on the expected data. -Inference veri ﬁcation: Producing cryptographic evidence that a speci ﬁc output was genuinely generated by running a speci ﬁc input through a speci ﬁc model. These tools use both hardware and software technology to provide assurances analogous to those that SSL protocols give the internet, or that digital signatures give documents, but in the more complex setting of AI systems. Priority Policy Actions W e ﬁrst describe a framework, and follow that with proposed mechanisms for accelerating and bolstering it. This framework would function similarly to how the National Institute of Standards and T echnology (NIST) has established cybersecurity frameworks that are widely adopted across sectors. 1. Establish a National AI Verification Framework W e recommend the development of a national framework for AI veri ﬁcation built on common standards, protocols, and infrastructure for veri ﬁable AI. This would include: 3 AI Veri ﬁcation Standards These standards would delineate the responsibilities and obligations between model developers and model users. They would also de ﬁne requirements that systems of proof for various use cases must meet. These criteria should not be prescriptive in terms of technology, but rather should make clear the functional properties that the technology must possess. Open Reference Implementations Developing and advocating for openly available software tools that implement veri ﬁcation tooling, which can be adopted by both government and industry. Open-source tooling enables accelerated development from technical experts, as well as customizable capabilities and fully transparent functionality for government security reviews. Veriﬁable benchmarking of model performance T o foster trust and enable informed decision-making, AI model producers should establish and adhere to consistent performance standards. These standards would allow users to select the most suitable model for their speciﬁc use case. Additionally, in order to build trust, model producers should provide veri ﬁable proof of performance to substantiate claims that their models meet established benchmarks National Registry for AI systems to store veri ﬁability information A publicly accessible registry should be established to serve as a de ﬁnitive source of record for machine learning models used in AI systems. This registry would include model ﬁngerprints, metadata, performance metrics, and veri ﬁable proofs of the computations used to generate this information. 2. Create Verification-Focused Research Programs W e recommend the establishment of dedicated research programs focused on advancing veriﬁcation technologies for AI. Though veri ﬁable AI technologies have seen signi ﬁcant progress in recent years and deployable solutions are currently available, further research and development are required to support use cases that involve the most advanced AI systems. These programs should address: 4 Computational Ef ﬁciency: Current veri ﬁcation methods require signi ﬁcant computational resources. Research is needed to reduce these requirements and make veri ﬁcation more easily implementable for large-scale AI systems. As AI systems continue to make gains in computing ef ﬁciency, so too must accompanying veri ﬁcation technology. Cryptographic Algorithm Development: While current cryptography systems can generate veriﬁable proofs of model training and results, their underlying methods require optimization to feasibly run on highly complex AI systems. This optimization will come from both implementing the latest advances from research institutions and continued research into more efﬁcient algorithms for future implementation. Hardware-based approaches for Trusted Execution Environments: Enhancing hardware solutions speci ﬁcally designed to expedite veri ﬁcation processes, while preserving the integrity of the data's original processing environment. This presents opportunities for advancements in proof of execution within these environments and increased efﬁciency in hardware deployment. Organizational Integration: There is currently a lack of best practice around how to integrate veriﬁability capabilities into organizational processes and governance structures. W e need to close the gap between what is technically feasible and what organizations are able to implement. These research programs should engage both academic institutions and industry partners to ensure practical applications of the latest veriﬁcation technology. 3. Develop Incentive Structures for Verifiable AI Adoption T o accelerate the adoption of our proposed framework for veri ﬁable AI systems, we recommend creating the following incentive structures. Federal Procurement Preferences: Give preference to AI systems with veriﬁcation capabilities in federal procurement processes. 5 Tax Incentives: Provide tax bene ﬁts for organizations that implement veri ﬁable AI systems in high-risk applications. Regulatory Streamlining: Create expedited regulatory pathways for AI systems that implement robust veri ﬁcation capabilities. Grant Program: T o further encourage the development of veri ﬁable AI technologies, we recommend establishing a grant program that provides funding for research and development projects in this area. This program would be open to academic institutions, non-proﬁt organizations, and private companies. These incentives would help offset the initial costs of implementing veri ﬁcation capabilities while creating market demand for continued development of these features. 4. Creation of Office for AI Authenticity W e recommend creating a government of ﬁce dedicated towards the implementation of the aforementioned policy. The of ﬁce s responsibilities would include: - Development of partnerships with leading industry players and research institutions, focusing on government consulting, use case scoping, and solution procurement - Managing implementation of veri ﬁable AI systems - Broadening general awareness through marketing campaigns targeted at business leaders as well as the public - Leading international efforts to ensure seamless interoperability of veriﬁability solutions among allied nations This of ﬁce would have member private and public member organizations working to prioritize its initiatives. Implementation Approach W e recommend sequencing these policies as follows: 6 Phase 1: Form internal of ﬁce with founding members; establish details of the veri ﬁcation framework and research initiatives, and design incentive programs. Phase 2: Implement incentive programs, launch research initiatives, and build the registry. Phase 3: T est and deploy initial public infrastructure, and begin requiring veri ﬁcation capabilities for high-risk government AI applications. Phase 4: Scale veri ﬁcation capabilities to all government AI systems, fully deploy public infrastructure, and establish international agreements on veri ﬁcation standards. Conclusion Advancing America's AI leadership requires not just investing in raw AI capabilities, but also in the veri ﬁcation infrastructure that will enable these systems to be deployed with con ﬁdence in high-stakes environments. By establishing standards, infrastructure, and incentives for veri ﬁcation, the United States can create a competitive advantage in trustworthy AI while addressing signi ﬁcant risks associated with unveri ﬁed AI systems. This innovation offers safeguards for both users of these systems and the general American public who are directly and indirectly affected by them. 7",12160,1759,full_document_text,
page_text,1,"Submitted by: VAIL, Inc. Date: March 12, 2025 Statement: This document is approved for public dissemination. The document contains no business-proprietary or conﬁdential information. Document contents may be reused by the government in developing the AI Action Plan and associated documents without attribution. Executive Summary: Trustworthy AI Systems VAIL (V eriﬁable Artiﬁcial Intelligence Layer) is a technology company developing open-source infrastructure that enables veriﬁable and trustworthy AI systems. Our response to this RFI outlines priority policy actions that would advance America's AI leadership by creating a foundation of trust and security for AI applications via a framework that promotes cryptographic veriﬁcation methods. Our proposals ensure that AI systems can be trusted as they proliferate across critical applications in national security, healthcare, and ﬁnance. Our recommendations focus on developing technical infrastructure, AI development standards, and incentive structures that would enable organizations to verify the authenticity, integrity, and behavior of AI systems with mathematical certainty. Present Day Threats The AI industry is experiencing exponential growth, wi th thousands of new models being developed each month. This rapid proliferation creates several critical security and trust challenges: 1. Model Authenticity: How can users verify that they are using the speciﬁc AI model they intend to use, rather than a modiﬁed or compromised version? Subtle changes to a model s design made by a bad actor can have signiﬁcant 1",1576,225,page_content,page_1
page_text,2,"impact when the model s precise output informs highly consequential decisions such as where to deploy military resources or when to grant access to sensitive data. Due to the highly complex nature of modern AI systems, it is not feasible to detect such model changes through manual inspection. 2.Claims & Disclosures: Even if a user is con ﬁdent they are using the intended version of a model, how can they verify that the model does indeed have the properties it is advertised to possess? Model cards that describe model properties and test results have become a common nutrition label for providing assurances of model specs and performance; however, given the already-rapid pace of model development that we are striving to accelerate, FDA-level regulatory scrutiny would not be advisable. Rather, veriﬁcation technology can be used to validate model card claims in order to ensure accuracy and combat potential errors or misinformation. 3.Training Integrity: How can users verify that a model's training process was not compromised to introduce biases or vulnerabilities? Traditional security measures focused on model inputs and outputs may not detect when a training data pipeline has been tampered with since the model s behavior appears normal in most cases. Furthermore, this compromise may remain undetected until a critical moment. 4.Result Validation: How can users verify that AI-generated outputs truly came from the expected model and input data? A user may be working with a model that a trusted third party is hosting remotely, but how does the user know that the model result coming back to them was not intercepted and modiﬁed in transit? 5.Secure Model Collaboration: As AI systems increasingly work together, how can we ensure secure communication and veri ﬁcation between models? With the rise of AI agents, system-to-system interaction will become more ubiquitous, more fully automated, and more normalized. As this happens, agents will gain access to more sensitive information. A single vulnerable agent in a workﬂow threatens the integrity of the entire system, potentially leaking sensitive information unbeknownst to the user. 2",2156,337,page_content,page_2
page_text,3,"These challenges are particularly acute in high-stakes environments where AI systems in ﬂuence critical decisions affecting human lives, economic stability, or national security. The Role of Verifiability V eri ﬁable AI is based on cryptographic techniques that allow one party to prove to a relying party that a data computation or transformation was performed correctly. In the context of AI, these techniques include: -Assurance of training data pipeline integrity: V erifying provenance of training data and how the data was processed before being fed into a machine learning model for training. -Model ﬁngerprinting: Creating a unique model identi ﬁer for a given AI model that allows the user to verify that they are using the expected model without needing to access the model's proprietary details. -Training veri ﬁcation: Generating cryptographic proofs that a model was trained on the expected data. -Inference veri ﬁcation: Producing cryptographic evidence that a speci ﬁc output was genuinely generated by running a speci ﬁc input through a speci ﬁc model. These tools use both hardware and software technology to provide assurances analogous to those that SSL protocols give the internet, or that digital signatures give documents, but in the more complex setting of AI systems. Priority Policy Actions W e ﬁrst describe a framework, and follow that with proposed mechanisms for accelerating and bolstering it. This framework would function similarly to how the National Institute of Standards and T echnology (NIST) has established cybersecurity frameworks that are widely adopted across sectors. 1. Establish a National AI Verification Framework W e recommend the development of a national framework for AI veri ﬁcation built on common standards, protocols, and infrastructure for veri ﬁable AI. This would include: 3",1832,281,page_content,page_3
page_text,4,"AI Veri ﬁcation Standards These standards would delineate the responsibilities and obligations between model developers and model users. They would also de ﬁne requirements that systems of proof for various use cases must meet. These criteria should not be prescriptive in terms of technology, but rather should make clear the functional properties that the technology must possess. Open Reference Implementations Developing and advocating for openly available software tools that implement veri ﬁcation tooling, which can be adopted by both government and industry. Open-source tooling enables accelerated development from technical experts, as well as customizable capabilities and fully transparent functionality for government security reviews. Veriﬁable benchmarking of model performance T o foster trust and enable informed decision-making, AI model producers should establish and adhere to consistent performance standards. These standards would allow users to select the most suitable model for their speciﬁc use case. Additionally, in order to build trust, model producers should provide veri ﬁable proof of performance to substantiate claims that their models meet established benchmarks National Registry for AI systems to store veri ﬁability information A publicly accessible registry should be established to serve as a de ﬁnitive source of record for machine learning models used in AI systems. This registry would include model ﬁngerprints, metadata, performance metrics, and veri ﬁable proofs of the computations used to generate this information. 2. Create Verification-Focused Research Programs W e recommend the establishment of dedicated research programs focused on advancing veriﬁcation technologies for AI. Though veri ﬁable AI technologies have seen signi ﬁcant progress in recent years and deployable solutions are currently available, further research and development are required to support use cases that involve the most advanced AI systems. These programs should address: 4",2003,284,page_content,page_4
page_text,5,"Computational Ef ﬁciency: Current veri ﬁcation methods require signi ﬁcant computational resources. Research is needed to reduce these requirements and make veri ﬁcation more easily implementable for large-scale AI systems. As AI systems continue to make gains in computing ef ﬁciency, so too must accompanying veri ﬁcation technology. Cryptographic Algorithm Development: While current cryptography systems can generate veriﬁable proofs of model training and results, their underlying methods require optimization to feasibly run on highly complex AI systems. This optimization will come from both implementing the latest advances from research institutions and continued research into more efﬁcient algorithms for future implementation. Hardware-based approaches for Trusted Execution Environments: Enhancing hardware solutions speci ﬁcally designed to expedite veri ﬁcation processes, while preserving the integrity of the data's original processing environment. This presents opportunities for advancements in proof of execution within these environments and increased efﬁciency in hardware deployment. Organizational Integration: There is currently a lack of best practice around how to integrate veriﬁability capabilities into organizational processes and governance structures. W e need to close the gap between what is technically feasible and what organizations are able to implement. These research programs should engage both academic institutions and industry partners to ensure practical applications of the latest veriﬁcation technology. 3. Develop Incentive Structures for Verifiable AI Adoption T o accelerate the adoption of our proposed framework for veri ﬁable AI systems, we recommend creating the following incentive structures. Federal Procurement Preferences: Give preference to AI systems with veriﬁcation capabilities in federal procurement processes. 5",1878,251,page_content,page_5
page_text,6,"Tax Incentives: Provide tax bene ﬁts for organizations that implement veri ﬁable AI systems in high-risk applications. Regulatory Streamlining: Create expedited regulatory pathways for AI systems that implement robust veri ﬁcation capabilities. Grant Program: T o further encourage the development of veri ﬁable AI technologies, we recommend establishing a grant program that provides funding for research and development projects in this area. This program would be open to academic institutions, non-proﬁt organizations, and private companies. These incentives would help offset the initial costs of implementing veri ﬁcation capabilities while creating market demand for continued development of these features. 4. Creation of Office for AI Authenticity W e recommend creating a government of ﬁce dedicated towards the implementation of the aforementioned policy. The of ﬁce s responsibilities would include: - Development of partnerships with leading industry players and research institutions, focusing on government consulting, use case scoping, and solution procurement - Managing implementation of veri ﬁable AI systems - Broadening general awareness through marketing campaigns targeted at business leaders as well as the public - Leading international efforts to ensure seamless interoperability of veriﬁability solutions among allied nations This of ﬁce would have member private and public member organizations working to prioritize its initiatives. Implementation Approach W e recommend sequencing these policies as follows: 6",1539,214,page_content,page_6
page_text,7,"Phase 1: Form internal of ﬁce with founding members; establish details of the veri ﬁcation framework and research initiatives, and design incentive programs. Phase 2: Implement incentive programs, launch research initiatives, and build the registry. Phase 3: T est and deploy initial public infrastructure, and begin requiring veri ﬁcation capabilities for high-risk government AI applications. Phase 4: Scale veri ﬁcation capabilities to all government AI systems, fully deploy public infrastructure, and establish international agreements on veri ﬁcation standards. Conclusion Advancing America's AI leadership requires not just investing in raw AI capabilities, but also in the veri ﬁcation infrastructure that will enable these systems to be deployed with con ﬁdence in high-stakes environments. By establishing standards, infrastructure, and incentives for veri ﬁcation, the United States can create a competitive advantage in trustworthy AI while addressing signi ﬁcant risks associated with unveri ﬁed AI systems. This innovation offers safeguards for both users of these systems and the general American public who are directly and indirectly affected by them. 7",1170,167,page_content,page_7
