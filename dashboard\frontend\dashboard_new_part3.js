/**
 * Create sentiment trend chart
 */
function createSentimentTrendChart(data) {
    const ctx = document.getElementById('sentimentTrendChart');
    if (!ctx) return;
    
    // Destroy existing chart if it exists
    if (dashboardState.charts.sentiment) {
        dashboardState.charts.sentiment.destroy();
    }
    
    // Create new chart
    dashboardState.charts.sentiment = new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.labels,
            datasets: data.datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    align: 'end',
                    labels: {
                        boxWidth: 12,
                        usePointStyle: true,
                        pointStyle: 'circle'
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        drawBorder: false
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            },
            elements: {
                point: {
                    radius: 3,
                    hoverRadius: 5
                }
            }
        }
    });
    
    // Set up time period buttons
    setupTimePeriodButtons();
}

/**
 * Setup time period buttons for sentiment trend chart
 */
function setupTimePeriodButtons() {
    const periodButtons = document.querySelectorAll('[data-period]');
    
    periodButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            periodButtons.forEach(btn => btn.classList.remove('active'));
            
            // Add active class to clicked button
            this.classList.add('active');
            
            // Update chart based on selected period
            const period = this.getAttribute('data-period');
            updateSentimentTrendChart(period);
        });
    });
}

/**
 * Update sentiment trend chart based on selected time period
 */
function updateSentimentTrendChart(period) {
    // Sample data for different time periods
    const weekData = {
        labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        datasets: [
            {
                label: 'Positive',
                data: [65, 59, 80, 81, 72, 75, 68],
                borderColor: '#1cc88a',
                backgroundColor: 'rgba(28, 200, 138, 0.1)',
                fill: true,
                tension: 0.4
            },
            {
                label: 'Negative',
                data: [28, 32, 25, 24, 32, 34, 29],
                borderColor: '#e74a3b',
                backgroundColor: 'rgba(231, 74, 59, 0.1)',
                fill: true,
                tension: 0.4
            },
            {
                label: 'Neutral',
                data: [15, 20, 12, 14, 10, 12, 18],
                borderColor: '#36b9cc',
                backgroundColor: 'rgba(54, 185, 204, 0.1)',
                fill: true,
                tension: 0.4
            }
        ]
    };
    
    const monthData = {
        labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
        datasets: [
            {
                label: 'Positive',
                data: [70, 75, 68, 72],
                borderColor: '#1cc88a',
                backgroundColor: 'rgba(28, 200, 138, 0.1)',
                fill: true,
                tension: 0.4
            },
            {
                label: 'Negative',
                data: [30, 25, 32, 28],
                borderColor: '#e74a3b',
                backgroundColor: 'rgba(231, 74, 59, 0.1)',
                fill: true,
                tension: 0.4
            },
            {
                label: 'Neutral',
                data: [10, 15, 12, 16],
                borderColor: '#36b9cc',
                backgroundColor: 'rgba(54, 185, 204, 0.1)',
                fill: true,
                tension: 0.4
            }
        ]
    };
    
    const yearData = {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        datasets: [
            {
                label: 'Positive',
                data: [65, 59, 80, 81, 72, 75, 68, 70, 73, 75, 82, 80],
                borderColor: '#1cc88a',
                backgroundColor: 'rgba(28, 200, 138, 0.1)',
                fill: true,
                tension: 0.4
            },
            {
                label: 'Negative',
                data: [28, 32, 25, 24, 32, 34, 29, 30, 25, 24, 20, 22],
                borderColor: '#e74a3b',
                backgroundColor: 'rgba(231, 74, 59, 0.1)',
                fill: true,
                tension: 0.4
            },
            {
                label: 'Neutral',
                data: [15, 20, 12, 14, 10, 12, 18, 14, 15, 13, 10, 12],
                borderColor: '#36b9cc',
                backgroundColor: 'rgba(54, 185, 204, 0.1)',
                fill: true,
                tension: 0.4
            }
        ]
    };
    
    // Select data based on period
    let data;
    switch (period) {
        case 'week':
            data = weekData;
            break;
        case 'month':
            data = monthData;
            break;
        case 'year':
            data = yearData;
            break;
        default:
            data = weekData;
    }
    
    // Update chart data
    if (dashboardState.charts.sentiment) {
        dashboardState.charts.sentiment.data.labels = data.labels;
        dashboardState.charts.sentiment.data.datasets = data.datasets;
        dashboardState.charts.sentiment.update();
    }
}

/**
 * Load recent activity
 */
async function loadRecentActivity() {
    const recentActivity = document.getElementById('recent-activity');
    if (!recentActivity) return;
    
    try {
        // Try to get real recent activity from APIs
        const analysisResponse = await fetch(`${API_CONFIG.visualization}/analysis/results?limit=5`);
        
        if (analysisResponse.ok) {
            const data = await analysisResponse.json();
            
            if (data.success && data.results && data.results.length > 0) {
                displayRecentActivity(data.results);
                return;
            }
        }
    } catch (apiError) {
        console.log('Recent activity API not available, showing sample data');
    }
    
    // Fallback to sample data
    const sampleActivity = [
        {
            organization_name: '1Day Sooner',
            activity_type: 'analysis',
            timestamp: new Date(Date.now() - 7200000).toISOString(),
            dominant_sentiment: 'Positive',
            dominant_policy_preference: 'Self-regulation'
        },
        {
            organization_name: '3C',
            activity_type: 'analysis',
            timestamp: new Date(Date.now() - 14400000).toISOString(),
            dominant_sentiment: 'Positive',
            dominant_policy_preference: 'Co-regulation'
        },
        {
            organization_name: 'Historical Dataset',
            activity_type: 'batch',
            timestamp: new Date(Date.now() - 28800000).toISOString(),
            count: 5790,
            description: '90 FR 9088 responses processed'
        },
        {
            organization_name: 'OpenAI',
            activity_type: 'upload',
            timestamp: new Date(Date.now() - 86400000).toISOString(),
            file_type: 'Policy Document'
        },
        {
            organization_name: 'Microsoft Corporation',
            activity_type: 'analysis',
            timestamp: new Date(Date.now() - 172800000).toISOString(),
            dominant_sentiment: 'Positive',
            dominant_policy_preference: 'International Coordination'
        }
    ];
    
    displayRecentActivity(sampleActivity);
}

/**
 * Display recent activity
 */
function displayRecentActivity(activities) {
    const recentActivity = document.getElementById('recent-activity');
    if (!recentActivity) return;
    
    let activityHTML = '<div class="list-group list-group-flush">';
    
    activities.forEach(activity => {
        const timeAgo = getTimeAgo(activity.timestamp);
        let activityContent = '';
        
        switch (activity.activity_type) {
            case 'analysis':
                activityContent = `
                    <div>
                        <strong>${activity.organization_name}</strong> - Analysis completed
                        <br><small class="text-muted">Sentiment: ${activity.dominant_sentiment || 'N/A'}, Policy: ${activity.dominant_policy_preference || 'N/A'}</small>
                    </div>
                `;
                break;
            case 'batch':
                activityContent = `
                    <div>
                        <strong>${activity.organization_name}</strong> - ${activity.count} analyses completed
                        <br><small class="text-muted">${activity.description}</small>
                    </div>
                `;
                break;
            case 'upload':
                activityContent = `
                    <div>
                        <strong>${activity.organization_name}</strong> - Document uploaded
                        <br><small class="text-muted">${activity.file_type || 'Document'}</small>
                    </div>
                `;
                break;
            default:
                activityContent = `
                    <div>
                        <strong>${activity.organization_name}</strong> - Activity recorded
                    </div>
                `;
        }
        
        activityHTML += `
            <div class="list-group-item d-flex justify-content-between align-items-center">
                ${activityContent}
                <small class="text-muted">${timeAgo}</small>
            </div>
        `;
    });
    
    activityHTML += '</div>';
    recentActivity.innerHTML = activityHTML;
}

/**
 * Helper function to calculate time ago from timestamp
 */
function getTimeAgo(timestamp) {
    try {
        const date = new Date(timestamp);
        const now = new Date();
        const diffMs = now - date;
        const diffMinutes = Math.floor(diffMs / (1000 * 60));
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffDays = Math.floor(diffHours / 24);
        
        if (diffMinutes < 60) {
            return diffMinutes <= 1 ? 'Just now' : `${diffMinutes} minutes ago`;
        } else if (diffHours < 24) {
            return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
        } else if (diffDays < 7) {
            return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
        } else {
            return date.toLocaleDateString();
        }
    } catch (e) {
        return 'Recently';
    }
}

/**
 * Setup file upload functionality
 */
function setupFileUpload() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    
    if (!uploadArea || !fileInput) return;
    
    // Drag and drop events
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        handleFiles(files);
    });
    
    // Click to upload
    uploadArea.addEventListener('click', function() {
        fileInput.click();
    });
}

/**
 * Handle file selection
 */
function handleFileSelection(e) {
    const files = e.target.files;
    handleFiles(files);
}
