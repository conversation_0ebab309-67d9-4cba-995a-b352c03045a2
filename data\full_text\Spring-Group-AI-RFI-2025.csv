﻿content_type,page_number,content,character_count,word_count,metadata_field,metadata_value
document_info,0,Document: Spring-Group-AI-RFI-2025.pdf,0,0,filename,Spring-Group-AI-RFI-2025.pdf
metadata,0,"Comments Received in Response To: Request for Information on the Development of an Artificial Intelligence (AI) Action Plan (""Plan"")",132,19,title,"Comments Received in Response To: Request for Information on the Development of an Artificial Intelligence (AI) Action Plan (""Plan"")"
metadata,0,Adobe Acrobat Pro (64-bit) 25.1.20432,37,5,creator,Adobe Acrobat Pro (64-bit) 25.1.20432
metadata,0,Adobe Acrobat Pro (64-bit) 25.1.20432,37,5,producer,Adobe Acrobat Pro (64-bit) 25.1.20432
metadata,0,D:20250415130934-04'00',23,1,creation_date,D:20250415130934-04'00'
metadata,0,D:20250421143238-04'00',23,1,modification_date,D:20250421143238-04'00'
document_stats,0,"Total pages: 15, Total characters: 25963, Total words: 4015",25963,4015,document_stats,"pages:15,chars:25963,words:4015"
full_text,0,"1 2 A Comprehensive Set of Policy Recommendations for Artiﬁcial Intelligence Integration into Education The SPRING Group March 2025 Prepared for: The White House Authors: <AUTHORS>
page_text,1,1,1,1,page_content,page_1
page_text,2,"2 A Comprehensive Set of Policy Recommendations for Artiﬁcial Intelligence Integration into Education The SPRING Group March 2025 Prepared for: The White House Authors: <AUTHORS>
page_text,3,3 1. Table of Contents 1. Table of Contents 3 2. Executive Summary 4 3. Introduction 5 4. Status Quo Analysis 6 4.1 Pros of AI Use in Education 6 4.2 Cons of AI Use in Education 7 5. Curriculum-based Policy Recommendations 9 5.1 Mandatory AI Education Training for Teachers 9 5.2 Mandatory AI Literacy Workshops for Students 10 6. Other Policy Recommendations 12 6.1 Student Privacy and Data Protection 12 6.1.1 AI Transparency 12 6.1.2 AI Privacy and Data Protection for Students 12 6.1.3 AI Certiﬁcation and Testing 12 6.2 AI Ethics 13 6.2.1 Bias Audits 13 6.2.2 Safety Compliance 13 6.2.3 Accessibility Enhancements 13 6.3 School Partnerships 14 6.3.1 AI Partnership for Schools 14 6.3.2 Teacher AI Training 14 7. Conclusion 15,730,124,page_content,page_3
page_text,4,"4 2. Executive Summary In the past few years, the rise of Artiﬁcial Intelligence (AI) has become a powerful force that cannot be underestimated. The varied uses and purposes of AI technology have pervaded everyday life, from its usage in the classroom to national security concerns. Furthermore, as AI brings many beneﬁts to society, it also brings about conversations regarding its ethical use. Many wonder how AI can be implemented in a way that will not only offer technological innovation and forward-thinking into society but also be used safely to minimize its potential threats. Artiﬁcial intelligence and its progress is a topic of importance to the SPRING Group. As an organization composed of students who are young adults, we will be the next generation that has to interact with various AI technologies as it is further integrated into our greater society. Therefore, we are especially concerned about the methods in which AI will be implemented, for these decisions made by the federal government will impact our own daily lives as AI continues to gain traction. As part of our continued goal to highlight youth viewpoints on issues of concern to them, SPRING seeks to bring the unique perspectives of students into national policies such as artiﬁcial intelligence. This brief starts with an analysis of the status quo concerning AI technologies and their relationships in the ﬁeld of education, then provides curriculum-based policy recommendations and external policy recommendations to further the development of AI in a safe yet engaging manner.",1562,249,page_content,page_4
page_text,5,"5 3. Introduction AI has greatly changed education in the United States. With abilities spanning from the creation of lesson plans to the generation of a fully ﬂeshed essay in a matter of minutes, its implementation has caused lots of discussion regarding student utilization, the role of teachers, and how it promotes equity in learning. According to a global survey by the Digital Education Council, 86 percent of students reported using artiﬁcial intelligence in their studies. 1 As a result, concerns have been raised over academic integrity and the overall net beneﬁt of utilizing artiﬁcial intelligence in the classroom. While assigned work is meant to bring out critical thinking and direct application of the topics learned, AI tools have the power to completely mitigate this intention by providing instant answers and even removing the need for student effort. It is imperative that students can use AI in a way that beneﬁts their knowledge while still being mindful of the guidelines in place and meeting the cor e objectives. AI has also signiﬁcantly impacted teachers. Instead of seeing the technology as a replacement, AI can be made for support that enhances their methods in the classroom. The tool can increase eﬃciency, ensure that all standards are being met, and overcome any challenges along the way. The educational divide in the United States is necessary to consider. The divide unfortunately affects millions, as certain groups face disparities in their access to education. Factors include socioeconomic status, family background, location, ethnicity, and gender. AI has features that can address or supplement some of these issues, such as assisting learning for students in institutions with lower faculty counts. 1 Digital Education Council, 2024",1775,279,page_content,page_5
page_text,6,"6 4. Status Quo Analysis 4.1 Pros of AI Use in Education One of the most important ways AI is used in education is in personalized learning systems, which use adaptable algorithms to change the lessons based on each student's level of skill, preferred learning style, and speed. The systems keep an eye on how students interact with each other, their behavior, and their performance so that they can change parts of the curriculum immediately, without needing time to adjust. This way, each student gets individualized teaching instead of a standard, one-size-ﬁts-all approach. By giving each student personalized material to help them learn, adaptive learning technologies can boost retention by up to 30% 2 . Intelligent teaching systems (ITS) are another important step forward in education that is being driven by AI. These methods work like one-on-one lessons because they give immediate and detailed feedback, break down harder ideas, and ﬁnd trouble spots in real time. For instance, Carnegie Learning's AI-powered tutor, MATHia, has shown measurable improvements in math skills, with the average student showing a 16 percentile point improvement compared to traditional classroom teaching 3 . AI-powered tutors give students instant feedback on how they can improve, so they can change their strategies right away. A study that compared students using AI-assisted learning tools to a control group found that the students in the experimental group who used the AI-assisted tool did better on writing projects than the students in the control group 4 . Aside from teaching, AI has also been used to handle administrative tasks, which gives teachers more time to focus on their teaching duties. AI-driven grading systems, like the ones in Gradescope, use natural language processing (NLP) and machine learning to look over student work. This makes grades more uniform and faster 5 . Using these automated systems can cut down on grading time by a large amount, giving teachers more time to work on developing lessons and teaching students 6 . 6 Worcester Polytechnic Institute, 2024 5 Columbia University, 2024 4 Song, 2023 3 Boland, 2021 2 Intersog, 2024",2161,345,page_content,page_6
page_text,7,"7 One of the best things about AI-powered learning tools is that they can get students more interested in learning. Studies show that students are more motivated and involved in class when the tools they are using are tailored to their speciﬁc cognitive needs 7 . By making sure that instructional content stays relevant and easy to ﬁnd, AI stops students from losing interest, which can happen when the pace of the teacher doesn't meet the level of understanding of the students. Programs like Estonia's national AI reading program show how AI-driven lessons can help close the achievement gap 8 . Working with leading U.S. tech companies, the program wants to add AI-driven classes to high school to give kids more advanced digital skills that will help them get jobs in tech-based economies. This model shows how AI can change education by getting rid of barriers like distance and money that make it hard to provide eﬃcient teaching. 4.2 Cons of AI Use in Education The integration of artiﬁcial intelligence (AI) in educational institutions presents numerous advantages, although it has also engendered certain issues that require thorough examination. Issues include academic dishonesty, the erosion of critical thinking abilities, and the perpetuation of biases throughout generations. They provide signiﬁcant hazards to the educational sector. The incidence of students engaging in academic dishonesty on projects has escalated due to the utilization of AI technologies such as ChatGPT, which can generate text that resembles human writing. Sixty-three percent of educators reported having to reprimand pupils for utilizing generative AI to cheat during the 2023 24 academic year 9 10 . This represented a substantial rise compared to the prior year. AI technologies enhance the learning experience; yet, they have also elicited concerns regarding academic integrity and the genuine comprehension of students who utilize them 11 . The use of AI-generated content may impede critical thinking and proﬁcient writing skills. Students may rely on AI tools for idea generation and content creation, thus diminishing their critical thinking skills and originality. Educators are concerned that 11 University of Missouri, 2023 10 EducationWeek, 2024 9 National Education Union, n.d. 8 e-Estonia, 2025 7 PubMed Central, 2023",2323,360,page_content,page_7
page_text,8,"8 such reliance may result in a generation of kids deﬁcient in essential cognitive abilities 12 . The ease with which students can complete assignments without a compr ehensive comprehension of the subject renders AI a valuable tool for academic dishonesty. The educational process is obstructed, preventing teachers from accurately assessing each student's true capabilities. Schools can safeguard academic integrity by implementing honor codes and utilizing AI detection techniques to identify content generated by artiﬁcial intelligence 13 . Establishing AI-operated assessment tools has proven to be challenging. These methodologies may overlook the nuances of individual expression and creativity, perhaps resulting in inaccurate assessments of student work. AI systems have been demonstrated to perpetuate biases inherent in their training data. This renders learning inequitable for certain pupils and may place them at a disadvantage 14 . Frequently, AI assessment systems perpetuate biases due to the presence of inherent biases in the training data 15 . This may result in inequitable assessments and exacerbate disparities 16 . To address this issue, developers must ensure that AI systems are trained on diverse and representative datasets 17 . Moreover, maintaining the currency of AI algorithms and scrutinizing for bias can enhance the accuracy and equity of ratings. AI has the potential to revolutionize education, but it also presents signiﬁcant challenges, such as facilitating cheating, undermining critical thinking, and disseminating biased information. Policymakers must address these issues. The education sector can leverage AI while mitigating its drawbacks, provided effective regulations are established. This will ensure that technology enhances learning rather than detracting from it. 17 Northern Illinois University, 2024 16 Harvard Business Review, 2019 15 Covisian, 2024 14 Mobile Guardian, n.d. 13 Stanford Teaching Commons, 2023 12 The Guardian, 2025",1986,285,page_content,page_8
page_text,9,"9 5. Curriculum-based Policy Recommendations Given the rapidly evolving nature of artiﬁcial intelligence from a social and political perspective, it is essential to develop policies from an educational standpoint that ensure proper usage of artiﬁcial intelligence. In a Pew Research study, the usage of AI for schoolwork has gone from 13% in 2023 to roughly 26% as of 2025. To advance education quality and promote high-quality learning structures that are reﬂective of a diverse range of educational environments, reforms from an educational standpoint must be addressed regarding AI usage for both teachers and students. 5.1 Mandatory AI Education Training for Teachers Introducing a mandate on artiﬁcial intelligence literacy training for educators is an effective means of handling the increasing rate of AI mishandling from an educational standpoint. Though artiﬁcial intelligence is an effective form of streamlining administrative tasks and everyday operations, teachers must learn the ethicality of artiﬁcial intelligence and understand the potential implications and biases within its structure. In a survey of 205 educators by Teach Plus, a nonproﬁt organization, 92% stated that artiﬁcial intelligence has the potential to be helpful to their teaching methods, but only half knew how to use it effectively and eﬃciently. Introducing a policy that comprehensively educates teachers on how to utilize AI effectively and eﬃciently without sacriﬁcing the quality of their work will signiﬁcantly enhance the learning experience of students and the management of time for learning fr om a teacher's perspective. On top of a required AI brieﬁng upon employment at an educational institution, training will be mandated to occur once every two academic school years, but recommended once every academic school year, to remain cognisant to the rapidly evolving nature of AI. This training will include 3 comprehensive subsections to be administered to educators and reviewed thoroughly. The ﬁrst part of the training would encompass the basic structural nature of artiﬁcial intelligence, reviewing how it works from an algorithmic standpoint, and its potential uses in a daily learning environment, like scheduling tasks, planning the organizational structure of daily learning, developing creative approaches to learning activities, and displaying methods to engage with students to optimize wellbeing and educational success. The second part of the training will cover the key administrative issues with AI. These include but are not limited to: AI hallucinations, which is when AI chatbots conjure up untrue information.",2624,389,page_content,page_9
page_text,10,"10 AI alignment, which is when AI does not perform what it is instructed to do in a highly subtle manner. AI runaway, which is when the AI chatbot develops answers that stray from the request of the user. AI discrimination, which is when AI provides information that is discriminatory due to statistical bias and discriminatory skews in online research. AI lock-in problem, which is when AI narrows the context of the user s prompt and neglects the broader picture of the situation/circumstance/prompt that the user provides. These issues remain imperative for educators to learn before overutilization AI in ways that directly harm the well-being and educational attainment of their students. The third and last unit will be educating teachers on how to identify AI mishandling within students while making an appropriate response to it. On top of providing teachers with methods to promote the ethical usage of AI within the classroom, teachers will learn how to identify the usage of AI within a student s work. Some examples of detection include comparing the style of homework submitted by a student to previous assignments, with the AI version having higher levels of vocabulary, an overly formal tone with a lack of creativity, and a lack of personal information to attribute a piece of writing to a speciﬁc writer. However, more than simply identifying AI, this unit will educate teachers on how to appropriately respond and teach students not to utilize AI. When caught using AI, teachers will approach the situation holistically, meeting with the student to identify areas of concern and developing a comprehensive plan to ensure the student's success in the future. After more than one disciplinary infraction related to AI usage, a higher level of disciplinary action will occur. 5.2 Mandatory AI Literacy Workshops for Students However, it is further essential for students to be educated on the ethical usage of AI that beneﬁts their education while enhancing their knowledge of using AI. Exposing students to technological innovations from a young age will increase a student s career development and ability to succeed. To ensure AI is as beneﬁcial as possible to a student, a comprehensive AI literacy workshop will occur once a year with an emphasis on optimizing a student s time, learning, and socioemotional development. Though similar to the teacher s AI teaching curriculum, the workshops for students will be heard to the receiving end of the education with 4 overviews of AI and ways to be used from a student perspective.",2547,416,page_content,page_10
page_text,11,"11 Though the potential uses of AI are growing rapidly, students will be taken through an overview of AI and how it works, then how it can be used from a student perspective. Examples include developing comprehensive homework scheduling structures, providing unit or curriculum explanations, developing study methods that are catered to the user, and more.",356,56,page_content,page_11
page_text,12,"12 6. Other Policy Recommendations This section focuses on recommendations outside of altering the school curriculum. Due to concerns about AI s security and use of data, this section mainly tackles recommendations regarding AI privacy and data protection, AI ethics, and partnerships with schools. 6.1 Student Privacy and Data Protection 6.1.1 AI Transparency AI trust is essential for its use in schools and education. Speciﬁcally, AI tools should be able to provide clear, understandable explanations for their decisions, predictions, and recommendations to whoever uses them, which in this case would be students. Furthermore, AI models should be fully transparent so that teachers, students, and parents can understand how AI impacts education. This sort of transparency is necessary to create trust between AI and its use in school and further education and to ensure that students are not only going to be able to learn from AI, but they won t be able to solely rely on it. 18 This means mandating transparency reports on how certain models function and the data used to train them to ensure that the model and the output information are unbiased and can complement learning rather than replace it. 6.1.2 AI Privacy and Data Protection for Students Data protection is one of the most dangerous parts of AI use, with data potentially being used to create internal biases. Thus, a framework should be created and designed to protect against student data collection, storage, and use by AI systems. This should include policies limiting the amount of data being collected, a clear opt-in and opt-out mechanism about data collection, and encryption standards to protect student data from misuse or cyberattacks. Any sort of regulation should also follow the Family Educational Rights and Privacy Act (FERPA), which acts as the current student privacy regulation. 19 6.1.3 AI Certiﬁcation and Testing All AI education tools should meet certain safety, reliability, and transparency standards before being deployed in schools to ensure that it is ready to be used at a student level. This can be similar to FDA approval but more focused on AI safety within education use. 19 U.S. Department of Education, n.d. 18 Lawton, 2024",2226,358,page_content,page_12
page_text,13,"13 These AI systems should be aligned with educational goals and testing for tr ansparency is crucial to maintain privacy laws and frameworks both in parallel with frameworks in this paper as well as outside acts. An easy way to determine if AI matches certain standards would be to create a certiﬁcation program that performs independent veriﬁcation on AI tools before they are used within schools. Certiﬁcation programs can provide an approval if they match high safety, reliability, and transparency standards that protect students and others who use the AI. 6.2 AI Ethics 6.2.1 Bias Audits AI use has always been highlighted with critiques about the ethics of AI and how it manages biases. Due to the way AI is trained on past data, it can often mirror certain stereotypes and biases that are found in the data and perpetuate or even amplify them during use, which is detrimental when used in school. 20 Thus, all AI educational tools should have independent audits performed on them to detect and mitigate biases in algorithms, particularly those that could disproportionately affect marginalized students. Through methods like ethics checklists and frameworks, independent auditors can focus on different dimensions of ethical AI, such as fairness, explainability, and safety, and evaluate the AI through those means before they are used in the educational system. 6.2.2 Safety Compliance Most, if not all, educational technology tools follow stricter guidelines than tools offered to the broader public, mainly because the people who use educational tools are mostly young students and children who are easily inﬂuenced. Regulations like FERPA and COPPA, as well as other state laws, are required for tools used in the educational sphere. 21 Educational AI tools should follow in these footsteps and be required to adhere to those guidelines and even more that speciﬁcally tailor to AI. This also includes clear penalties for companies that violate any of those r egulations while simultaneously encouraging the development of AI tools speciﬁcally for education. 6.2.3 Accessibility Enhancements A wide range of students often need AI in their learning, whether they simply need some help with tutoring services or whether they are under special education and need tools 21 Mann et. al., 2024 20 Greene-Santos, 2024",2322,368,page_content,page_13
page_text,14,"14 to help in their unique learning needs, often requiring specialized educational programs. 22 Currently, technologies like assistive technology in speech-to-text tools are already used to help with special education and can be further augmented with AI. AI in classrooms should include assistive technologies that enhance accessibility for students with various conditions, such as text-to-speech and vice-versa tools, adaptive learning tools, and specialized AI tutor tools. However, these AI tools must meet standards under the Americans with Disability Act to be used in schools for such use. 6.3 School Partnerships 6.3.1 AI Partnership for Schools AI use is commonly documented by the federal government regarding different use cases from student aid to create public policy analysis. With this high oversight by the government regarding different uses of AI, the government should create a federal-private partnership regarding AI use in education to create a connection between the government, tech companies, and schools to develop safe and effective AI tools. This partnership should include strict compliance standards for AI systems and models while ensuring accessibility for underfunded schools. Federal funding for underfunded schools to access such AI tools can also be involved and be a part of these partnerships while driving innovation and maintaining high standards from AI so that students can use those models safely. 6.3.2 Teacher AI Training AI should be understood and utilized by students but teachers as well. Teachers will need proper training to fully understand and utilize AI to complement their teaching so that they can ensure students can properly learn using AI without having it do all of the work and take away from the learning. Thus, there should be a federal program dedicated to funding and creating specialized AI literacy and training programs for teachers to ensure they understand AI risks, limitations, and potential. This training should also help teachers integrate AI responsibly into their classrooms. Teachers must be able to learn AI to use it and teach it effectively and safely. 22 21K Schools, 2025",2155,331,page_content,page_14
page_text,15,"15 7. Conclusion Artiﬁcial intelligence is one of the most revolutionary technologies in recent memory, and it is more prevalent than ever before in American history. As a youth-centered policy research team, the SPRING Group aimed to investigate the current state of AI technology in the classroom, as well as potential policy goals the White House could implement to ensure a safer, more eﬃcient, and equitable future for American students. Artiﬁcial intelligence has already transformed the United States educational workplace, from improving instructional delivery in the form of artiﬁcial tutors, streaming administrative eﬃciency in the form of AI grading systems, and even personalizing individual student assistance with personalized, adaptive learning systems. These systems increase student engagement and optimize instructor eﬃciency, leading to an overall positive learning environment. This isn t just a theory; countries around the world are churning out initiatives to expand AI literacy and integrate AI-driven curricula, and they re showcasing positive results. At the same time, AI perpetuates many negative impacts, as well, such as a rise in academic dishonesty, the erosion of critical thinking skills, and the exacerbation of hidden biases against marginalized students. However, policies can be implemented to strike a balance between the potentially life-changing beneﬁts and the system-transforming negatives artiﬁcial intelligence can have in the classroom. AI is already being used in the classroom by both students and teachers, so the integration of mandatory AI education training for both constituencies will help foster a healthy technological environment. Beyond the classroom, privacy and ethical policies must be rigorously implemented. AI models used in education must meet rigorous standards for transparency and security to avoid biases and ensure that student data remains safe. AI models must pass stringent security and safety standards and remain accessible to all students, regardless of special educational needs. Finally, governments must partner with schools to ensure the equitable and safe distribution of AI resources. The SPRING Group remains vehemently committed to the fostering of a safe, reliable artiﬁcial intelligence national framework to usher in the next generation of American leadership in AI. We believe AI leadership begins with our youngest generation, and creating safe and innovative policies today ensures that America will stand out on the international stage of artiﬁcial intelligence development.",2566,372,page_content,page_15
