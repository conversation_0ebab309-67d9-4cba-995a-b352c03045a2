#!/usr/bin/env python3
"""
Search and Filter API Endpoints for AI Policy Analyzer Dashboard
Flask API wrapper for search and discovery functionality

Author: Claude Code
Date: July 31, 2025
"""

import json
import os
import sys
from typing import Dict, List, Any, Optional
from flask import Flask, jsonify, request
from flask_cors import CORS

# Add path for imports
sys.path.append('.')

# Import search components
from search_api import SearchIndexManager, SearchEngine, initialize_search_system

app = Flask(__name__)
CORS(app)

# Initialize search system
search_index, search_engine = initialize_search_system()

@app.route('/api/search', methods=['GET', 'POST'])
def search_documents():
    """Perform full-text search across documents"""
    try:
        if request.method == 'POST':
            data = request.json or {}
            query = data.get('query', '')
            filters = data.get('filters', {})
            limit = data.get('limit', 50)
        else:
            query = request.args.get('query', '')
            filters = {}
            if request.args.get('organization_type'):
                filters['organization_type'] = request.args.get('organization_type')
            if request.args.get('sector'):
                filters['sector'] = request.args.get('sector')
            if request.args.get('document_type'):
                filters['document_type'] = request.args.get('document_type')
            limit = int(request.args.get('limit', 50))
        
        if not query.strip():
            return jsonify({'error': 'Search query is required'}), 400
        
        results = search_engine.full_text_search(query, filters, limit)
        
        return jsonify({
            'query': query,
            'filters': filters,
            'total_results': len(results),
            'results': results
        })
        
    except Exception as e:
        return jsonify({'error': f'Search failed: {str(e)}'}), 500

@app.route('/api/search/similar/<document_id>', methods=['GET'])
def find_similar_documents(document_id):
    """Find documents similar to the given document"""
    try:
        pattern_types = request.args.getlist('pattern_types') or None
        limit = int(request.args.get('limit', 20))
        
        results = search_engine.similarity_search(document_id, pattern_types, limit)
        
        return jsonify({
            'reference_document_id': document_id,
            'pattern_types': pattern_types,
            'total_results': len(results),
            'similar_documents': results
        })
        
    except Exception as e:
        return jsonify({'error': f'Similarity search failed: {str(e)}'}), 500

@app.route('/api/search/filter', methods=['POST'])
def filter_documents():
    """Advanced filtering based on analysis categories"""
    try:
        data = request.json or {}
        filters = data.get('filters', {})
        limit = data.get('limit', 50)
        
        # Create a structured search based on filters
        results = []
        
        # If we have analysis-specific filters, use structured search
        analysis_filters = {k: v for k, v in filters.items() if k in [
            'analysis_category', 'analysis_subcategory', 'keyword', 'min_frequency'
        ]}
        
        if analysis_filters:
            # Use analysis index for structured search
            import sqlite3
            conn = sqlite3.connect(search_index.db_path)
            cursor = conn.cursor()
            
            base_query = '''
                SELECT DISTINCT d.id, d.organization_name, d.organization_type, d.sector,
                       d.document_type, d.submission_date,
                       COUNT(ai.keyword) as match_count
                FROM documents d
                JOIN analysis_index ai ON d.id = ai.document_id
                WHERE 1=1
            '''
            
            params = []
            
            if filters.get('analysis_category'):
                base_query += " AND ai.analysis_category = ?"
                params.append(filters['analysis_category'])
            
            if filters.get('analysis_subcategory'):
                base_query += " AND ai.analysis_subcategory = ?"
                params.append(filters['analysis_subcategory'])
            
            if filters.get('keyword'):
                base_query += " AND ai.keyword LIKE ?"
                params.append(f"%{filters['keyword']}%")
            
            if filters.get('min_frequency'):
                base_query += " AND ai.frequency >= ?"
                params.append(filters['min_frequency'])
            
            # Document-level filters
            if filters.get('organization_type'):
                base_query += " AND d.organization_type = ?"
                params.append(filters['organization_type'])
            
            if filters.get('sector'):
                base_query += " AND d.sector = ?"
                params.append(filters['sector'])
            
            base_query += '''
                GROUP BY d.id, d.organization_name, d.organization_type, d.sector,
                         d.document_type, d.submission_date
                ORDER BY match_count DESC, d.organization_name
                LIMIT ?
            '''
            params.append(limit)
            
            cursor.execute(base_query, params)
            db_results = cursor.fetchall()
            
            for row in db_results:
                results.append({
                    'document_id': row[0],
                    'organization_name': row[1],
                    'organization_type': row[2],
                    'sector': row[3],
                    'document_type': row[4],
                    'submission_date': row[5],
                    'match_count': row[6]
                })
            
            conn.close()
        
        else:
            # Use basic document filtering
            conn = sqlite3.connect(search_index.db_path)
            cursor = conn.cursor()
            
            base_query = '''
                SELECT id, organization_name, organization_type, sector,
                       document_type, submission_date
                FROM documents
                WHERE 1=1
            '''
            
            params = []
            
            if filters.get('organization_type'):
                base_query += " AND organization_type = ?"
                params.append(filters['organization_type'])
            
            if filters.get('sector'):
                base_query += " AND sector = ?"
                params.append(filters['sector'])
            
            if filters.get('document_type'):
                base_query += " AND document_type = ?"
                params.append(filters['document_type'])
            
            base_query += " ORDER BY organization_name LIMIT ?"
            params.append(limit)
            
            cursor.execute(base_query, params)
            db_results = cursor.fetchall()
            
            for row in db_results:
                results.append({
                    'document_id': row[0],
                    'organization_name': row[1],
                    'organization_type': row[2],
                    'sector': row[3],
                    'document_type': row[4],
                    'submission_date': row[5]
                })
            
            conn.close()
        
        return jsonify({
            'filters': filters,
            'total_results': len(results),
            'results': results
        })
        
    except Exception as e:
        return jsonify({'error': f'Filter search failed: {str(e)}'}), 500

@app.route('/api/search/suggestions', methods=['GET'])
def get_search_suggestions():
    """Get search suggestions based on partial query"""
    try:
        partial_query = request.args.get('q', '')
        limit = int(request.args.get('limit', 10))
        
        if not partial_query.strip():
            return jsonify({'suggestions': []})
        
        import sqlite3
        conn = sqlite3.connect(search_index.db_path)
        cursor = conn.cursor()
        
        suggestions = []
        
        # Suggest organization names
        cursor.execute('''
            SELECT DISTINCT organization_name, COUNT(*) as doc_count
            FROM documents
            WHERE LOWER(organization_name) LIKE ?
            GROUP BY organization_name
            ORDER BY doc_count DESC
            LIMIT ?
        ''', (f'%{partial_query.lower()}%', limit//2))
        
        org_results = cursor.fetchall()
        for org_name, count in org_results:
            suggestions.append({
                'type': 'organization',
                'text': org_name,
                'description': f'{count} documents',
                'category': 'Organizations'
            })
        
        # Suggest keywords from analysis
        cursor.execute('''
            SELECT DISTINCT keyword, analysis_category, COUNT(*) as freq
            FROM analysis_index
            WHERE LOWER(keyword) LIKE ?
            GROUP BY keyword, analysis_category
            ORDER BY freq DESC
            LIMIT ?
        ''', (f'%{partial_query.lower()}%', limit//2))
        
        keyword_results = cursor.fetchall()
        for keyword, category, freq in keyword_results:
            suggestions.append({
                'type': 'keyword',
                'text': keyword,
                'description': f'{category} - {freq} mentions',
                'category': 'Keywords'
            })
        
        conn.close()
        
        return jsonify({
            'query': partial_query,
            'suggestions': suggestions[:limit]
        })
        
    except Exception as e:
        return jsonify({'error': f'Failed to get suggestions: {str(e)}'}), 500

@app.route('/api/search/facets', methods=['GET'])
def get_search_facets():
    """Get available filter facets and their counts"""
    try:
        import sqlite3
        conn = sqlite3.connect(search_index.db_path)
        cursor = conn.cursor()
        
        facets = {}
        
        # Organization types
        cursor.execute('''
            SELECT organization_type, COUNT(*) as count
            FROM documents
            WHERE organization_type != ''
            GROUP BY organization_type
            ORDER BY count DESC
        ''')
        org_types = cursor.fetchall()
        facets['organization_types'] = [{'value': ot[0], 'count': ot[1]} for ot in org_types]
        
        # Sectors
        cursor.execute('''
            SELECT sector, COUNT(*) as count
            FROM documents
            WHERE sector != ''
            GROUP BY sector
            ORDER BY count DESC
        ''')
        sectors = cursor.fetchall()
        facets['sectors'] = [{'value': s[0], 'count': s[1]} for s in sectors]
        
        # Document types
        cursor.execute('''
            SELECT document_type, COUNT(*) as count
            FROM documents
            WHERE document_type != ''
            GROUP BY document_type
            ORDER BY count DESC
        ''')
        doc_types = cursor.fetchall()
        facets['document_types'] = [{'value': dt[0], 'count': dt[1]} for dt in doc_types]
        
        # Analysis categories
        cursor.execute('''
            SELECT analysis_category, COUNT(DISTINCT document_id) as count
            FROM analysis_index
            GROUP BY analysis_category
            ORDER BY count DESC
        ''')
        analysis_cats = cursor.fetchall()
        facets['analysis_categories'] = [{'value': ac[0], 'count': ac[1]} for ac in analysis_cats]
        
        # Top keywords by category
        cursor.execute('''
            SELECT analysis_category, keyword, SUM(frequency) as total_freq
            FROM analysis_index
            GROUP BY analysis_category, keyword
            ORDER BY analysis_category, total_freq DESC
        ''')
        keyword_results = cursor.fetchall()
        
        keywords_by_category = {}
        for cat, keyword, freq in keyword_results:
            if cat not in keywords_by_category:
                keywords_by_category[cat] = []
            if len(keywords_by_category[cat]) < 10:  # Top 10 per category
                keywords_by_category[cat].append({'keyword': keyword, 'frequency': freq})
        
        facets['top_keywords'] = keywords_by_category
        
        conn.close()
        
        return jsonify({
            'facets': facets,
            'total_documents': sum([item['count'] for item in facets['organization_types']])
        })
        
    except Exception as e:
        return jsonify({'error': f'Failed to get facets: {str(e)}'}), 500

@app.route('/api/index', methods=['POST'])
def index_document():
    """Index a new document for search"""
    try:
        data = request.json
        analysis_results = data.get('analysis_results')
        file_path = data.get('file_path')
        
        if not analysis_results:
            return jsonify({'error': 'Analysis results are required'}), 400
        
        document_id = search_index.index_document(analysis_results, file_path)
        
        return jsonify({
            'message': 'Document indexed successfully',
            'document_id': document_id
        })
        
    except Exception as e:
        return jsonify({'error': f'Indexing failed: {str(e)}'}), 500

@app.route('/api/index/status', methods=['GET'])
def get_index_status():
    """Get search index status and statistics"""
    try:
        import sqlite3
        conn = sqlite3.connect(search_index.db_path)
        cursor = conn.cursor()
        
        # Get document count
        cursor.execute('SELECT COUNT(*) FROM documents')
        doc_count = cursor.fetchone()[0]
        
        # Get content entries count
        cursor.execute('SELECT COUNT(*) FROM document_content')
        content_count = cursor.fetchone()[0]
        
        # Get analysis index count
        cursor.execute('SELECT COUNT(*) FROM analysis_index')
        analysis_count = cursor.fetchone()[0]
        
        # Get similarity patterns count
        cursor.execute('SELECT COUNT(*) FROM similarity_patterns')
        pattern_count = cursor.fetchone()[0]
        
        # Get last indexed document
        cursor.execute('''
            SELECT organization_name, indexed_at 
            FROM documents 
            ORDER BY indexed_at DESC 
            LIMIT 1
        ''')
        last_indexed = cursor.fetchone()
        
        conn.close()
        
        return jsonify({
            'status': 'healthy',
            'statistics': {
                'total_documents': doc_count,
                'content_entries': content_count,
                'analysis_entries': analysis_count,
                'similarity_patterns': pattern_count,
                'last_indexed_document': last_indexed[0] if last_indexed else None,
                'last_indexed_at': last_indexed[1] if last_indexed else None
            }
        })
        
    except Exception as e:
        return jsonify({'error': f'Failed to get index status: {str(e)}'}), 500

@app.route('/api/search/health', methods=['GET'])
def search_health_check():
    """Search API health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'AI Policy Analyzer Search API',
        'version': '1.0.0',
        'components': {
            'search_index': 'operational',
            'search_engine': 'operational',
            'database': 'connected'
        }
    })

if __name__ == '__main__':
    print("Starting AI Policy Analyzer Search API...")
    print("Available endpoints:")
    print("  GET/POST /api/search - Full-text search")
    print("  GET /api/search/similar/<document_id> - Similarity search")
    print("  POST /api/search/filter - Advanced filtering")
    print("  GET /api/search/suggestions - Search suggestions")
    print("  GET /api/search/facets - Available filter facets")
    print("  POST /api/index - Index new document")
    print("  GET /api/index/status - Index status")
    print("  GET /api/search/health - Health check")
    
    # Import port configuration
try:
    from port_config import get_port
    port = get_port('search_api')
except ImportError:
    port = 5002  # fallback port

app.run(debug=True, host='0.0.0.0', port=port)