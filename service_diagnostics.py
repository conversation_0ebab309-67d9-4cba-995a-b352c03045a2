#!/usr/bin/env python3
"""
AI Policy Analyzer - 服务诊断工具
用于诊断和修复服务启动问题

功能：
1. 检查系统依赖
2. 验证文件结构
3. 测试端口可用性
4. 分析服务日志
5. 提供修复建议

Author: Claude Code
Date: 2025-08-28
"""

import os
import sys
import time
import socket
import subprocess
import requests
import json
from pathlib import Path
from datetime import datetime
import logging

class ServiceDiagnostics:
    """服务诊断器"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.dashboard_dir = self.base_dir / "dashboard"
        self.backend_dir = self.dashboard_dir / "backend"
        self.frontend_dir = self.dashboard_dir / "frontend"
        self.logs_dir = self.base_dir / "logs"
        
        # 服务配置
        self.services = {
            'visualization_api': {'port': 5001, 'script': 'visualization_api.py'},
            'search_api': {'port': 5002, 'script': 'search_endpoints.py'},
            'historical_api': {'port': 5003, 'script': 'historical_data_endpoints.py'},
            'analytics_api': {'port': 5005, 'script': 'advanced_analytics_api.py'},
            'batch_api': {'port': 5007, 'script': 'enhanced_batch_processor.py'},
            'frontend': {'port': 8028, 'script': 'server.py', 'dir': self.frontend_dir}
        }
        
        self.issues = []
        self.fixes = []
        
    def log_issue(self, issue: str, severity: str = "WARNING"):
        """记录问题"""
        self.issues.append(f"[{severity}] {issue}")
        print(f"{'❌' if severity == 'ERROR' else '⚠️'} {issue}")
    
    def log_fix(self, fix: str):
        """记录修复建议"""
        self.fixes.append(fix)
        print(f"💡 修复建议: {fix}")
    
    def check_python_version(self):
        """检查Python版本"""
        print("🐍 检查Python版本...")
        
        if sys.version_info < (3, 8):
            self.log_issue(f"Python版本过低: {sys.version_info}, 需要3.8+", "ERROR")
            self.log_fix("升级Python到3.8或更高版本")
            return False
        else:
            print(f"✅ Python版本: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
            return True
    
    def check_dependencies(self):
        """检查依赖包"""
        print("\n📦 检查依赖包...")
        
        required_packages = [
            'flask', 'flask_cors', 'pandas', 'numpy', 'requests',
            'sqlite3', 'json', 'pathlib', 'datetime'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                if package == 'sqlite3':
                    import sqlite3
                elif package == 'json':
                    import json
                elif package == 'pathlib':
                    from pathlib import Path
                elif package == 'datetime':
                    from datetime import datetime
                else:
                    __import__(package)
                print(f"✅ {package}")
            except ImportError:
                missing_packages.append(package)
                self.log_issue(f"缺少依赖包: {package}", "ERROR")
        
        if missing_packages:
            self.log_fix(f"安装缺少的包: pip install {' '.join(missing_packages)}")
            return False
        
        return True
    
    def check_file_structure(self):
        """检查文件结构"""
        print("\n📁 检查文件结构...")
        
        required_dirs = [
            self.dashboard_dir,
            self.backend_dir,
            self.frontend_dir
        ]
        
        for dir_path in required_dirs:
            if dir_path.exists():
                print(f"✅ {dir_path.name}/")
            else:
                self.log_issue(f"目录不存在: {dir_path}", "ERROR")
                self.log_fix(f"创建目录: mkdir -p {dir_path}")
        
        # 检查关键脚本文件
        for service_name, config in self.services.items():
            script_dir = config.get('dir', self.backend_dir)
            script_path = script_dir / config['script']
            
            if script_path.exists():
                print(f"✅ {config['script']}")
            else:
                self.log_issue(f"脚本文件不存在: {script_path}", "ERROR")
                self.log_fix(f"确保脚本文件存在: {script_path}")
    
    def check_port_availability(self):
        """检查端口可用性"""
        print("\n🔌 检查端口可用性...")
        
        for service_name, config in self.services.items():
            port = config['port']
            
            if self.is_port_available(port):
                print(f"✅ 端口 {port} ({service_name}) - 可用")
            else:
                self.log_issue(f"端口 {port} ({service_name}) 被占用", "WARNING")
                self.log_fix(f"释放端口 {port} 或修改配置使用其他端口")
    
    def is_port_available(self, port: int) -> bool:
        """检查端口是否可用"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return True
        except OSError:
            return False
    
    def check_running_services(self):
        """检查正在运行的服务"""
        print("\n🏃 检查正在运行的服务...")
        
        for service_name, config in self.services.items():
            port = config['port']
            
            if self.is_service_running(port):
                print(f"✅ {service_name} - 运行中 (端口 {port})")
            else:
                print(f"⚫ {service_name} - 未运行 (端口 {port})")
    
    def is_service_running(self, port: int) -> bool:
        """检查服务是否运行"""
        try:
            response = requests.get(f"http://localhost:{port}", timeout=2)
            return True
        except:
            return False
    
    def analyze_logs(self):
        """分析日志文件"""
        print("\n📋 分析日志文件...")
        
        if not self.logs_dir.exists():
            print("ℹ️ 日志目录不存在，可能是首次运行")
            return
        
        log_files = list(self.logs_dir.glob("*.log"))
        
        if not log_files:
            print("ℹ️ 没有找到日志文件")
            return
        
        # 分析最新的日志文件
        latest_log = max(log_files, key=lambda f: f.stat().st_mtime)
        print(f"📄 分析最新日志: {latest_log.name}")
        
        try:
            with open(latest_log, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            error_count = 0
            warning_count = 0
            
            for line in lines[-50:]:  # 只看最后50行
                if 'ERROR' in line or '❌' in line:
                    error_count += 1
                    print(f"🔴 {line.strip()}")
                elif 'WARNING' in line or '⚠️' in line:
                    warning_count += 1
                    print(f"🟡 {line.strip()}")
            
            print(f"📊 日志统计: {error_count} 个错误, {warning_count} 个警告")
            
        except Exception as e:
            self.log_issue(f"无法读取日志文件: {e}", "WARNING")
    
    def test_service_startup(self, service_name: str):
        """测试单个服务启动"""
        print(f"\n🧪 测试 {service_name} 启动...")
        
        config = self.services[service_name]
        script_dir = config.get('dir', self.backend_dir)
        script_path = script_dir / config['script']
        
        if not script_path.exists():
            self.log_issue(f"脚本不存在: {script_path}", "ERROR")
            return False
        
        try:
            # 尝试启动服务（短时间测试）
            process = subprocess.Popen(
                [sys.executable, str(script_path)],
                cwd=str(script_dir),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # 等待2秒
            time.sleep(2)
            
            if process.poll() is None:
                print(f"✅ {service_name} 启动测试成功")
                process.terminate()
                process.wait()
                return True
            else:
                stdout, stderr = process.communicate()
                self.log_issue(f"{service_name} 启动失败", "ERROR")
                if stderr:
                    print(f"错误信息: {stderr.decode()}")
                return False
                
        except Exception as e:
            self.log_issue(f"测试 {service_name} 时出错: {e}", "ERROR")
            return False
    
    def generate_report(self):
        """生成诊断报告"""
        print("\n" + "="*60)
        print("📋 诊断报告")
        print("="*60)
        
        if not self.issues:
            print("✅ 没有发现问题，系统状态良好")
        else:
            print(f"发现 {len(self.issues)} 个问题:")
            for i, issue in enumerate(self.issues, 1):
                print(f"{i}. {issue}")
        
        if self.fixes:
            print(f"\n💡 修复建议 ({len(self.fixes)} 项):")
            for i, fix in enumerate(self.fixes, 1):
                print(f"{i}. {fix}")
        
        # 保存报告到文件
        report_file = self.base_dir / f"diagnostic_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("AI Policy Analyzer - 诊断报告\n")
            f.write("="*60 + "\n")
            f.write(f"生成时间: {datetime.now()}\n\n")
            
            f.write("问题列表:\n")
            for issue in self.issues:
                f.write(f"- {issue}\n")
            
            f.write("\n修复建议:\n")
            for fix in self.fixes:
                f.write(f"- {fix}\n")
        
        print(f"\n📄 详细报告已保存到: {report_file}")
        print("="*60)
    
    def run_full_diagnosis(self):
        """运行完整诊断"""
        print("🔍 AI Policy Analyzer - 服务诊断工具")
        print("="*60)
        
        # 基础检查
        self.check_python_version()
        self.check_dependencies()
        self.check_file_structure()
        self.check_port_availability()
        
        # 服务检查
        self.check_running_services()
        
        # 日志分析
        self.analyze_logs()
        
        # 生成报告
        self.generate_report()

def main():
    """主函数"""
    diagnostics = ServiceDiagnostics()
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "test":
            # 测试特定服务
            if len(sys.argv) > 2:
                service_name = sys.argv[2]
                if service_name in diagnostics.services:
                    diagnostics.test_service_startup(service_name)
                else:
                    print(f"❌ 未知服务: {service_name}")
                    print(f"可用服务: {', '.join(diagnostics.services.keys())}")
            else:
                print("用法: python service_diagnostics.py test <service_name>")
        
        elif command == "ports":
            # 只检查端口
            diagnostics.check_port_availability()
        
        elif command == "logs":
            # 只分析日志
            diagnostics.analyze_logs()
        
        else:
            print("用法: python service_diagnostics.py [test|ports|logs]")
    else:
        # 运行完整诊断
        diagnostics.run_full_diagnosis()

if __name__ == '__main__':
    main()
