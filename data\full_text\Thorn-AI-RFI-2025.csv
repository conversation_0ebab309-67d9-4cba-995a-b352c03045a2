﻿content_type,page_number,content,character_count,word_count,metadata_field,metadata_value
document_info,0,Document: Thorn-AI-RFI-2025.pdf,0,0,filename,Thorn-AI-RFI-2025.pdf
metadata,0,US AI RFI,9,3,title,US AI RFI
metadata,0,Skia/PDF m135 Google Docs Renderer,34,5,producer,Skia/PDF m135 Google Docs Renderer
metadata,0,D:20250318075940-04'00',23,1,creation_date,D:20250318075940-04'00'
metadata,0,D:20250318075940-04'00',23,1,modification_date,D:20250318075940-04'00'
document_stats,0,"Total pages: 5, Total characters: 14784, Total words: 2053",14784,2053,document_stats,"pages:5,chars:14784,words:2053"
full_text,0,"AI Action Plan RFI Introduction Thorn is a nonpro ﬁt that builds technology and conducts research to defend children from sexual abuse. Founded in 2012, the organization equips those on the frontlines with the technology and research they need to protect children from sexual abuse and exploitation in the digital age. Thorn s tools have helped the tech industry detect and report millions of child sexual abuse ﬁles on the open web, and connected investigators and non-pro ﬁts with critical information to help them solve cases faster and remove children from harm. <PERSON> s research has provided the ecosystem with the necessary insights and issue understanding to build robust interventions across the ecosystem. Thorn has acted as a leader in preventing the misuse of generative AI for furthering child sexual abuse via our Safety by Design for Generative AI initiative1, both via providing early visibility to the ecosystem on emerging generative AI enabled sexual harms against children, and via galvanizing the ecosystem to move to prevent the harms. These harms include: offenders complicating victim identi ﬁcation by making photorealistic AI-generated child sexual abuse material2 (AIG-CSAM) at scale, perpetrating re-victimization by ﬁne-tuning open source and open weight models on existing child abuse imagery to generate additional explicit images of these children3, and scaling their sexual extortion and harassment efforts by using generative AI to sexualize benign imagery of children, accelerating the creation of content necessary to target a child4. 4 Malicious Actors Manipulating Photos and Videos to Create Explicit Content and Sextortion Schemes. FBI, June 2023, https:/ /www.ic3.gov/PSA/Archive/2023/PSA230605 ; Thorn. (2025). Deepfake Nudes & Young People: Navigating a new frontier in technology-facilitated nonconsensual sexual abuse and exploitation. https:/ /info.thorn.org/hubfs/Research/Thorn_DeepfakeNudes&YoungPeople_Mar2025.pdf 3 Thiel, Stroebel, and Portnoff. Generative ML and CSAM: Implications and Mitigations. Stanford Digital Repository, June 2023, https:/ /doi.org/10.25740/jv206yg3793 2 Thiel, Stroebel, and Portnoff. Generative ML and CSAM: Implications and Mitigations. Stanford Digital Repository, June 2023, https:/ /doi.org/10.25740/jv206yg3793 ; 2024 Update: Understanding the Rapid Evolution of AI-Generated Child Abuse Imagery. IWF, July 2024, https:/ /www.iwf.org.uk/about-us/why-we-exist/our-research/how-ai-is-being-abused-to-create-child-sexual-abuse-imagery ; Generative AI CSAM is CSAM. NCMEC, Mar 2024, https:/ /www.missingkids.org/blog/2024/generative-ai-csam-is-csam 1 Thorn and All Tech Is Human Forge Generative AI Principles with AI Leaders to Enact Strong Child Safety Commitments. Thorn, April 2024, https:/ /www.thorn.org/blog/generative-ai-principles 1 Beginning in July of 2023, Thorn and All Tech Is Human (ATIH) organized a working group consisting of representatives from leading generative AI companies, to collaboratively deﬁne, align on, and commit to a set of Safety by Design principles and mitigations to prevent the misuse of generative artiﬁcial intelligence (AI) to further sexual harms against children. The goal of this initiative was to establish a standard such that, if adopted, these technologies are less capable of producing AIG-CSAM and other content that contributes to the sexual exploitation of children, the content that is created gets detected more reliably, and the distribution of the models, apps and services used to create the content is limited. The output of this work is two fold: 1. Our Safety by Design for Generative AI: Preventing Child Sexual Abuse paper 5 that provides fundamental principles, and mitigations to enact those principles, for building generative AI to prevent the misuse of generative AI technologies to perpetrate, proliferate, and further sexual harms against children. These principles and mitigations cover the full lifecycle of machine learning/AI (develop, deploy, maintain), and apply across several key technology players in the ecosystem (AI Developers, ﬁrst-party and third-party AI Providers, Data Hosting Platforms, Social Platforms, and Search Engines). 2. Commitments 6 from a set of industry stakeholders, to implement the preventative and proactive principles deﬁned in the paper described above, into their generative AI technologies and products. Companies agreed to adopt these principles, and further to transparently share their progress in taking action on these principles. The initial group of companies who joined into these commitments include: Amazon, Anthropic, Civitai, Google, Meta, Metaphysic, Microsoft, Mistral AI, OpenAI, and Stability AI. Since initial launch, Invoke has also joined into these commitments. Request for Information Thorn supports OSTP s efforts to drive U.S. AI policy that promotes human ﬂourishing. This ﬂourishing deﬁnitionally must include the wellbeing of children. Due to the remit of our organization s mission, the recommendations in this submission focus on one category: investments the U.S. government should make to ensure that the foundation of these vital technologies are established, from the beginning, in such a way that 6 Thorn and All Tech Is Human Forge Generative AI Principles with AI Leaders to Enact Strong Child Safety Commitments. Thorn, April 2024, https://www.thorn.org/blog/generative-ai-principles 5 Thorn & ATIH (2024) Safety by Design for Generative AI: Preventing Child Sexual Abuse. Thorn Repository. Available at https://info.thorn.org/hubfs/thorn-safety-by-design-forgenerative-AI.pdf . 2 subsequent rapid innovation leads to children s ﬂourishing, rather than children s abuse and harm. Build the federal government s capacity to assess generative AI models training data and AIG-CSAM capabilities We are already observing today the concrete physical, social and ﬁnancial harms 7 American children and their families are experiencing due to the misuse of generative AI. In pursuit of rapid innovation, preventing criminal misuse of emerging technology can fall to the wayside. This is a costly mistake: leading to an environment where developer resources are spent rebuilding infrastructure and technology that has already been built, in response to criminal misuse that is already ongoing. Strategies for building technology should instead seek to prevent misuse from the beginning: and for generative AI, this fundamentally begins with the training data. Popular models on the market have already been discovered to include CSAM in their training data 8 . For some models, their compositional generalization 9 capabilities further allow them to combine concepts (e.g. adult sexual content and non-sexual depictions of children) to then produce AIG-CSAM. With proper training data curation, developers can mitigate their models AIG-CSAM capabilities, and in this way minimize the need for resources focused on downstream corrections, as well as accelerate positive use cases for generative AI. In addition to training data curation, assessing models for their AIG-CSAM capabilities further ensures that this preventative work pays dividends downstream for children s safety and ﬂourishing. Currently, there is no umbrella of immunity for organizations to directly assess their models for AIG-CSAM capabilities without fear of legal liability 10 , and training data curation (including ﬁltering for CSAM) is not consistently practiced across the generative AI ecosystem. As a result, we recommend that the government build its capacity to assess generative AI models training data and AIG-CSAM capabilities in the following ways: 10 Despite this, it is possible for red teaming to be carried out such that due regard is given for the regulatory bounds on those carrying out testing. One option could involve separately assessing whether: the model is capable of producing adult sexual content, and the model is capable of producing photo realistic representations of children 9 Compositional generalization is a term that is sometimes used to refer to a model s ability to combine attributes seen independently in training. While it is still an open area of research on when and how models are able to do this, if both independent factors named above have a high propensity and the model demonstrates strong compositional generalization, this may indicate a corresponding high propensity for a model to be able to produce AIG-CSAM. 8 Thiel. Identifying and Eliminating CSAM in Generative ML Training Data and Models. Stanford Digital Repository, Dec 2023, https://doi.org/10.25740/kh752sm9123 7 Criminals Use Generative Artiﬁcial Intelligence to Facilitate Financial Fraud. FBI, Dec 2024. https://www.ic3.gov/PSA/2024/PSA241203 3 Direct the National Institutes of Standards and Technology (NIST) in collaboration with the National Institute of Justice (NIJ) to develop frameworks to robustly benchmark technologies commonly used for detecting CSAM (e.g. hashing and matching 11 ) to establish their efficacy in ﬁltering generative AI training datasets. Direct NIST to invest in research exploring whether and what performance degradation in other model capabilities may or may not occur as a result of training data ﬁltering and training data curation to prevent AIG-CSAM capabilities Task the DHS/DOJ with establishing MOU s with U.S. AI companies to assess their models for AIG-CSAM capabilities, and audit their training data curation processes Invest in technical research to establish stronger safeguards for open source models, open weight models, and model hosting platforms The accessibility of open source and open weight models allows for signiﬁcantly easier customization and optimization of models, opening the door to both opportunities and risks 12 . Offenders ﬁne-tune open source and open weight models on existing child abuse imagery to generate additional explicit images of these children 13 . Nudifying apps are built off of open foundation models, and are used today to sexualize benign depictions of children 14 . Similarly, model hosting platforms can act as an accelerant for the distribution of models that facilitate this type of criminal activity 15 . Without prioritizing development and innovation towards robust safeguards baked into the model itself, we are opening the door for criminals to take advantage of American innovation to sexually abuse and harass our children. There is an urgent need for scalable, reliable model assessments that are not overly reliant on prompt/response strategies. Current strategies for model assessments are inherently manual: at their core, they all involve evaluating using prompts and assessing 15 Thiel, Stroebel, and Portnoff. Generative ML and CSAM: Implications and Mitigations. Stanford Digital Repository, June 2023, https://doi.org/10.25740/jv206yg3793 ; a16z Funded AI Platform Generated Images That Could Be Categorized as Child Pornography, Leaked Documents Show. 404 Media, Dec 2023, https://www.404media.co/a16z-funded-ai-platform-generated-images-that-could-be-categorized-as-child-pornography-leake d-documents-show ; Harris and Willner. Was an AI Image Generator Taken Down for Making Child Porn? IEEE Spectrum, Aug 2024, https://spectrum.ieee.org/stable-diffusion ; An AI companion site is hosting sexually charged conversations with underage celebrity bots. MIT Tech Review, Feb 2025, https://www.technologyreview.com/2025/02/27/1112616/an-ai-companion-site-is-hosting-sexually-charged-conversations-wit h-underage-celebrity-bots 14 Thorn. (2025). Deepfake Nudes & Young People: Navigating a new frontier in technology-facilitated nonconsensual sexual abuse and exploitation. https://info.thorn.org/hubfs/Research/Thorn_DeepfakeNudes&YoungPeople_Mar2025.pdf 13 Thiel, Stroebel, and Portnoff. Generative ML and CSAM: Implications and Mitigations. Stanford Digital Repository, June 2023, https://doi.org/10.25740/jv206yg3793 12 Dual-Use Foundation Models with Widely Available Model Weights. NTIA Report, July 2024, https://www.ntia.gov/sites/default/ﬁles/publications/ntia-ai-open-model-report.pdf 11 How Hashing and Matching Can Help Prevent Revictimization. Thorn, Aug 2023. https://www.thorn.org/blog/hashing-detect-child-sex-abuse-imagery 4 outputs. Given the pace and scale of newly released models into the ecosystem, and the speci ﬁc sensitivities of assessing AIG-CSAM related harms, these strategies are not sufficient16. There is further a need for model training strategies that prevent and mitigate adversarial ﬁne-tuning, unlearning and other adversarial optimizations downstream17. As a result, we recommend that the government invest in technical research to establish stronger safeguards for open source models, open weight models, and model hosting platforms in the following ways: Direct NIST to advance the state of the art in solutions for scalable model assessments, and model training strategies for building models which are robust to downstream adversarial manipulation Determine and instruct the appropriate department (e.g. DHS, DOJ, or the AI Safety Institute) to establish MOU s with U.S. model hosting companies to assess their current strategies for preventing their platforms from scaling the distribution of models that are misused to further sexual harms against children Direct the Federal Trade Commission's Bureau of Consumer Protection to review the websites, apps and other resources that advertise nudifying services to determine the extent to which they are engaging in unfair, deceptive and fraudulent business practices that result in the creation and distribution of illegal media (AIG-CSAM) Conclusion There is real potential for generative AI to promote human ﬂourishing. In order to accomplish this outcome, it will require both clear eyes on how this technology is currently being used to harm and harass the most vulnerable among us: our children, and the leadership and will to prevent this abuse. We wish to express our appreciation to the OSTP for their engagement and leadership in ensuring that this formative technology achieves those positive ends. If any questions arise from review of this request for information, please reach out to Dr. Rebecca Portnoff and Lara Gemar . 17 Pan et al. Leveraging Catastrophic Forgetting to Develop Safe Diffusion Models against Malicious Finetuning. NeurIps 2024, https:/ /openreview.net/pdf?id=pR37AmwbOt This document is approved for public dissemination. The document contains no business-proprietary or conﬁdential information. Document contents may be reused by the government in developing the AI Action Plan and associated documents without attribution. 16 Bereska and Gavves. Mechanistic Interpretability for AI Safety: A Review. arxiv.org, Aug 2024, https:/ /arxiv.org/pdf/2404.14082 5",14784,2053,full_document_text,
page_text,1,"AI Action Plan RFI Introduction Thorn is a nonpro ﬁt that builds technology and conducts research to defend children from sexual abuse. Founded in 2012, the organization equips those on the frontlines with the technology and research they need to protect children from sexual abuse and exploitation in the digital age. Thorn s tools have helped the tech industry detect and report millions of child sexual abuse ﬁles on the open web, and connected investigators and non-pro ﬁts with critical information to help them solve cases faster and remove children from harm. Thorn s research has provided the ecosystem with the necessary insights and issue understanding to build robust interventions across the ecosystem. Thorn has acted as a leader in preventing the misuse of generative AI for furthering child sexual abuse via our Safety by Design for Generative AI initiative1, both via providing early visibility to the ecosystem on emerging generative AI enabled sexual harms against children, and via galvanizing the ecosystem to move to prevent the harms. These harms include: offenders complicating victim identi ﬁcation by making photorealistic AI-generated child sexual abuse material2 (AIG-CSAM) at scale, perpetrating re-victimization by ﬁne-tuning open source and open weight models on existing child abuse imagery to generate additional explicit images of these children3, and scaling their sexual extortion and harassment efforts by using generative AI to sexualize benign imagery of children, accelerating the creation of content necessary to target a child4. 4 Malicious Actors Manipulating Photos and Videos to Create Explicit Content and Sextortion Schemes. FBI, June 2023, https:/ /www.ic3.gov/PSA/Archive/2023/PSA230605 ; Thorn. (2025). Deepfake Nudes & Young People: Navigating a new frontier in technology-facilitated nonconsensual sexual abuse and exploitation. https:/ /info.thorn.org/hubfs/Research/Thorn_DeepfakeNudes&YoungPeople_Mar2025.pdf 3 Thiel, Stroebel, and Portnoff. Generative ML and CSAM: Implications and Mitigations. Stanford Digital Repository, June 2023, https:/ /doi.org/10.25740/jv206yg3793 2 Thiel, Stroebel, and Portnoff. Generative ML and CSAM: Implications and Mitigations. Stanford Digital Repository, June 2023, https:/ /doi.org/10.25740/jv206yg3793 ; 2024 Update: Understanding the Rapid Evolution of AI-Generated Child Abuse Imagery. IWF, July 2024, https:/ /www.iwf.org.uk/about-us/why-we-exist/our-research/how-ai-is-being-abused-to-create-child-sexual-abuse-imagery ; Generative AI CSAM is CSAM. NCMEC, Mar 2024, https:/ /www.missingkids.org/blog/2024/generative-ai-csam-is-csam 1 Thorn and All Tech Is Human Forge Generative AI Principles with AI Leaders to Enact Strong Child Safety Commitments. Thorn, April 2024, https:/ /www.thorn.org/blog/generative-ai-principles 1",2819,370,page_content,page_1
page_text,2,"Beginning in July of 2023, Thorn and All Tech Is Human (ATIH) organized a working group consisting of representatives from leading generative AI companies, to collaboratively deﬁne, align on, and commit to a set of Safety by Design principles and mitigations to prevent the misuse of generative artiﬁcial intelligence (AI) to further sexual harms against children. The goal of this initiative was to establish a standard such that, if adopted, these technologies are less capable of producing AIG-CSAM and other content that contributes to the sexual exploitation of children, the content that is created gets detected more reliably, and the distribution of the models, apps and services used to create the content is limited. The output of this work is two fold: 1. Our Safety by Design for Generative AI: Preventing Child Sexual Abuse paper 5 that provides fundamental principles, and mitigations to enact those principles, for building generative AI to prevent the misuse of generative AI technologies to perpetrate, proliferate, and further sexual harms against children. These principles and mitigations cover the full lifecycle of machine learning/AI (develop, deploy, maintain), and apply across several key technology players in the ecosystem (AI Developers, ﬁrst-party and third-party AI Providers, Data Hosting Platforms, Social Platforms, and Search Engines). 2. Commitments 6 from a set of industry stakeholders, to implement the preventative and proactive principles deﬁned in the paper described above, into their generative AI technologies and products. Companies agreed to adopt these principles, and further to transparently share their progress in taking action on these principles. The initial group of companies who joined into these commitments include: Amazon, Anthropic, Civitai, Google, Meta, Metaphysic, Microsoft, Mistral AI, OpenAI, and Stability AI. Since initial launch, Invoke has also joined into these commitments. Request for Information Thorn supports OSTP s efforts to drive U.S. AI policy that promotes human ﬂourishing. This ﬂourishing deﬁnitionally must include the wellbeing of children. Due to the remit of our organization s mission, the recommendations in this submission focus on one category: investments the U.S. government should make to ensure that the foundation of these vital technologies are established, from the beginning, in such a way that 6 Thorn and All Tech Is Human Forge Generative AI Principles with AI Leaders to Enact Strong Child Safety Commitments. Thorn, April 2024, https://www.thorn.org/blog/generative-ai-principles 5 Thorn & ATIH (2024) Safety by Design for Generative AI: Preventing Child Sexual Abuse. Thorn Repository. Available at https://info.thorn.org/hubfs/thorn-safety-by-design-forgenerative-AI.pdf . 2",2781,403,page_content,page_2
page_text,3,"subsequent rapid innovation leads to children s ﬂourishing, rather than children s abuse and harm. Build the federal government s capacity to assess generative AI models training data and AIG-CSAM capabilities We are already observing today the concrete physical, social and ﬁnancial harms 7 American children and their families are experiencing due to the misuse of generative AI. In pursuit of rapid innovation, preventing criminal misuse of emerging technology can fall to the wayside. This is a costly mistake: leading to an environment where developer resources are spent rebuilding infrastructure and technology that has already been built, in response to criminal misuse that is already ongoing. Strategies for building technology should instead seek to prevent misuse from the beginning: and for generative AI, this fundamentally begins with the training data. Popular models on the market have already been discovered to include CSAM in their training data 8 . For some models, their compositional generalization 9 capabilities further allow them to combine concepts (e.g. adult sexual content and non-sexual depictions of children) to then produce AIG-CSAM. With proper training data curation, developers can mitigate their models AIG-CSAM capabilities, and in this way minimize the need for resources focused on downstream corrections, as well as accelerate positive use cases for generative AI. In addition to training data curation, assessing models for their AIG-CSAM capabilities further ensures that this preventative work pays dividends downstream for children s safety and ﬂourishing. Currently, there is no umbrella of immunity for organizations to directly assess their models for AIG-CSAM capabilities without fear of legal liability 10 , and training data curation (including ﬁltering for CSAM) is not consistently practiced across the generative AI ecosystem. As a result, we recommend that the government build its capacity to assess generative AI models training data and AIG-CSAM capabilities in the following ways: 10 Despite this, it is possible for red teaming to be carried out such that due regard is given for the regulatory bounds on those carrying out testing. One option could involve separately assessing whether: the model is capable of producing adult sexual content, and the model is capable of producing photo realistic representations of children 9 Compositional generalization is a term that is sometimes used to refer to a model s ability to combine attributes seen independently in training. While it is still an open area of research on when and how models are able to do this, if both independent factors named above have a high propensity and the model demonstrates strong compositional generalization, this may indicate a corresponding high propensity for a model to be able to produce AIG-CSAM. 8 Thiel. Identifying and Eliminating CSAM in Generative ML Training Data and Models. Stanford Digital Repository, Dec 2023, https://doi.org/10.25740/kh752sm9123 7 Criminals Use Generative Artiﬁcial Intelligence to Facilitate Financial Fraud. FBI, Dec 2024. https://www.ic3.gov/PSA/2024/PSA241203 3",3141,469,page_content,page_3
page_text,4,"Direct the National Institutes of Standards and Technology (NIST) in collaboration with the National Institute of Justice (NIJ) to develop frameworks to robustly benchmark technologies commonly used for detecting CSAM (e.g. hashing and matching 11 ) to establish their efficacy in ﬁltering generative AI training datasets. Direct NIST to invest in research exploring whether and what performance degradation in other model capabilities may or may not occur as a result of training data ﬁltering and training data curation to prevent AIG-CSAM capabilities Task the DHS/DOJ with establishing MOU s with U.S. AI companies to assess their models for AIG-CSAM capabilities, and audit their training data curation processes Invest in technical research to establish stronger safeguards for open source models, open weight models, and model hosting platforms The accessibility of open source and open weight models allows for signiﬁcantly easier customization and optimization of models, opening the door to both opportunities and risks 12 . Offenders ﬁne-tune open source and open weight models on existing child abuse imagery to generate additional explicit images of these children 13 . Nudifying apps are built off of open foundation models, and are used today to sexualize benign depictions of children 14 . Similarly, model hosting platforms can act as an accelerant for the distribution of models that facilitate this type of criminal activity 15 . Without prioritizing development and innovation towards robust safeguards baked into the model itself, we are opening the door for criminals to take advantage of American innovation to sexually abuse and harass our children. There is an urgent need for scalable, reliable model assessments that are not overly reliant on prompt/response strategies. Current strategies for model assessments are inherently manual: at their core, they all involve evaluating using prompts and assessing 15 Thiel, Stroebel, and Portnoff. Generative ML and CSAM: Implications and Mitigations. Stanford Digital Repository, June 2023, https://doi.org/10.25740/jv206yg3793 ; a16z Funded AI Platform Generated Images That Could Be Categorized as Child Pornography, Leaked Documents Show. 404 Media, Dec 2023, https://www.404media.co/a16z-funded-ai-platform-generated-images-that-could-be-categorized-as-child-pornography-leake d-documents-show ; Harris and Willner. Was an AI Image Generator Taken Down for Making Child Porn? IEEE Spectrum, Aug 2024, https://spectrum.ieee.org/stable-diffusion ; An AI companion site is hosting sexually charged conversations with underage celebrity bots. MIT Tech Review, Feb 2025, https://www.technologyreview.com/2025/02/27/1112616/an-ai-companion-site-is-hosting-sexually-charged-conversations-wit h-underage-celebrity-bots 14 Thorn. (2025). Deepfake Nudes & Young People: Navigating a new frontier in technology-facilitated nonconsensual sexual abuse and exploitation. https://info.thorn.org/hubfs/Research/Thorn_DeepfakeNudes&YoungPeople_Mar2025.pdf 13 Thiel, Stroebel, and Portnoff. Generative ML and CSAM: Implications and Mitigations. Stanford Digital Repository, June 2023, https://doi.org/10.25740/jv206yg3793 12 Dual-Use Foundation Models with Widely Available Model Weights. NTIA Report, July 2024, https://www.ntia.gov/sites/default/ﬁles/publications/ntia-ai-open-model-report.pdf 11 How Hashing and Matching Can Help Prevent Revictimization. Thorn, Aug 2023. https://www.thorn.org/blog/hashing-detect-child-sex-abuse-imagery 4",3498,439,page_content,page_4
page_text,5,"outputs. Given the pace and scale of newly released models into the ecosystem, and the speci ﬁc sensitivities of assessing AIG-CSAM related harms, these strategies are not sufficient16. There is further a need for model training strategies that prevent and mitigate adversarial ﬁne-tuning, unlearning and other adversarial optimizations downstream17. As a result, we recommend that the government invest in technical research to establish stronger safeguards for open source models, open weight models, and model hosting platforms in the following ways: Direct NIST to advance the state of the art in solutions for scalable model assessments, and model training strategies for building models which are robust to downstream adversarial manipulation Determine and instruct the appropriate department (e.g. DHS, DOJ, or the AI Safety Institute) to establish MOU s with U.S. model hosting companies to assess their current strategies for preventing their platforms from scaling the distribution of models that are misused to further sexual harms against children Direct the Federal Trade Commission's Bureau of Consumer Protection to review the websites, apps and other resources that advertise nudifying services to determine the extent to which they are engaging in unfair, deceptive and fraudulent business practices that result in the creation and distribution of illegal media (AIG-CSAM) Conclusion There is real potential for generative AI to promote human ﬂourishing. In order to accomplish this outcome, it will require both clear eyes on how this technology is currently being used to harm and harass the most vulnerable among us: our children, and the leadership and will to prevent this abuse. We wish to express our appreciation to the OSTP for their engagement and leadership in ensuring that this formative technology achieves those positive ends. If any questions arise from review of this request for information, please reach out to Dr. Rebecca Portnoff and Lara Gemar . 17 Pan et al. Leveraging Catastrophic Forgetting to Develop Safe Diffusion Models against Malicious Finetuning. NeurIps 2024, https:/ /openreview.net/pdf?id=pR37AmwbOt This document is approved for public dissemination. The document contains no business-proprietary or conﬁdential information. Document contents may be reused by the government in developing the AI Action Plan and associated documents without attribution. 16 Bereska and Gavves. Mechanistic Interpretability for AI Safety: A Review. arxiv.org, Aug 2024, https:/ /arxiv.org/pdf/2404.14082 5",2541,372,page_content,page_5
