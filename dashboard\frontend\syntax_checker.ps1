# PowerShell script to check for common syntax errors in JavaScript files

# Define patterns to look for
$patterns = @(
    # Look for assignments to function calls
    '(\w+\([^)]*\))\s*=\s*',
    # Look for single equals in if statements (potential mistake)
    'if\s*\(\s*[\w\.]+\s*=\s*[^=]',
    # Look for single equals in while conditions (potential mistake)
    'while\s*\(\s*[\w\.]+\s*=\s*[^=]',
    # Look for single equals in for conditions (potential mistake)
    'for\s*\([^;]*;\s*[\w\.]+\s*=\s*[^=][^;]*;'
)

# List of files to check
$files = @(
    "dashboard_complete.js",
    "dashboard_complete_part2.js",
    "dashboard_complete_part3.js",
    "dashboard_complete_part4.js",
    "dashboard_complete_part5.js",
    "dashboard_complete_part6.js",
    "dashboard_complete_part7.js",
    "dashboard_complete_part8.js",
    "dashboard_enhancements.js"
)

foreach ($file in $files) {
    Write-Output "`nChecking $file..."
    
    if (Test-Path $file) {
        $content = Get-Content $file -Raw
        $lineNumber = 1
        $lines = $content -split "`n"
        
        foreach ($line in $lines) {
            foreach ($pattern in $patterns) {
                if ($line -match $pattern) {
                    Write-Output "Potential syntax error in $file at line $lineNumber"
                    Write-Output "    $line"
                }
            }
            $lineNumber++
        }
    } else {
        Write-Output "File not found: $file"
    }
}

Write-Output "`nAlso checking for assignment operators in conditional statements..."

foreach ($file in $files) {
    if (Test-Path $file) {
        $content = Get-Content $file -Raw
        $matches = [regex]::Matches($content, '(if|while|for)\s*\([^)]*=(?!=)[^)]*\)')
        
        foreach ($match in $matches) {
            $lineContent = $match.Value
            $lineNumber = ($content.Substring(0, $match.Index) -split "`n").Length
            Write-Output "Potential conditional assignment in $file near line $lineNumber"
            Write-Output "    $lineContent"
        }
    }
}

Write-Output "`nDone checking files"
