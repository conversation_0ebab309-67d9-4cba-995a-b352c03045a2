/**
 * AI Policy Analyzer Dashboard - Frontend JavaScript
 * Modern implementation with enhanced visualization and user experience
 * 
 * Author: <PERSON>
 * Date: August 13, 2025
 */

// Configuration
const API_CONFIG = {
    visualization: 'http://localhost:5001/api',
    search: 'http://localhost:5002/api', 
    historical: 'http://localhost:5003/api',
    batch: 'http://localhost:5007/api',
    analytics: 'http://localhost:5005/api/analytics'
};

// Global state
let dashboardState = {
    currentSection: 'dashboard',
    searchResults: [],
    analysisData: null,
    historicalData: [],
    charts: {},
    batchProcessing: {
        isRunning: false,
        currentBatchId: null,
        progress: null
    },
    theme: localStorage.getItem('dashboard_theme') || 'light',
    sidebarCollapsed: localStorage.getItem('sidebar_collapsed') === 'true' || false
};

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
    setupEventListeners();
    loadDashboardData();
});

/**
 * Initialize all dashboard components
 */
function initializeDashboard() {
    console.log('🚀 Initializing AI Policy Analyzer Dashboard...');
    
    // Apply saved theme
    applyTheme(dashboardState.theme);
    
    // Apply sidebar state
    if (dashboardState.sidebarCollapsed) {
        document.body.classList.add('sb-sidenav-toggled');
    }
    
    // Initialize navigation
    setupNavigation();
    
    // Initialize sidebar toggle
    setupSidebarToggle();
    
    // Initialize upload functionality
    setupFileUpload();
    
    // Initialize ML models early
    try {
        loadMLModels();
        console.log('✅ ML models pre-loaded successfully');
    } catch (error) {
        console.warn('⚠️ ML models pre-loading failed:', error);
    }
    
    // Initialize search functionality
    setupSearch();
    
    console.log('✅ Dashboard initialized successfully');
}

/**
 * Setup event listeners
 */
function setupEventListeners() {
    // Search input enter key
    document.getElementById('searchInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });
    
    // File input change
    const fileInput = document.getElementById('fileInput');
    if (fileInput) {
        fileInput.addEventListener('change', handleFileSelection);
    }
    
    // Theme toggle (if present)
    const themeToggle = document.getElementById('themeToggle');
    if (themeToggle) {
        themeToggle.addEventListener('click', toggleTheme);
    }
}

/**
 * Setup navigation between sections
 */
function setupNavigation() {
    const navLinks = document.querySelectorAll('.list-group-item');
    const sections = document.querySelectorAll('.content-section');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetSection = this.getAttribute('data-section');
            
            // Update active nav link
            navLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
            
            // Show target section
            sections.forEach(section => {
                section.classList.remove('active');
            });
            
            const targetElement = document.getElementById(targetSection + '-section');
            if (targetElement) {
                targetElement.classList.add('active');
                dashboardState.currentSection = targetSection;
                
                // Load section-specific data
                loadSectionData(targetSection);
            }
        });
    });
}

/**
 * Setup sidebar toggle functionality
 */
function setupSidebarToggle() {
    const sidebarToggle = document.getElementById('sidebarToggle');
    
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function(e) {
            e.preventDefault();
            document.body.classList.toggle('sb-sidenav-toggled');
            
            // Save state
            dashboardState.sidebarCollapsed = document.body.classList.contains('sb-sidenav-toggled');
            localStorage.setItem('sidebar_collapsed', dashboardState.sidebarCollapsed);
        });
    }
}

/**
 * Load section-specific data
 */
function loadSectionData(section) {
    switch(section) {
        case 'dashboard':
            loadDashboardData();
            break;
        case 'historical':
            loadHistoricalData();
            break;
        case 'search':
            loadSearchSection();
            break;
        case 'analysis':
            // Analysis section - no auto-load
            break;
        case 'upload':
            loadUploadSection();
            break;
        case 'visualizations':
            loadVisualizationsSection();
            break;
        case 'sentiment-lab':
            loadSentimentLab();
            break;
        case 'network':
            loadNetworkAnalysis();
            break;
        case 'policy-simulator':
            loadPolicySimulator();
            break;
    }
}
