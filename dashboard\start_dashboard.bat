@echo off
REM AI Policy Analyzer Dashboard - Windows Startup Script
REM Author: <PERSON>
REM Updated: August 13, 2025

echo ========================================
echo AI Policy Analyzer Dashboard - PRODUCTION
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.7+ from https://python.org
    pause
    exit /b 1
)

echo Python version:
python --version
echo.

REM Check current directory
echo Current directory: %CD%
echo.

REM Check if required files exist
if not exist "frontend\index.html" (
    echo ERROR: Frontend files not found
    echo Please ensure you are in the correct directory
    pause
    exit /b 1
)

REM Fix Windows-specific issues
echo Fixing Windows-specific database issues...
python fix_windows_database.py
echo.

REM Install required Python packages
echo Installing required Python packages...
pip install -r backend\requirements.txt
if errorlevel 1 (
    echo Warning: Some packages may not have installed correctly
    echo Trying individual package installation...
    pip install flask flask-cors pandas numpy plotly scikit-learn
)
echo.

REM Create batch files for each service
echo Creating service startup files...

REM Analytics API
echo @echo off > start_analytics_api.bat
echo echo Starting Analytics API on port 5005... >> start_analytics_api.bat
echo cd "%CD%" >> start_analytics_api.bat
echo python backend\advanced_analytics_api.py >> start_analytics_api.bat

REM Visualization API
echo @echo off > start_visualization_api.bat
echo echo Starting Visualization API on port 5001... >> start_visualization_api.bat
echo cd "%CD%" >> start_visualization_api.bat
echo python backend\visualization_api.py >> start_visualization_api.bat

REM Search API
echo @echo off > start_search_api.bat
echo echo Starting Search API on port 5002... >> start_search_api.bat
echo cd "%CD%" >> start_search_api.bat
echo python backend\search_endpoints.py >> start_search_api.bat

REM Historical Data API
echo @echo off > start_historical_api.bat
echo echo Starting Historical Data API on port 5003... >> start_historical_api.bat
echo cd "%CD%" >> start_historical_api.bat
echo python backend\historical_data_endpoints.py >> start_historical_api.bat

REM Batch Processing API
echo @echo off > start_batch_api.bat
echo echo Starting Batch Processing API on port 5007... >> start_batch_api.bat
echo cd "%CD%" >> start_batch_api.bat
echo python backend\enhanced_batch_processor.py >> start_batch_api.bat

REM Frontend Server
echo @echo off > start_frontend.bat
echo echo Starting Frontend Server on port 8080... >> start_frontend.bat
echo cd "%CD%\frontend" >> start_frontend.bat
echo python -m http.server 8080 >> start_frontend.bat

echo Service files created successfully!
echo.

REM Start all services in separate windows
echo Starting all services...
echo.

echo [1/6] Starting Analytics API (port 5005)...
start "AI Policy Analyzer - Analytics API" start_analytics_api.bat

timeout /t 2 /nobreak >nul

echo [2/6] Starting Visualization API (port 5001)...
start "AI Policy Analyzer - Visualization API" start_visualization_api.bat

timeout /t 2 /nobreak >nul

echo [3/6] Starting Search API (port 5002)...
start "AI Policy Analyzer - Search API" start_search_api.bat

timeout /t 2 /nobreak >nul

echo [4/6] Starting Historical Data API (port 5003)...
start "AI Policy Analyzer - Historical Data API" start_historical_api.bat

timeout /t 2 /nobreak >nul

echo [5/6] Starting Batch Processing API (port 5007)...
start "AI Policy Analyzer - Batch API" start_batch_api.bat

timeout /t 2 /nobreak >nul

echo [6/6] Starting Frontend Server (port 8080)...
start "AI Policy Analyzer - Frontend" start_frontend.bat

timeout /t 3 /nobreak >nul

echo.
echo ========================================
echo AI Policy Analyzer Dashboard - PRODUCTION READY!
echo ========================================
echo.
echo Access the dashboard at:
echo   http://localhost:8080/index.html
echo.
echo Production APIs:
echo   Analytics:     http://localhost:5005
echo   Visualization: http://localhost:5001
echo   Search:        http://localhost:5002
echo   Historical:    http://localhost:5003
echo   Batch:         http://localhost:5007
echo.
echo To STOP all services:
echo   Run: stop_dashboard.bat (if available)
echo   Or close all the opened command windows
echo.
echo Dashboard should open automatically in 5 seconds...

REM Wait a moment for services to start
timeout /t 5 /nobreak >nul

REM Try to open the dashboard in default browser with the new UI
start http://localhost:8080/index.html

echo.
echo Press any key to exit this window (services will continue running in background)
pause >nul

REM Create stop_dashboard.bat if it doesn't exist
if not exist "stop_dashboard.bat" (
    echo Creating stop_dashboard.bat...
    echo @echo off > stop_dashboard.bat
    echo echo Stopping all AI Policy Analyzer Dashboard services... >> stop_dashboard.bat
    echo taskkill /f /fi "WINDOWTITLE eq AI Policy Analyzer*" >> stop_dashboard.bat
    echo echo Services stopped successfully. >> stop_dashboard.bat
    echo pause >> stop_dashboard.bat
    echo stop_dashboard.bat created successfully.
)