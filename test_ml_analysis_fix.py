#!/usr/bin/env python3
"""
测试ML分析修复效果
验证"Model classification not found"错误是否已解决

Author: Claude Code
Date: 2025-08-10
"""

import os
import sys
import time
import json
import requests
from pathlib import Path

def test_frontend_ml_models():
    """测试前端ML模型加载"""
    print("🧠 测试前端ML模型加载...")
    
    # 检查dashboard.js文件是否包含修复
    dashboard_js = Path("dashboard/frontend/dashboard.js")
    
    if not dashboard_js.exists():
        print("   ❌ dashboard.js文件不存在")
        return False
    
    try:
        with open(dashboard_js, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修复点
        fixes_found = {
            'error_handling': 'Model classification' in content and 'not found' in content,
            'model_loading': 'loadMLModels()' in content,
            'early_initialization': 'ML models pre-loaded' in content,
            'fallback_mechanism': 'reloading...' in content
        }
        
        print("   修复检查结果:")
        for fix_name, found in fixes_found.items():
            status = "✅" if found else "❌"
            print(f"     {status} {fix_name.replace('_', ' ').title()}: {'Found' if found else 'Missing'}")
        
        all_fixes_present = all(fixes_found.values())
        
        if all_fixes_present:
            print("   ✅ 所有ML模型修复已应用")
        else:
            print("   ⚠️ 部分修复可能缺失")
        
        return all_fixes_present
        
    except Exception as e:
        print(f"   ❌ 检查文件时出错: {e}")
        return False

def test_ml_model_types():
    """测试ML模型类型定义"""
    print("🔍 测试ML模型类型定义...")
    
    dashboard_js = Path("dashboard/frontend/dashboard.js")
    
    try:
        with open(dashboard_js, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查所有必要的模型类型
        required_models = [
            'sentiment',
            'topic', 
            'clustering',
            'classification',
            'anomaly'
        ]
        
        models_found = {}
        for model in required_models:
            # 检查模型定义
            model_defined = f"'{model}':" in content or f'"{model}":' in content
            # 检查switch case
            case_handled = f"case '{model}':" in content
            
            models_found[model] = {
                'defined': model_defined,
                'case_handled': case_handled,
                'complete': model_defined and case_handled
            }
        
        print("   模型类型检查:")
        all_complete = True
        for model, status in models_found.items():
            complete_status = "✅" if status['complete'] else "❌"
            print(f"     {complete_status} {model}: {'Complete' if status['complete'] else 'Incomplete'}")
            if not status['complete']:
                all_complete = False
        
        if all_complete:
            print("   ✅ 所有ML模型类型定义完整")
        else:
            print("   ⚠️ 部分模型类型定义不完整")
        
        return all_complete
        
    except Exception as e:
        print(f"   ❌ 检查模型类型时出错: {e}")
        return False

def test_html_model_selector():
    """测试HTML中的模型选择器"""
    print("🌐 测试HTML模型选择器...")
    
    index_html = Path("dashboard/frontend/index.html")
    
    if not index_html.exists():
        print("   ❌ index.html文件不存在")
        return False
    
    try:
        with open(index_html, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查模型选择器选项
        required_options = [
            'value="sentiment"',
            'value="topic"',
            'value="clustering"',
            'value="classification"',
            'value="anomaly"'
        ]
        
        options_found = {}
        for option in required_options:
            found = option in content
            model_name = option.split('"')[1]
            options_found[model_name] = found
        
        print("   HTML选项检查:")
        all_options_present = True
        for model, found in options_found.items():
            status = "✅" if found else "❌"
            print(f"     {status} {model}: {'Present' if found else 'Missing'}")
            if not found:
                all_options_present = False
        
        # 检查mlModelType ID
        ml_model_type_present = 'id="mlModelType"' in content
        print(f"   {'✅' if ml_model_type_present else '❌'} mlModelType ID: {'Present' if ml_model_type_present else 'Missing'}")
        
        result = all_options_present and ml_model_type_present
        
        if result:
            print("   ✅ HTML模型选择器配置正确")
        else:
            print("   ⚠️ HTML模型选择器配置有问题")
        
        return result
        
    except Exception as e:
        print(f"   ❌ 检查HTML文件时出错: {e}")
        return False

def test_error_message_improvements():
    """测试错误消息改进"""
    print("💬 测试错误消息改进...")
    
    dashboard_js = Path("dashboard/frontend/dashboard.js")
    
    try:
        with open(dashboard_js, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查改进的错误消息
        error_improvements = {
            'detailed_error': 'Available models:' in content,
            'model_list': 'Object.keys(mlModels)' in content,
            'helpful_message': 'Please refresh the page' in content,
            'fallback_loading': 'models not loaded, reloading' in content
        }
        
        print("   错误消息改进检查:")
        all_improvements = True
        for improvement, found in error_improvements.items():
            status = "✅" if found else "❌"
            print(f"     {status} {improvement.replace('_', ' ').title()}: {'Found' if found else 'Missing'}")
            if not found:
                all_improvements = False
        
        if all_improvements:
            print("   ✅ 错误消息已全面改进")
        else:
            print("   ⚠️ 部分错误消息改进缺失")
        
        return all_improvements
        
    except Exception as e:
        print(f"   ❌ 检查错误消息时出错: {e}")
        return False

def test_initialization_order():
    """测试初始化顺序"""
    print("🚀 测试初始化顺序...")
    
    dashboard_js = Path("dashboard/frontend/dashboard.js")
    
    try:
        with open(dashboard_js, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查初始化顺序
        initialization_checks = {
            'dom_ready': 'DOMContentLoaded' in content,
            'early_ml_loading': 'loadMLModels()' in content and 'initializeDashboard' in content,
            'error_handling': 'try {' in content and 'loadMLModels()' in content,
            'success_logging': 'ML models pre-loaded successfully' in content
        }
        
        print("   初始化顺序检查:")
        all_correct = True
        for check, found in initialization_checks.items():
            status = "✅" if found else "❌"
            print(f"     {status} {check.replace('_', ' ').title()}: {'Found' if found else 'Missing'}")
            if not found:
                all_correct = False
        
        if all_correct:
            print("   ✅ 初始化顺序正确")
        else:
            print("   ⚠️ 初始化顺序可能有问题")
        
        return all_correct
        
    except Exception as e:
        print(f"   ❌ 检查初始化顺序时出错: {e}")
        return False

def generate_test_report(results):
    """生成测试报告"""
    print("\n" + "="*60)
    print("📊 ML分析修复测试报告")
    print("="*60)
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    
    print(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
    
    print("\n📋 详细结果:")
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} {test_name.replace('_', ' ').title()}")
    
    overall_status = "✅ 全部通过" if passed_tests == total_tests else "⚠️ 部分失败" if passed_tests > 0 else "❌ 全部失败"
    print(f"\n🎯 总体状态: {overall_status}")
    
    if passed_tests == total_tests:
        print("\n🎉 恭喜！'Model classification not found'错误已完全修复！")
        print("💡 系统现在应该能够正常运行ML分析功能。")
    else:
        print("\n⚠️ 仍有部分问题需要解决。")
        print("💡 请检查失败的测试项目并进行相应修复。")
    
    print("="*60)
    
    return passed_tests == total_tests

def main():
    """主函数"""
    print("🧪 AI Policy Analyzer - ML分析修复测试")
    print("="*60)
    print("测试目标: 验证'Model classification not found'错误修复效果")
    print()
    
    # 执行所有测试
    test_results = {
        'frontend_ml_models': test_frontend_ml_models(),
        'ml_model_types': test_ml_model_types(),
        'html_model_selector': test_html_model_selector(),
        'error_message_improvements': test_error_message_improvements(),
        'initialization_order': test_initialization_order()
    }
    
    # 生成测试报告
    all_passed = generate_test_report(test_results)
    
    # 保存测试结果
    try:
        report_data = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'test_results': test_results,
            'overall_success': all_passed,
            'summary': {
                'total_tests': len(test_results),
                'passed_tests': sum(1 for result in test_results.values() if result),
                'success_rate': (sum(1 for result in test_results.values() if result) / len(test_results)) * 100
            }
        }
        
        with open('ml_analysis_fix_test_report.json', 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 测试报告已保存: ml_analysis_fix_test_report.json")
        
    except Exception as e:
        print(f"\n⚠️ 保存测试报告失败: {e}")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
