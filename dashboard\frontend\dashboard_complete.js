/**
 * AI Policy Analyzer Dashboard
 * A modern dashboard for visualizing and analyzing AI policy data
 * 
 * Version: 2.0.0
 * Last Update: 2025-08-13
 */

// Define API configuration - This was missing in the original code
const API_CONFIG = {
    visualization: 'http://localhost:5001/api',
    search: 'http://localhost:5002/api',
    history: 'http://localhost:5003/api',
    batch: 'http://localhost:5007/api',
    analytics: 'http://localhost:5005/api/analytics'
};

// Initialize dashboard state - This was missing or incomplete
const dashboardState = {
    currentSection: 'dashboard',
    sidebarCollapsed: false,
    searchResults: [],
    historyData: [],
    historySortDirection: 'desc',
    charts: {
        orgTypeChart: null,
        policyChart: null,
        sentimentTrendChart: null
    },
    sentimentPeriod: 'month',
    theme: localStorage.getItem('dashboard_theme') || 'light'
};

/**
 * Show/hide loading overlay
 */
function showLoading(show = true) {
    const loader = document.getElementById('loadingOverlay');
    if (!loader) return;
    
    loader.style.display = show ? 'flex' : 'none';
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    const notifications = document.getElementById('notifications');
    if (!notifications) return;
    
    const id = 'notification-' + Date.now();
    const notification = document.createElement('div');
    notification.id = id;
    notification.className = `alert alert-${type} alert-dismissible fade show`;
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    notifications.appendChild(notification);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        const element = document.getElementById(id);
        if (element) {
            element.classList.remove('show');
            setTimeout(() => element.remove(), 300);
        }
    }, 5000);
}

/**
 * Format date
 */
function formatDate(date) {
    if (!date) return 'N/A';
    
    // Check if date is already a Date object, if not convert it
    const dateObj = date instanceof Date ? date : new Date(date);
    
    // Format options
    const options = { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };
    
    try {
        return dateObj.toLocaleDateString('en-US', options);
    } catch (e) {
        console.error('Date formatting error:', e);
        return 'Invalid Date';
    }
}

/**
 * Safe update element
 */
function safeUpdateElement(id, property, value) {
    const element = document.getElementById(id);
    if (element) {
        if (property === 'innerHTML' || property === 'textContent') {
            element[property] = value;
        } else if (property === 'value') {
            element.value = value;
        } else if (property === 'src' && element instanceof HTMLImageElement) {
            element.src = value;
        } else if (property === 'class' || property === 'className') {
            element.className = value;
        } else {
            console.warn(`Unsupported property '${property}' for element '${id}'`);
        }
    } else {
        console.warn(`Element '${id}' not found`);
    }
}

/**
 * Setup sidebar
 */
function setupSidebar() {
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');
    
    if (!sidebarToggle || !sidebar || !mainContent) {
        console.error('Sidebar elements not found');
        return;
    }
    
    // Check for stored preference
    dashboardState.sidebarCollapsed = localStorage.getItem('sidebar_collapsed') === 'true';
    
    // Initialize sidebar state
    if (dashboardState.sidebarCollapsed) {
        sidebar.classList.add('collapsed');
        mainContent.classList.add('expanded');
    }
    
    // Toggle sidebar
    sidebarToggle.addEventListener('click', function() {
        sidebar.classList.toggle('collapsed');
        mainContent.classList.toggle('expanded');
        
        dashboardState.sidebarCollapsed = sidebar.classList.contains('collapsed');
        localStorage.setItem('sidebar_collapsed', dashboardState.sidebarCollapsed);
    });
    
    // Setup navigation links
    const navLinks = document.querySelectorAll('.list-group-item');
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            navLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
            
            const section = this.getAttribute('data-section');
            if (section) {
                showSection(section);
            }
            
            // Close sidebar on mobile when link is clicked
            if (window.innerWidth < 768) {
                sidebar.classList.add('collapsed');
                mainContent.classList.add('expanded');
                dashboardState.sidebarCollapsed = true;
                localStorage.setItem('sidebar_collapsed', 'true');
            }
        });
    });
}

/**
 * Show a section and hide others
 */
function showSection(sectionName) {
    const sections = document.querySelectorAll('.content-section');
    
    sections.forEach(section => {
        section.style.display = section.id === `${sectionName}-section` ? 'block' : 'none';
    });
    
    // Update current section and load data
    dashboardState.currentSection = sectionName;
    loadSectionData(sectionName);
}

/**
 * Setup navigation
 */
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Update active state
            navLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
            
            // Get target section
            const targetSection = this.getAttribute('data-section') || 'dashboard';
            
            // Show section
            showSection(targetSection);
        });
    });
    
    // Handle top search
    const searchForm = document.getElementById('topSearchForm');
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            performSearch();
        });
    }
    
    // Initialize with dashboard section
    const dashboardLink = document.querySelector(`.list-group-item[data-section="dashboard"]`);
    if (dashboardLink) {
        dashboardLink.classList.add('active');
        showSection('dashboard');
    }
}

/**
 * Load data for a specific section
 */
function loadSectionData(sectionName) {
    switch (sectionName) {
        case 'dashboard':
            loadDashboardData();
            break;
        case 'search':
            // Search is loaded on demand
            break;
        case 'analysis':
            displayAnalysisSection();
            break;
        case 'upload':
            // Upload section is static
            break;
        case 'visualizations':
            // Load visualizations
            break;
        case 'sentiment-lab':
            loadSentimentLab();
            break;
        case 'network':
            loadNetworkAnalysis();
            break;
        case 'policy-simulator':
            loadPolicySimulator();
            break;
        case 'history':
            loadHistoricalData();
            break;
        default:
            console.log(`No data loader for section: ${sectionName}`);
    }
}
