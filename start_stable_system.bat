@echo off
chcp 65001 >nul
title AI Policy Analyzer - 稳定启动器

echo.
echo ========================================
echo   AI Policy Analyzer - 稳定启动器
echo ========================================
echo.

:: 设置变量
set "BASE_DIR=%~dp0"
set "DASHBOARD_DIR=%BASE_DIR%dashboard"
set "BACKEND_DIR=%DASHBOARD_DIR%\backend"
set "FRONTEND_DIR=%DASHBOARD_DIR%\frontend"
set "LOGS_DIR=%BASE_DIR%logs"

:: 创建日志目录
if not exist "%LOGS_DIR%" mkdir "%LOGS_DIR%"

:: 生成时间戳
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "TIMESTAMP=%dt:~0,4%%dt:~4,2%%dt:~6,2%_%dt:~8,2%%dt:~10,2%%dt:~12,2%"

echo 📁 工作目录: %BASE_DIR%
echo 📋 日志目录: %LOGS_DIR%
echo 🕐 启动时间: %TIMESTAMP%
echo.

:: 检查Python
echo 🐍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或不在PATH中
    echo 💡 请安装Python 3.8+并添加到PATH
    pause
    exit /b 1
)
echo ✅ Python环境正常

:: 检查目录结构
echo.
echo 📁 检查目录结构...
if not exist "%DASHBOARD_DIR%" (
    echo ❌ Dashboard目录不存在: %DASHBOARD_DIR%
    pause
    exit /b 1
)
if not exist "%BACKEND_DIR%" (
    echo ❌ Backend目录不存在: %BACKEND_DIR%
    pause
    exit /b 1
)
if not exist "%FRONTEND_DIR%" (
    echo ❌ Frontend目录不存在: %FRONTEND_DIR%
    pause
    exit /b 1
)
echo ✅ 目录结构正常

:: 检查关键脚本
echo.
echo 📄 检查关键脚本...
set "SCRIPTS_OK=1"

if not exist "%BACKEND_DIR%\visualization_api.py" (
    echo ❌ visualization_api.py 不存在
    set "SCRIPTS_OK=0"
)
if not exist "%BACKEND_DIR%\search_endpoints.py" (
    echo ❌ search_endpoints.py 不存在
    set "SCRIPTS_OK=0"
)
if not exist "%BACKEND_DIR%\historical_data_endpoints.py" (
    echo ❌ historical_data_endpoints.py 不存在
    set "SCRIPTS_OK=0"
)
if not exist "%FRONTEND_DIR%\server.py" (
    echo ❌ server.py 不存在
    set "SCRIPTS_OK=0"
)

if "%SCRIPTS_OK%"=="0" (
    echo ❌ 关键脚本文件缺失
    pause
    exit /b 1
)
echo ✅ 关键脚本完整

:: 清理旧进程
echo.
echo 🧹 清理旧进程...
taskkill /f /im python.exe >nul 2>&1
timeout /t 2 /nobreak >nul

:: 启动服务
echo.
echo 🚀 启动服务...

:: 启动Visualization API
echo 📊 启动Visualization API (端口 5001)...
start "Visualization API" /min cmd /c "cd /d "%BACKEND_DIR%" && python visualization_api.py > "%LOGS_DIR%\visualization_api_%TIMESTAMP%.log" 2>&1"
timeout /t 3 /nobreak >nul

:: 启动Search API
echo 🔍 启动Search API (端口 5002)...
start "Search API" /min cmd /c "cd /d "%BACKEND_DIR%" && python search_endpoints.py > "%LOGS_DIR%\search_api_%TIMESTAMP%.log" 2>&1"
timeout /t 3 /nobreak >nul

:: 启动Historical Data API
echo 📚 启动Historical Data API (端口 5003)...
start "Historical Data API" /min cmd /c "cd /d "%BACKEND_DIR%" && python historical_data_endpoints.py > "%LOGS_DIR%\historical_api_%TIMESTAMP%.log" 2>&1"
timeout /t 3 /nobreak >nul

:: 启动Analytics API (可选)
if exist "%BACKEND_DIR%\advanced_analytics_api.py" (
    echo 📈 启动Analytics API (端口 5005)...
    start "Analytics API" /min cmd /c "cd /d "%BACKEND_DIR%" && python advanced_analytics_api.py > "%LOGS_DIR%\analytics_api_%TIMESTAMP%.log" 2>&1"
    timeout /t 3 /nobreak >nul
)

:: 启动Batch API (可选)
if exist "%BACKEND_DIR%\enhanced_batch_processor.py" (
    echo 📦 启动Batch API (端口 5007)...
    start "Batch API" /min cmd /c "cd /d "%BACKEND_DIR%" && python enhanced_batch_processor.py > "%LOGS_DIR%\batch_api_%TIMESTAMP%.log" 2>&1"
    timeout /t 3 /nobreak >nul
)

:: 启动Frontend
echo 🌐 启动Frontend (端口 8028)...
start "Frontend Server" /min cmd /c "cd /d "%FRONTEND_DIR%" && python server.py > "%LOGS_DIR%\frontend_%TIMESTAMP%.log" 2>&1"
timeout /t 5 /nobreak >nul

:: 等待服务启动
echo.
echo ⏳ 等待服务启动完成...
timeout /t 10 /nobreak >nul

:: 检查服务状态
echo.
echo 📊 检查服务状态...

:: 检查端口是否被占用
netstat -an | findstr ":5001 " >nul && echo ✅ Visualization API (5001) - 运行中 || echo ❌ Visualization API (5001) - 未运行
netstat -an | findstr ":5002 " >nul && echo ✅ Search API (5002) - 运行中 || echo ❌ Search API (5002) - 未运行
netstat -an | findstr ":5003 " >nul && echo ✅ Historical Data API (5003) - 运行中 || echo ❌ Historical Data API (5003) - 未运行
netstat -an | findstr ":5005 " >nul && echo ✅ Analytics API (5005) - 运行中 || echo ℹ️ Analytics API (5005) - 可选服务
netstat -an | findstr ":5007 " >nul && echo ✅ Batch API (5007) - 运行中 || echo ℹ️ Batch API (5007) - 可选服务
netstat -an | findstr ":8028 " >nul && echo ✅ Frontend (8028) - 运行中 || echo ❌ Frontend (8028) - 未运行

:: 显示访问信息
echo.
echo ========================================
echo   🎯 AI Policy Analyzer 启动完成!
echo ========================================
echo.
echo 🌐 访问地址:
echo    主控制台: http://localhost:8028
echo    直接访问: http://localhost:8028/index.html
echo.
echo 🔧 API端点:
echo    Visualization: http://localhost:5001
echo    Search:        http://localhost:5002
echo    Historical:    http://localhost:5003
echo    Analytics:     http://localhost:5005
echo    Batch:         http://localhost:5007
echo.
echo 📋 日志文件位置: %LOGS_DIR%
echo.
echo 💡 使用提示:
echo    - 在浏览器中打开 http://localhost:8028
echo    - 如果服务异常，请查看日志文件
echo    - 按任意键停止所有服务
echo.
echo ========================================

:: 等待用户输入
pause

:: 停止所有服务
echo.
echo 🛑 停止所有服务...
taskkill /f /im python.exe >nul 2>&1
echo ✅ 所有服务已停止

echo.
echo 👋 感谢使用 AI Policy Analyzer!
pause
