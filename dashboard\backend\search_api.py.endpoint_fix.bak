#!/usr/bin/env python3
"""
Search and Discovery API for AI Policy Analyzer Dashboard
Provides full-text search, filtering, and pattern discovery capabilities

Author: Claude Code
Date: July 31, 2025
"""

import json
import os
import re
import sys
from typing import Dict, List, Any, Optional, Set
from datetime import datetime
from collections import defaultdict, Counter
import sqlite3
import hashlib

# Import path utilities
try:
    from path_utils import get_dashboard_path, get_analysis_results_path, setup_imports
    setup_imports()
except ImportError:
    # Fallback path setup
    current_dir = os.path.dirname(os.path.abspath(__file__))
    sys.path.append(os.path.dirname(current_dir))
    sys.path.append(os.path.dirname(os.path.dirname(current_dir)))

class SearchIndexManager:
    """Manages document indexing and search operations"""
    
    def __init__(self, index_db_path: str = None):
        if index_db_path is None:
            # Auto-detect current directory and create database path
            current_dir = os.path.dirname(os.path.abspath(__file__))
            index_db_path = os.path.join(current_dir, 'search_index.db')
        
        self.db_path = index_db_path
        # Ensure directory exists
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        self.init_database()
        
    def init_database(self):
        """Initialize SQLite database for search indexing"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Documents table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS documents (
                id TEXT PRIMARY KEY,
                organization_name TEXT,
                organization_type TEXT,
                sector TEXT,
                document_type TEXT,
                submission_date TEXT,
                file_path TEXT,
                content_hash TEXT,
                indexed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Full-text search index
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS document_content (
                document_id TEXT,
                content_type TEXT,
                content_text TEXT,
                section_id TEXT,
                FOREIGN KEY (document_id) REFERENCES documents (id)
            )
        ''')
        
        # Analysis results index
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS analysis_index (
                document_id TEXT,
                analysis_category TEXT,
                analysis_subcategory TEXT,
                keyword TEXT,
                frequency INTEGER,
                density REAL,
                FOREIGN KEY (document_id) REFERENCES documents (id)
            )
        ''')
        
        # Similarity patterns index
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS similarity_patterns (
                document_id TEXT,
                pattern_type TEXT,
                pattern_value TEXT,
                strength REAL,
                FOREIGN KEY (document_id) REFERENCES documents (id)
            )
        ''')
        
        # Create indexes for performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_org_type ON documents (organization_type)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_sector ON documents (sector)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_content_type ON document_content (content_type)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_analysis_category ON analysis_index (analysis_category)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_pattern_type ON similarity_patterns (pattern_type)')
        
        conn.commit()
        conn.close()
    
    def index_document(self, analysis_results: Dict, file_path: str = None) -> str:
        """Index a document and its analysis results"""
        
        metadata = analysis_results.get('metadata', {})
        document_id = self._generate_document_id(metadata, file_path)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # Insert document metadata
            cursor.execute('''
                INSERT OR REPLACE INTO documents 
                (id, organization_name, organization_type, sector, document_type, submission_date, file_path, content_hash)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                document_id,
                metadata.get('organization_name', ''),
                metadata.get('organization_type', ''),
                metadata.get('sector', ''),
                metadata.get('document_type', ''),
                metadata.get('submission_date', ''),
                file_path or '',
                hashlib.md5(json.dumps(analysis_results, sort_keys=True).encode()).hexdigest()
            ))
            
            # Clear existing content for this document
            cursor.execute('DELETE FROM document_content WHERE document_id = ?', (document_id,))
            cursor.execute('DELETE FROM analysis_index WHERE document_id = ?', (document_id,))
            cursor.execute('DELETE FROM similarity_patterns WHERE document_id = ?', (document_id,))
            
            # Index document content
            self._index_content(cursor, document_id, analysis_results)
            
            # Index analysis results
            self._index_analysis_results(cursor, document_id, analysis_results)
            
            # Index similarity patterns
            self._index_similarity_patterns(cursor, document_id, analysis_results)
            
            conn.commit()
            print(f"✅ Indexed document: {document_id}")
            return document_id
            
        except Exception as e:
            conn.rollback()
            print(f"❌ Failed to index document: {e}")
            raise
        finally:
            conn.close()
    
    def _generate_document_id(self, metadata: Dict, file_path: str = None) -> str:
        """Generate unique document ID"""
        org_name = metadata.get('organization_name', 'unknown')
        doc_type = metadata.get('document_type', 'unknown')
        date = metadata.get('analysis_date', datetime.now().isoformat())
        
        id_string = f"{org_name}_{doc_type}_{date}"
        return re.sub(r'[^a-zA-Z0-9_-]', '_', id_string.lower())
    
    def _index_content(self, cursor, document_id: str, analysis_results: Dict):
        """Index document content for full-text search"""
        
        # Index metadata as searchable content
        metadata = analysis_results.get('metadata', {})
        metadata_text = ' '.join([
            str(v) for v in metadata.values() if isinstance(v, (str, int, float))
        ])
        
        cursor.execute('''
            INSERT INTO document_content (document_id, content_type, content_text)
            VALUES (?, ?, ?)
        ''', (document_id, 'metadata', metadata_text))
        
        # Index narrative summary
        narrative_summary = analysis_results.get('narrative_summary', {})
        summary_text = ' '.join([
            str(v) for v in narrative_summary.values() if isinstance(v, str)
        ])
        
        cursor.execute('''
            INSERT INTO document_content (document_id, content_type, content_text)
            VALUES (?, ?, ?)
        ''', (document_id, 'summary', summary_text))
        
        # Index key themes and stakeholders
        moral_data = analysis_results.get('moral_dimensions', {})
        stakeholders = moral_data.get('stakeholder_considerations', [])
        if stakeholders:
            stakeholder_text = ' '.join(stakeholders)
            cursor.execute('''
                INSERT INTO document_content (document_id, content_type, content_text)
                VALUES (?, ?, ?)
            ''', (document_id, 'stakeholders', stakeholder_text))
    
    def _index_analysis_results(self, cursor, document_id: str, analysis_results: Dict):
        """Index detailed analysis results for structured search"""
        
        # Index sentiment analysis
        sentiment_data = analysis_results.get('sentiment_tone', {})
        sentiment_scores = sentiment_data.get('sentiment_scores', {})
        
        for sentiment, score in sentiment_scores.items():
            cursor.execute('''
                INSERT INTO analysis_index (document_id, analysis_category, analysis_subcategory, keyword, frequency, density)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (document_id, 'sentiment', sentiment, sentiment, score, score))
        
        # Index moral dimensions
        moral_data = analysis_results.get('moral_dimensions', {})
        moral_categories = moral_data.get('moral_categories', {})
        
        for category, data in moral_categories.items():
            if data.get('total_mentions', 0) > 0:
                keywords_found = data.get('keywords_found', {})
                for keyword, freq in keywords_found.items():
                    cursor.execute('''
                        INSERT INTO analysis_index (document_id, analysis_category, analysis_subcategory, keyword, frequency, density)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (document_id, 'moral', category, keyword, freq, data.get('density', 0)))
        
        # Index policy solutions
        policy_data = analysis_results.get('policy_solutions', {})
        solution_categories = policy_data.get('solution_categories', {})
        
        for category, data in solution_categories.items():
            if data.get('total_mentions', 0) > 0:
                keywords_found = data.get('keywords_found', {})
                for keyword, freq in keywords_found.items():
                    cursor.execute('''
                        INSERT INTO analysis_index (document_id, analysis_category, analysis_subcategory, keyword, frequency, density)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (document_id, 'policy', category, keyword, freq, data.get('density', 0)))
        
        # Index problem definition
        problem_data = analysis_results.get('problem_definition', {})
        problem_categories = problem_data.get('problem_categories', {})
        
        for category, data in problem_categories.items():
            if data.get('total_mentions', 0) > 0:
                keywords_found = data.get('keywords_found', {})
                for keyword, freq in keywords_found.items():
                    cursor.execute('''
                        INSERT INTO analysis_index (document_id, analysis_category, analysis_subcategory, keyword, frequency, density)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (document_id, 'problem', category, keyword, freq, data.get('density', 0)))
    
    def _index_similarity_patterns(self, cursor, document_id: str, analysis_results: Dict):
        """Index patterns for similarity search"""
        
        # Index moral framing patterns
        moral_data = analysis_results.get('moral_dimensions', {})
        moral_framing_type = moral_data.get('moral_framing_type')
        if moral_framing_type:
            cursor.execute('''
                INSERT INTO similarity_patterns (document_id, pattern_type, pattern_value, strength)
                VALUES (?, ?, ?, ?)
            ''', (document_id, 'moral_framing', moral_framing_type, moral_data.get('total_moral_mentions', 0)))
        
        # Index policy preferences
        policy_data = analysis_results.get('policy_solutions', {})
        dominant_preference = policy_data.get('dominant_preference')
        if dominant_preference:
            preference_strength = policy_data.get('policy_preferences', {}).get(dominant_preference, 0)
            cursor.execute('''
                INSERT INTO similarity_patterns (document_id, pattern_type, pattern_value, strength)
                VALUES (?, ?, ?, ?)
            ''', (document_id, 'policy_preference', dominant_preference, preference_strength))
        
        # Index sentiment patterns
        sentiment_data = analysis_results.get('sentiment_tone', {})
        overall_sentiment = sentiment_data.get('overall_sentiment')
        if overall_sentiment:
            sentiment_scores = sentiment_data.get('sentiment_scores', {})
            sentiment_strength = sentiment_scores.get(overall_sentiment, 0)
            cursor.execute('''
                INSERT INTO similarity_patterns (document_id, pattern_type, pattern_value, strength)
                VALUES (?, ?, ?, ?)
            ''', (document_id, 'sentiment_pattern', overall_sentiment, sentiment_strength))
        
        # Index organization patterns
        metadata = analysis_results.get('metadata', {})
        org_type = metadata.get('organization_type')
        sector = metadata.get('sector')
        
        if org_type:
            cursor.execute('''
                INSERT INTO similarity_patterns (document_id, pattern_type, pattern_value, strength)
                VALUES (?, ?, ?, ?)
            ''', (document_id, 'organization_type', org_type, 1.0))
        
        if sector:
            cursor.execute('''
                INSERT INTO similarity_patterns (document_id, pattern_type, pattern_value, strength)
                VALUES (?, ?, ?, ?)
            ''', (document_id, 'sector', sector, 1.0))

class SearchEngine:
    """Advanced search and discovery engine"""
    
    def __init__(self, index_manager: SearchIndexManager):
        self.index_manager = index_manager
    
    def full_text_search(self, query: str, filters: Dict = None, limit: int = 50) -> List[Dict]:
        """Perform full-text search across documents"""
        
        conn = sqlite3.connect(self.index_manager.db_path)
        cursor = conn.cursor()
        
        # Build search query
        search_terms = query.lower().split()
        
        base_query = '''
            SELECT DISTINCT d.id, d.organization_name, d.organization_type, d.sector, 
                   d.document_type, d.submission_date,
                   COUNT(dc.content_text) as relevance_score
            FROM documents d
            LEFT JOIN document_content dc ON d.id = dc.document_id
            WHERE 1=1
        '''
        
        params = []
        
        # Add text search conditions
        if search_terms:
            search_conditions = []
            for term in search_terms:
                search_conditions.append("LOWER(dc.content_text) LIKE ?")
                params.append(f'%{term}%')
            
            if search_conditions:
                base_query += f" AND ({' OR '.join(search_conditions)})"
        
        # Add filters
        if filters:
            if filters.get('organization_type'):
                base_query += " AND d.organization_type = ?"
                params.append(filters['organization_type'])
            
            if filters.get('sector'):
                base_query += " AND d.sector = ?"
                params.append(filters['sector'])
            
            if filters.get('document_type'):
                base_query += " AND d.document_type = ?"
                params.append(filters['document_type'])
        
        base_query += '''
            GROUP BY d.id, d.organization_name, d.organization_type, d.sector, 
                     d.document_type, d.submission_date
            ORDER BY relevance_score DESC, d.organization_name
            LIMIT ?
        '''
        params.append(limit)
        
        cursor.execute(base_query, params)
        results = cursor.fetchall()
        
        # Format results
        formatted_results = []
        for row in results:
            formatted_results.append({
                'document_id': row[0],
                'organization_name': row[1],
                'organization_type': row[2],
                'sector': row[3],
                'document_type': row[4],
                'submission_date': row[5],
                'relevance_score': row[6]
            })
        
        conn.close()
        return formatted_results
    
    def similarity_search(self, document_id: str, pattern_types: List[str] = None, limit: int = 20) -> List[Dict]:
        """Find documents similar to the given document"""
        
        conn = sqlite3.connect(self.index_manager.db_path)
        cursor = conn.cursor()
        
        if pattern_types is None:
            pattern_types = ['moral_framing', 'policy_preference', 'sentiment_pattern', 'organization_type']
        
        # Get patterns for the reference document
        ref_query = '''
            SELECT pattern_type, pattern_value, strength
            FROM similarity_patterns
            WHERE document_id = ? AND pattern_type IN ({})
        '''.format(','.join(['?' for _ in pattern_types]))
        
        cursor.execute(ref_query, [document_id] + pattern_types)
        ref_patterns = cursor.fetchall()
        
        if not ref_patterns:
            return []
        
        # Find similar documents
        similarity_scores = defaultdict(float)
        
        for pattern_type, pattern_value, strength in ref_patterns:
            similar_query = '''
                SELECT sp.document_id, sp.strength
                FROM similarity_patterns sp
                JOIN documents d ON sp.document_id = d.id
                WHERE sp.pattern_type = ? AND sp.pattern_value = ? 
                AND sp.document_id != ?
            '''
            
            cursor.execute(similar_query, (pattern_type, pattern_value, document_id))
            similar_docs = cursor.fetchall()
            
            for doc_id, doc_strength in similar_docs:
                # Calculate similarity score based on pattern strength
                similarity_scores[doc_id] += min(strength, doc_strength)
        
        # Get document details for similar documents
        if not similarity_scores:
            return []
        
        doc_ids = list(similarity_scores.keys())
        placeholders = ','.join(['?' for _ in doc_ids])
        
        detail_query = f'''
            SELECT id, organization_name, organization_type, sector, 
                   document_type, submission_date
            FROM documents
            WHERE id IN ({placeholders})
        '''
        
        cursor.execute(detail_query, doc_ids)
        doc_details = cursor.fetchall()
        
        # Combine with similarity scores
        results = []
        for row in doc_details:
            doc_id = row[0]
            results.append({
                'document_id': doc_id,
                'organization_name': row[1],
                'organization_type': row[2],
                'sector': row[3],
                'document_type': row[4],
                'submission_date': row[5],
                'similarity_score': round(similarity_scores[doc_id], 3)
            })
        
        # Sort by similarity score
        results.sort(key=lambda x: x['similarity_score'], reverse=True)
        
        conn.close()
        return results[:limit]

# Initialize search components
def initialize_search_system():
    """Initialize the search system with existing data"""
    print("🔍 Initializing AI Policy Analyzer Search System...")
    
    search_index = SearchIndexManager()
    search_engine = SearchEngine(search_index)
    
    # Index existing Microsoft analysis
    microsoft_file = '../../microsoft_policy_narrative_analysis.json'
    if os.path.exists(microsoft_file):
        with open(microsoft_file, 'r') as f:
            microsoft_data = json.load(f)
        
        doc_id = search_index.index_document(microsoft_data, microsoft_file)
        print(f"✅ Indexed Microsoft analysis: {doc_id}")
    
    return search_index, search_engine

if __name__ == '__main__':
    # Initialize search system
    index_manager, engine = initialize_search_system()
    
    print("\n🔍 Search System Ready!")
    print("Available components:")
    print("- Document indexing and management")
    print("- Full-text search capabilities") 
    print("- Similarity pattern matching")
    print("- Search suggestions and autocomplete")