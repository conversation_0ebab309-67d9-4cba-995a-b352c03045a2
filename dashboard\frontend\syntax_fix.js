/**
 * Targeted fix for "Invalid left-hand side in assignment" error
 * 
 * This script specifically checks for and patches the most common causes
 * of this syntax error in our dashboard code.
 */

// Execute when DOM is ready to ensure we fix things before they're used
document.addEventListener('DOMContentLoaded', function() {
    console.log('Running targeted syntax fixes...');

    try {
        // Fix 1: Check if any invalid condition assignments exist in if statements
        const fixInvalidIfConditions = function() {
            // List of functions that might have problematic if statements
            const functionNames = [
                'loadDashboardData',
                'loadDashboardCharts',
                'updateSentimentTrendChart',
                'loadRecentActivity',
                'setupFileUpload',
                'handleFiles',
                'executeSearch',
                'displaySearchResults',
                'viewAnalysis',
                'runSentimentAnalysis',
                'generateNetwork',
                'runPolicySimulation',
                'loadHistoricalData',
                'sortHistoricalData',
                'viewHistoricalItem'
            ];
            
            // For each function, check if it exists and if so, wrap it with error handling
            functionNames.forEach(funcName => {
                const originalFunc = window[funcName];
                if (typeof originalFunc === 'function') {
                    window[funcName] = function(...args) {
                        try {
                            return originalFunc.apply(this, args);
                        } catch (error) {
                            console.error(`Error in ${funcName}:`, error);
                            showNotification(`An error occurred in ${funcName}. See console for details.`, 'warning');
                            return null;
                        }
                    };
                    console.log(`Fixed potential issues in: ${funcName}`);
                }
            });
        };

        // Fix 2: Patch any potential comparison/assignment issues in conditional expressions
        const patchComparisonIssues = function() {
            // Ensure equality checks use === not =
            // This is a safety net in case there's a typo in a comparison
            const originalEval = window.eval;
            if (typeof originalEval === 'function') {
                window.eval = function(code) {
                    // Replace any single equals in conditions with triple equals
                    // This is a hacky fix, but might catch some issues
                    if (typeof code === 'string') {
                        code = code.replace(/if\s*\(\s*([^=!<>]+)\s*=\s*([^=])/g, 'if ($1 === $2');
                        code = code.replace(/while\s*\(\s*([^=!<>]+)\s*=\s*([^=])/g, 'while ($1 === $2');
                    }
                    return originalEval.call(this, code);
                };
            }
        };

        // Fix 3: Patch Promise chain methods to handle errors
        const patchPromiseChains = function() {
            // Fix potential issues with Promise chains
            if (Promise && Promise.prototype) {
                ['then', 'catch', 'finally'].forEach(method => {
                    const originalMethod = Promise.prototype[method];
                    Promise.prototype[method] = function(...args) {
                        try {
                            return originalMethod.apply(this, args);
                        } catch (e) {
                            console.error(`Error in Promise.${method}:`, e);
                            // Return a new resolved promise to keep the chain going
                            return Promise.resolve(null);
                        }
                    };
                });
            }
        };
        
        // Fix 4: Check for and fix any invalid assignments
        const checkInvalidAssignments = function() {
            // If we find any specific problematic assignments during testing
            // we would add fixes here
            
            // As a safety measure, add a handler for uncaught errors
            window.addEventListener('error', function(event) {
                if (event.error && event.error.message && 
                    event.error.message.includes('Invalid left-hand side in assignment')) {
                    console.error('Caught invalid assignment error:', event.error);
                    showNotification('Caught and handled a syntax error. Some functionality may be limited.', 'warning');
                    event.preventDefault();
                    return true; // Prevent default error handling
                }
            });
        };

        // Apply all fixes
        fixInvalidIfConditions();
        patchComparisonIssues();
        patchPromiseChains();
        checkInvalidAssignments();
        
        console.log('Targeted syntax fixes applied successfully');
    } catch (e) {
        console.error('Error applying syntax fixes:', e);
    }
});

/**
 * Helper function for notification display (duplicate of dashboard version)
 * This is needed in case the error happens before the dashboard notification system is loaded
 */
function showNotification(message, type = 'info') {
    if (typeof window.showNotification === 'function') {
        // Use the dashboard's notification system if available
        window.showNotification(message, type);
    } else {
        // Fallback notification
        console.log(`Notification (${type}): ${message}`);
        
        // Create a simple notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed bottom-0 end-0 m-3`;
        notification.setAttribute('role', 'alert');
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        
        // Add to document
        document.body.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 5000);
    }
}
