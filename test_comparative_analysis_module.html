<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comparative Analysis Module Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .organization-item {
            padding: 10px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        
        .organization-item:hover {
            background-color: #f8f9fa;
            border-color: #007bff;
        }
        
        .selected-org-item {
            padding: 8px;
            background-color: #e7f3ff;
            border: 1px solid #007bff;
            border-radius: 6px;
            margin-bottom: 8px;
        }
        
        .insight-item {
            padding: 8px;
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            border-radius: 4px;
        }
        
        .organization-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .selected-list {
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-balance-scale me-2"></i>
            Comparative Analysis Module Test
        </h1>
        
        <!-- Comparison Controls -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cogs"></i> Comparison Setup</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <label for="comparisonType" class="form-label">Comparison Type</label>
                        <select class="form-select" id="comparisonType">
                            <option value="organizations">Organization Comparison</option>
                            <option value="timeperiods">Time Period Comparison</option>
                            <option value="sectors">Sector Comparison</option>
                            <option value="policies">Policy Stance Comparison</option>
                            <option value="sentiment">Sentiment Comparison</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="comparisonMetric" class="form-label">Primary Metric</label>
                        <select class="form-select" id="comparisonMetric">
                            <option value="sentiment">Sentiment Analysis</option>
                            <option value="policy_stance">Policy Stance</option>
                            <option value="moral_framework">Moral Framework</option>
                            <option value="influence">Influence Score</option>
                            <option value="document_count">Document Volume</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-primary mt-4" onclick="runComparativeAnalysis()">
                            <i class="fas fa-balance-scale"></i> Run Comparison
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Organization Selection -->
        <div class="card mb-4" id="organizationSelectionCard">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-building"></i> Select Organizations to Compare</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Available Organizations</h6>
                        <div id="availableOrganizations" class="organization-list">
                            <div class="organization-item" data-org="google">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="org-google" value="google">
                                    <label class="form-check-label" for="org-google">
                                        <strong>Google LLC</strong> <span class="badge bg-primary">Corporate</span>
                                        <br><small class="text-muted">Technology • 156 documents</small>
                                    </label>
                                </div>
                            </div>
                            <div class="organization-item" data-org="microsoft">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="org-microsoft" value="microsoft">
                                    <label class="form-check-label" for="org-microsoft">
                                        <strong>Microsoft Corporation</strong> <span class="badge bg-primary">Corporate</span>
                                        <br><small class="text-muted">Technology • 134 documents</small>
                                    </label>
                                </div>
                            </div>
                            <div class="organization-item" data-org="openai">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="org-openai" value="openai">
                                    <label class="form-check-label" for="org-openai">
                                        <strong>OpenAI</strong> <span class="badge bg-primary">Corporate</span>
                                        <br><small class="text-muted">AI Research • 89 documents</small>
                                    </label>
                                </div>
                            </div>
                            <div class="organization-item" data-org="mit">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="org-mit" value="mit">
                                    <label class="form-check-label" for="org-mit">
                                        <strong>MIT</strong> <span class="badge bg-success">Academic</span>
                                        <br><small class="text-muted">Education • 78 documents</small>
                                    </label>
                                </div>
                            </div>
                            <div class="organization-item" data-org="stanford">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="org-stanford" value="stanford">
                                    <label class="form-check-label" for="org-stanford">
                                        <strong>Stanford HAI</strong> <span class="badge bg-success">Academic</span>
                                        <br><small class="text-muted">Education • 67 documents</small>
                                    </label>
                                </div>
                            </div>
                            <div class="organization-item" data-org="eu_commission">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="org-eu" value="eu_commission">
                                    <label class="form-check-label" for="org-eu">
                                        <strong>EU Commission</strong> <span class="badge bg-warning">Government</span>
                                        <br><small class="text-muted">Policy • 45 documents</small>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>Selected for Comparison (<span id="selectedCount">0</span>)</h6>
                        <div id="selectedOrganizations" class="selected-list">
                            <p class="text-muted">Select organizations from the left to compare</p>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-outline-secondary btn-sm" onclick="clearSelection()">
                                <i class="fas fa-times"></i> Clear All
                            </button>
                            <button class="btn btn-outline-primary btn-sm ms-2" onclick="selectRecommended()">
                                <i class="fas fa-magic"></i> Select Recommended
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Comparison Results -->
        <div id="comparisonResults" class="mt-4">
            <div class="text-center text-muted py-4">
                <i class="fas fa-balance-scale fa-3x mb-3"></i>
                <p>Select organizations and run comparison to see results</p>
            </div>
        </div>
        
        <!-- Test Controls -->
        <div class="test-section">
            <h2><i class="fas fa-cogs me-2"></i>Test Controls</h2>
            <div class="row">
                <div class="col-md-6">
                    <h6>Quick Test Comparisons</h6>
                    <button class="btn btn-outline-primary me-2 mb-2" onclick="testComparison('tech_giants')">
                        <i class="fas fa-building"></i> Compare Tech Giants
                    </button>
                    <button class="btn btn-outline-success me-2 mb-2" onclick="testComparison('academic_vs_corporate')">
                        <i class="fas fa-graduation-cap"></i> Academic vs Corporate
                    </button>
                    <button class="btn btn-outline-info me-2 mb-2" onclick="testComparison('policy_makers')">
                        <i class="fas fa-gavel"></i> Policy Makers
                    </button>
                    <button class="btn btn-outline-warning me-2 mb-2" onclick="testMetric('sentiment')">
                        <i class="fas fa-heart"></i> Test Sentiment Metric
                    </button>
                </div>
                <div class="col-md-6">
                    <h6>Test Results</h6>
                    <div id="testResults" class="alert alert-info">
                        <p><strong>Test Status:</strong> Ready to test comparative functionality</p>
                        <ul>
                            <li>✅ Multi-organization comparison</li>
                            <li>✅ Interactive selection interface</li>
                            <li>✅ Multiple comparison metrics</li>
                            <li>✅ Visual comparison charts</li>
                            <li>✅ Detailed comparison tables</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="dashboard/frontend/dashboard.js"></script>
    <script>
        // Test functions
        function testComparison(comparisonSet) {
            console.log(`🧪 Testing comparison set: ${comparisonSet}`);
            
            // Clear current selection
            clearSelection();
            
            // Select organizations based on test set
            let orgsToSelect = [];
            switch (comparisonSet) {
                case 'tech_giants':
                    orgsToSelect = ['google', 'microsoft', 'openai'];
                    break;
                case 'academic_vs_corporate':
                    orgsToSelect = ['mit', 'stanford', 'google'];
                    break;
                case 'policy_makers':
                    orgsToSelect = ['eu_commission', 'mit', 'google'];
                    break;
            }
            
            // Select the organizations
            orgsToSelect.forEach(orgId => {
                const checkbox = document.getElementById(`org-${orgId}`);
                if (checkbox) {
                    checkbox.checked = true;
                    addOrganizationToSelection(orgId);
                }
            });
            
            updateSelectedOrganizationsDisplay();
            
            // Run comparison
            runComparativeAnalysis();
            
            const testResults = document.getElementById('testResults');
            testResults.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle"></i> Comparison Test: ${comparisonSet}</h6>
                    <p>✅ Organizations selected: ${orgsToSelect.join(', ')}</p>
                    <p>✅ Comparison analysis executed</p>
                    <p>✅ Charts and tables generated</p>
                    <p><strong>Try:</strong> Explore the comparison results below</p>
                </div>
            `;
        }
        
        function testMetric(metric) {
            console.log(`🧪 Testing metric: ${metric}`);
            document.getElementById('comparisonMetric').value = metric;
            
            // Select recommended organizations if none selected
            if (selectedOrganizations.length < 2) {
                selectRecommended();
            }
            
            runComparativeAnalysis();
            
            const testResults = document.getElementById('testResults');
            testResults.innerHTML = `
                <div class="alert alert-info">
                    <h6><i class="fas fa-chart-bar"></i> Metric Test: ${metric}</h6>
                    <p>✅ Primary metric changed to ${metric}</p>
                    <p>✅ Comparison recalculated</p>
                    <p>✅ Charts updated with new metric</p>
                    <p><strong>Try:</strong> Switch between different metrics</p>
                </div>
            `;
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Comparative Analysis Test Page Loaded');
            
            // Initialize comparative functionality
            if (typeof loadComparativeAnalysis === 'function') {
                loadComparativeAnalysis();
            } else {
                console.warn('Comparative functions not loaded. Make sure dashboard.js is included.');
            }
            
            // Auto-select recommended organizations for demo
            setTimeout(() => {
                selectRecommended();
            }, 1000);
        });
    </script>
</body>
</html>
