@echo off
echo Starting API services for AI Policy Analyzer Dashboard...

REM Stop any running services
echo Stopping existing services...
taskkill /F /FI "WINDOWTITLE eq Analytics API*" 2>NUL
taskkill /F /FI "WINDOWTITLE eq Visualization API*" 2>NUL
taskkill /F /FI "WINDOWTITLE eq Search API*" 2>NUL
taskkill /F /FI "WINDOWTITLE eq Historical Data API*" 2>NUL
taskkill /F /FI "WINDOWTITLE eq Batch Processing API*" 2>NUL
timeout /t 2 /nobreak > NUL

REM Set working directory
cd /d "%~dp0"

REM Start API services
echo.
echo Starting Visualization API on port 5001...
start "Visualization API" cmd /k python backend\visualization_api.py

timeout /t 1 /nobreak > NUL
echo Starting Search API on port 5002...
start "Search API" cmd /k python backend\search_api_server.py

timeout /t 1 /nobreak > NUL
echo Starting Historical Data API on port 5003...
start "Historical Data API" cmd /k python backend\analysis_results_api.py

timeout /t 1 /nobreak > NUL
echo Starting Analytics API on port 5005...
start "Analytics API" cmd /k python backend\advanced_analytics_api.py

timeout /t 1 /nobreak > NUL
echo Starting Batch Processing API on port 5007...
start "Batch Processing API" cmd /k python backend\batch_processing_api.py

echo.
echo All API services have been started. Dashboard should now work properly.
echo Press Enter to keep these services running in the background...
pause > NUL
