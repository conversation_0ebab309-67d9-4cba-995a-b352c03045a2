/**
 * Dashboard Enhancements
 * Additional improvements for the AI Policy Analyzer dashboard
 * Include this file after dashboard_complete_final.js
 */

// 1. Real-time Data Refresh
// Add auto-refresh capability for dashboard data
(function() {
    let refreshInterval = null;
    
    // Function to toggle auto-refresh
    window.toggleAutoRefresh = function(enable, intervalMinutes = 5) {
        if (enable) {
            // Clear any existing interval
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
            
            // Set new refresh interval (convert minutes to milliseconds)
            refreshInterval = setInterval(() => {
                console.log('Auto-refreshing dashboard data...');
                loadDashboardData();
                showNotification('Dashboard data refreshed', 'info');
            }, intervalMinutes * 60 * 1000);
            
            showNotification(`Auto-refresh enabled (every ${intervalMinutes} minutes)`, 'success');
        } else {
            // Disable auto-refresh
            if (refreshInterval) {
                clearInterval(refreshInterval);
                refreshInterval = null;
                showNotification('Auto-refresh disabled', 'info');
            }
        }
    };
    
    // Add auto-refresh controls to UI
    document.addEventListener('DOMContentLoaded', function() {
        const dashboardHeader = document.querySelector('#dashboard-section .d-flex.justify-content-between');
        if (dashboardHeader) {
            const autoRefreshControls = document.createElement('div');
            autoRefreshControls.className = 'btn-group btn-group-sm ms-2';
            autoRefreshControls.innerHTML = `
                <button class="btn btn-outline-secondary" onclick="toggleAutoRefresh(true, 5)">
                    <i class="fas fa-sync-alt me-1"></i> Auto-refresh
                </button>
                <button class="btn btn-outline-secondary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown"></button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="toggleAutoRefresh(true, 1); return false;">Every 1 minute</a></li>
                    <li><a class="dropdown-item" href="#" onclick="toggleAutoRefresh(true, 5); return false;">Every 5 minutes</a></li>
                    <li><a class="dropdown-item" href="#" onclick="toggleAutoRefresh(true, 15); return false;">Every 15 minutes</a></li>
                    <li><a class="dropdown-item" href="#" onclick="toggleAutoRefresh(true, 30); return false;">Every 30 minutes</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="#" onclick="toggleAutoRefresh(false); return false;">Disable auto-refresh</a></li>
                </ul>
            `;
            dashboardHeader.appendChild(autoRefreshControls);
        }
    });
})();

// 2. Enhanced Accessibility Features
(function() {
    // Add keyboard navigation support for sidebar
    document.addEventListener('DOMContentLoaded', function() {
        const navItems = document.querySelectorAll('.list-group-item');
        
        navItems.forEach(item => {
            // Add proper ARIA attributes
            item.setAttribute('role', 'button');
            item.setAttribute('tabindex', '0');
            
            // Add keyboard support
            item.addEventListener('keydown', function(event) {
                if (event.key === 'Enter' || event.key === ' ') {
                    event.preventDefault();
                    this.click();
                }
            });
        });
    });
    
    // Add high contrast mode toggle
    document.addEventListener('DOMContentLoaded', function() {
        // Add high contrast button to the navbar
        const navbar = document.querySelector('.navbar .container-fluid');
        if (navbar) {
            const contrastBtn = document.createElement('button');
            contrastBtn.className = 'btn btn-sm btn-outline-secondary ms-2';
            contrastBtn.innerHTML = '<i class="fas fa-adjust"></i>';
            contrastBtn.setAttribute('title', 'Toggle high contrast mode');
            contrastBtn.setAttribute('aria-label', 'Toggle high contrast mode');
            contrastBtn.onclick = toggleHighContrast;
            
            navbar.appendChild(contrastBtn);
        }
        
        // Check if high contrast was previously enabled
        if (localStorage.getItem('highContrast') === 'true') {
            document.body.classList.add('high-contrast');
        }
    });
    
    // Toggle high contrast mode
    window.toggleHighContrast = function() {
        document.body.classList.toggle('high-contrast');
        localStorage.setItem('highContrast', document.body.classList.contains('high-contrast'));
        showNotification('High contrast mode toggled', 'info');
    };
})();

// 3. Data Export Options Enhancement
(function() {
    // Enhanced export for all major sections
    document.addEventListener('DOMContentLoaded', function() {
        // Add export options to relevant sections
        addExportOptions('search-section', 'Search Results');
        addExportOptions('sentiment-lab-section', 'Sentiment Analysis');
        addExportOptions('network-section', 'Network Analysis');
        addExportOptions('policy-simulator-section', 'Policy Simulation');
    });
    
    function addExportOptions(sectionId, title) {
        const sectionHeader = document.querySelector(`#${sectionId} .d-flex.justify-content-between`);
        if (sectionHeader) {
            const exportDropdown = document.createElement('div');
            exportDropdown.className = 'dropdown ms-2';
            exportDropdown.innerHTML = `
                <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-download me-1"></i> Export
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="exportSectionData('${sectionId}', 'csv'); return false;">Export as CSV</a></li>
                    <li><a class="dropdown-item" href="#" onclick="exportSectionData('${sectionId}', 'json'); return false;">Export as JSON</a></li>
                    <li><a class="dropdown-item" href="#" onclick="exportSectionData('${sectionId}', 'pdf'); return false;">Export as PDF</a></li>
                </ul>
            `;
            sectionHeader.appendChild(exportDropdown);
        }
    }
    
    // Generic export function for different sections
    window.exportSectionData = function(sectionId, format) {
        // In a full implementation, this would generate actual exports
        // For now, we'll show a notification about the feature
        console.log(`Exporting ${sectionId} as ${format}`);
        showNotification(`${format.toUpperCase()} export for this section will be available in the next update`, 'info');
    };
})();

// 4. Dashboard State Management Improvements
(function() {
    // Save and restore all dashboard state
    document.addEventListener('DOMContentLoaded', function() {
        // Restore previous state if available
        restoreDashboardState();
        
        // Save state when leaving/refreshing page
        window.addEventListener('beforeunload', saveDashboardState);
    });
    
    function saveDashboardState() {
        const state = {
            activeSection: dashboardState.activeSection,
            searchQuery: document.getElementById('searchInput')?.value || '',
            sentimentPeriod: dashboardState.sentimentPeriod,
            historicalFilters: {
                dateRange: document.getElementById('historyDateRange')?.value || '30',
                orgType: document.getElementById('historyOrgType')?.value || '',
                docType: document.getElementById('historyDocType')?.value || ''
            },
            simulatorParams: {
                policyType: document.getElementById('simPolicyType')?.value || 'self-regulation',
                scope: document.getElementById('simScope')?.value || 'moderate',
                enforcement: document.getElementById('simEnforcement')?.value || 'voluntary',
                timeframe: document.getElementById('simTimeframe')?.value || 'medium'
            },
            darkMode: document.body.classList.contains('dark-mode'),
            sidebarCollapsed: document.getElementById('sidebar').classList.contains('collapsed')
        };
        
        localStorage.setItem('dashboardState', JSON.stringify(state));
    }
    
    function restoreDashboardState() {
        try {
            const savedState = localStorage.getItem('dashboardState');
            if (savedState) {
                const state = JSON.parse(savedState);
                
                // Restore active section
                if (state.activeSection) {
                    navigateToSection(state.activeSection);
                }
                
                // Restore search query
                if (state.searchQuery && document.getElementById('searchInput')) {
                    document.getElementById('searchInput').value = state.searchQuery;
                }
                
                // Restore sentiment period
                if (state.sentimentPeriod) {
                    dashboardState.sentimentPeriod = state.sentimentPeriod;
                    const periodBtn = document.querySelector(`.btn-time-period[data-period="${state.sentimentPeriod}"]`);
                    if (periodBtn) {
                        document.querySelectorAll('.btn-time-period').forEach(btn => btn.classList.remove('active'));
                        periodBtn.classList.add('active');
                    }
                }
                
                // Restore historical filters
                if (state.historicalFilters) {
                    const { dateRange, orgType, docType } = state.historicalFilters;
                    if (dateRange && document.getElementById('historyDateRange')) {
                        document.getElementById('historyDateRange').value = dateRange;
                    }
                    if (orgType && document.getElementById('historyOrgType')) {
                        document.getElementById('historyOrgType').value = orgType;
                    }
                    if (docType && document.getElementById('historyDocType')) {
                        document.getElementById('historyDocType').value = docType;
                    }
                }
                
                // Restore simulator params
                if (state.simulatorParams) {
                    const { policyType, scope, enforcement, timeframe } = state.simulatorParams;
                    if (policyType && document.getElementById('simPolicyType')) {
                        document.getElementById('simPolicyType').value = policyType;
                    }
                    if (scope && document.getElementById('simScope')) {
                        document.getElementById('simScope').value = scope;
                    }
                    if (enforcement && document.getElementById('simEnforcement')) {
                        document.getElementById('simEnforcement').value = enforcement;
                    }
                    if (timeframe && document.getElementById('simTimeframe')) {
                        document.getElementById('simTimeframe').value = timeframe;
                    }
                }
            }
        } catch (error) {
            console.error('Error restoring dashboard state:', error);
        }
    }
})();

// 5. Enhanced Error Handling & Connection Status
(function() {
    // Define API endpoints for production services
    const API_CONFIG = {
        analytics: 'http://localhost:5005/api/analytics',
        search: 'http://localhost:5002/api/search',
        visualization: 'http://localhost:5001/api/visualize',
        historical: 'http://localhost:5003/api/historical',
        batch: 'http://localhost:5007/api/batch'
    };

    // Track API connectivity
    const apiStatus = {
        analytics: 'unknown',
        search: 'unknown',
        visualization: 'unknown',
        historical: 'unknown',
        batch: 'unknown'
    };
    
    // Check all API endpoints on load
    document.addEventListener('DOMContentLoaded', function() {
        checkApiConnectivity();
        
        // Add connectivity indicator to navbar
        const navbar = document.querySelector('.navbar .container-fluid');
        if (navbar) {
            const statusIndicator = document.createElement('div');
            statusIndicator.id = 'apiStatusIndicator';
            statusIndicator.className = 'ms-auto me-3';
            statusIndicator.innerHTML = `
                <button class="btn btn-sm btn-outline-secondary position-relative" 
                        data-bs-toggle="tooltip" data-bs-placement="bottom" 
                        title="API connection status">
                    <i class="fas fa-plug"></i>
                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-secondary">
                        <span class="visually-hidden">API status</span>
                    </span>
                </button>
            `;
            navbar.appendChild(statusIndicator);
            
            // Initialize tooltip
            new bootstrap.Tooltip(statusIndicator.querySelector('button'));
            
            // Add status click handler to show detailed status
            statusIndicator.querySelector('button').addEventListener('click', showApiStatusModal);
        }
    });
    
    // Check connectivity to all production API endpoints
    function checkApiConnectivity() {
        // Check each API endpoint individually
        Object.keys(API_CONFIG).forEach(key => {
            const url = API_CONFIG[key] + '/health';
            
            fetch(url, { 
                method: 'GET', 
                timeout: 2000,
                headers: {
                    'Accept': 'application/json'
                }
            })
            .then(response => {
                apiStatus[key] = response.ok ? 'online' : 'error';
                updateConnectivityIndicator();
            })
            .catch(error => {
                console.log(`API check failed for ${key}:`, error);
                apiStatus[key] = 'offline';
                updateConnectivityIndicator();
            });
        });
    }
    
    // Update the status indicator
    function updateConnectivityIndicator() {
        const indicator = document.querySelector('#apiStatusIndicator .badge');
        const button = document.querySelector('#apiStatusIndicator button');
        
        if (!indicator || !button) return;
        
        const allOnline = Object.values(apiStatus).every(status => status === 'online');
        const allOffline = Object.values(apiStatus).every(status => status === 'offline');
        
        if (allOnline) {
            indicator.className = 'position-absolute top-0 start-100 translate-middle badge rounded-pill bg-success';
            button.setAttribute('title', 'All API services online');
        } else if (allOffline) {
            indicator.className = 'position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger';
            button.setAttribute('title', 'All API services offline - using sample data');
        } else {
            indicator.className = 'position-absolute top-0 start-100 translate-middle badge rounded-pill bg-warning';
            button.setAttribute('title', 'Some API services offline - partial functionality');
        }
        
        // Refresh tooltip
        new bootstrap.Tooltip(button);
    }
    
    // Show detailed API status modal
    function showApiStatusModal() {
        // Create modal if it doesn't exist
        let modal = document.getElementById('apiStatusModal');
        
        if (!modal) {
            const modalElement = document.createElement('div');
            modalElement.className = 'modal fade';
            modalElement.id = 'apiStatusModal';
            modalElement.tabIndex = -1;
            modalElement.setAttribute('aria-labelledby', 'apiStatusModalLabel');
            modalElement.setAttribute('aria-hidden', 'true');
            
            modalElement.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="apiStatusModalLabel">API Connection Status</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <ul class="list-group" id="apiStatusList">
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Analytics API
                                    <span class="badge bg-secondary">Checking...</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Search API
                                    <span class="badge bg-secondary">Checking...</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Visualization API
                                    <span class="badge bg-secondary">Checking...</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Historical Data API
                                    <span class="badge bg-secondary">Checking...</span>
                                </li>
                            </ul>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-primary" onclick="checkApiConnectivity()">
                                <i class="fas fa-sync-alt me-1"></i> Refresh Status
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modalElement);
            modal = modalElement;
        }
        
        // Update status list
        const statusList = modal.querySelector('#apiStatusList');
        if (statusList) {
            const listItems = statusList.querySelectorAll('li');
            
            Object.keys(apiStatus).forEach((api, index) => {
                if (index < listItems.length) {
                    const statusBadge = listItems[index].querySelector('.badge');
                    if (statusBadge) {
                        switch (apiStatus[api]) {
                            case 'online':
                                statusBadge.className = 'badge bg-success';
                                statusBadge.textContent = 'Online';
                                break;
                            case 'offline':
                                statusBadge.className = 'badge bg-danger';
                                statusBadge.textContent = 'Offline';
                                break;
                            case 'error':
                                statusBadge.className = 'badge bg-warning';
                                statusBadge.textContent = 'Error';
                                break;
                            default:
                                statusBadge.className = 'badge bg-secondary';
                                statusBadge.textContent = 'Unknown';
                        }
                    }
                }
            });
        }
        
        // Show the modal
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
    }
    
    // Make function available globally
    window.checkApiConnectivity = checkApiConnectivity;
    window.showApiStatusModal = showApiStatusModal;
})();

// 6. Performance Optimizations
(function() {
    // Lazy load charts
    document.addEventListener('DOMContentLoaded', function() {
        // Original chart creation functions are wrapped to be called only when their section is visible
        const originalCreateOrgTypeChart = window.createOrgTypeChart;
        const originalCreatePolicyChart = window.createPolicyChart;
        const originalCreateSentimentTrendChart = window.createSentimentTrendChart;
        
        if (typeof originalCreateOrgTypeChart === 'function') {
            window.createOrgTypeChart = function(data) {
                const ctx = document.getElementById('orgTypeChart');
                if (!ctx || !isElementVisible(ctx)) {
                    console.log('Deferring org type chart creation until visible');
                    return; // Don't create chart if not visible
                }
                return originalCreateOrgTypeChart(data);
            };
        }
        
        if (typeof originalCreatePolicyChart === 'function') {
            window.createPolicyChart = function(data) {
                const ctx = document.getElementById('policyChart');
                if (!ctx || !isElementVisible(ctx)) {
                    console.log('Deferring policy chart creation until visible');
                    return; // Don't create chart if not visible
                }
                return originalCreatePolicyChart(data);
            };
        }
        
        if (typeof originalCreateSentimentTrendChart === 'function') {
            window.createSentimentTrendChart = function() {
                const ctx = document.getElementById('sentimentTrendChart');
                if (!ctx || !isElementVisible(ctx)) {
                    console.log('Deferring sentiment trend chart creation until visible');
                    return; // Don't create chart if not visible
                }
                return originalCreateSentimentTrendChart();
            };
        }
        
        // Helper function to check if element is visible
        function isElementVisible(element) {
            if (!element) return false;
            const rect = element.getBoundingClientRect();
            return (
                rect.top <= (window.innerHeight || document.documentElement.clientHeight) &&
                rect.bottom >= 0
            );
        }
        
        // Re-check chart creation when dashboard section becomes visible
        const originalNavigateToSection = window.navigateToSection;
        if (typeof originalNavigateToSection === 'function') {
            window.navigateToSection = function(sectionName) {
                originalNavigateToSection(sectionName);
                
                // If navigating to dashboard, ensure charts are created
                if (sectionName === 'dashboard') {
                    setTimeout(() => {
                        if (!dashboardState.charts.orgTypeChart && document.getElementById('orgTypeChart')) {
                            console.log('Creating deferred org type chart');
                            loadDashboardCharts();
                        }
                    }, 100);
                }
            };
        }
    });
    
    // Optimize DOM updates
    const debouncedDomUpdates = {};
    
    // Debounce function to prevent excessive DOM updates
    window.debounceUpdate = function(key, updateFn, delay = 200) {
        if (debouncedDomUpdates[key]) {
            clearTimeout(debouncedDomUpdates[key]);
        }
        
        debouncedDomUpdates[key] = setTimeout(() => {
            updateFn();
            debouncedDomUpdates[key] = null;
        }, delay);
    };
})();

// 7. Mobile Experience Enhancements
(function() {
    document.addEventListener('DOMContentLoaded', function() {
        // Detect mobile devices
        const isMobile = window.innerWidth < 768;
        
        if (isMobile) {
            // Auto-collapse sidebar on mobile
            const sidebar = document.getElementById('sidebar');
            if (sidebar && !sidebar.classList.contains('collapsed')) {
                sidebar.classList.add('collapsed');
            }
            
            // Add swipe gestures for sidebar on mobile
            let touchStartX = 0;
            let touchEndX = 0;
            
            document.addEventListener('touchstart', e => {
                touchStartX = e.changedTouches[0].screenX;
            }, false);
            
            document.addEventListener('touchend', e => {
                touchEndX = e.changedTouches[0].screenX;
                handleSwipe();
            }, false);
            
            function handleSwipe() {
                const sidebar = document.getElementById('sidebar');
                if (!sidebar) return;
                
                // Detect left and right swipes
                if (touchEndX < touchStartX - 75) {
                    // Swipe left, collapse sidebar
                    sidebar.classList.add('collapsed');
                }
                
                if (touchEndX > touchStartX + 75 && touchStartX < 50) {
                    // Swipe right from edge, expand sidebar
                    sidebar.classList.remove('collapsed');
                }
            }
            
            // Optimize tables for mobile
            const tables = document.querySelectorAll('.table');
            tables.forEach(table => {
                table.classList.add('table-responsive-sm');
            });
        }
    });
})();

// Initialize enhancements after page loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('Dashboard enhancements initialized');
    showNotification('Enhanced features loaded', 'success');
});

// Add corresponding CSS for new features
(function() {
    const enhancementsStyle = document.createElement('style');
    enhancementsStyle.textContent = `
        /* High contrast mode */
        body.high-contrast {
            --bs-primary: #0000ff;
            --bs-success: #006600;
            --bs-danger: #cc0000;
            --bs-info: #0066cc;
            --bs-warning: #cc6600;
            background-color: #ffffff;
            color: #000000;
        }
        
        body.high-contrast .card {
            border: 1px solid #000000;
        }
        
        body.high-contrast .bg-light {
            background-color: #f8f8f8 !important;
        }
        
        body.high-contrast .text-muted {
            color: #333333 !important;
        }
        
        /* Mobile enhancements */
        @media (max-width: 767.98px) {
            .sidebar.collapsed {
                margin-left: -100%;
            }
            
            .content-wrapper {
                margin-left: 0 !important;
                width: 100% !important;
            }
            
            .card-header {
                padding: 0.5rem 0.75rem;
            }
            
            .btn-sm {
                padding: 0.25rem 0.5rem;
                font-size: 0.75rem;
            }
        }
    `;
    
    document.head.appendChild(enhancementsStyle);
})();
