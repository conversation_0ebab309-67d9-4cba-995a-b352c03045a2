/**
 * Update Sentiment Trend Chart with data
 */
function updateSentimentTrendChart(sentimentData) {
    const ctx = document.getElementById('sentimentTrendChart');
    if (!ctx) return;
    
    // Clean up any existing chart
    if (dashboardState.charts.sentimentTrendChart) {
        dashboardState.charts.sentimentTrendChart.destroy();
    }
    
    // Create chart with the data
    dashboardState.charts.sentimentTrendChart = new Chart(ctx, {
        type: 'line',
        data: sentimentData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 1,
                    ticks: {
                        callback: function(value) {
                            return (value * 100) + '%';
                        }
                    },
                    grid: {
                        display: true,
                        drawBorder: false
                    }
                },
                x: {
                    grid: {
                        display: false,
                        drawBorder: false
                    }
                }
            }
        }
    });
}

/**
 * Load recent activity
 */
function loadRecentActivity() {
    const recentActivityElement = document.getElementById('recent-activity');
    if (!recentActivityElement) return;
    
    // Try to fetch recent activity from API
    fetch(`${API_CONFIG.history}/activity/recent`)
        .then(response => {
            if (!response.ok) throw new Error('API unavailable');
            return response.json();
        })
        .then(data => {
            displayRecentActivity(data.activities || []);
        })
        .catch(error => {
            console.warn('Failed to fetch recent activity, using sample values');
            
            // Use sample data
            const sampleActivities = [
                {
                    id: 'act-1',
                    type: 'analysis',
                    organization: 'Google LLC',
                    document_type: 'AI Policy',
                    timestamp: new Date(new Date().setHours(new Date().getHours() - 2)).toISOString()
                },
                {
                    id: 'act-2',
                    type: 'upload',
                    organization: 'OpenAI',
                    document_type: 'Safety Guidelines',
                    timestamp: new Date(new Date().setHours(new Date().getHours() - 5)).toISOString()
                },
                {
                    id: 'act-3',
                    type: 'search',
                    query: 'regulatory frameworks',
                    results: 24,
                    timestamp: new Date(new Date().setHours(new Date().getHours() - 6)).toISOString()
                },
                {
                    id: 'act-4',
                    type: 'analysis',
                    organization: 'Microsoft Corporation',
                    document_type: 'Policy Statement',
                    timestamp: new Date(new Date().setHours(new Date().getHours() - 8)).toISOString()
                },
                {
                    id: 'act-5',
                    type: 'upload',
                    organization: '1Day Sooner',
                    document_type: 'Position Paper',
                    timestamp: new Date(new Date().setDate(new Date().getDate() - 1)).toISOString()
                }
            ];
            
            displayRecentActivity(sampleActivities);
        });
}

/**
 * Display recent activity
 */
function displayRecentActivity(activities) {
    const recentActivityElement = document.getElementById('recent-activity');
    if (!recentActivityElement) return;
    
    if (!activities || activities.length === 0) {
        recentActivityElement.innerHTML = '<div class="text-center text-muted py-3">No recent activity</div>';
        return;
    }
    
    let activityHTML = '';
    
    activities.forEach(activity => {
        const time = formatDate(new Date(activity.timestamp));
        let content = '';
        let icon = '';
        
        switch (activity.type) {
            case 'analysis':
                icon = '<div class="activity-icon bg-primary"><i class="fas fa-chart-bar"></i></div>';
                content = `<strong>Analyzed</strong> ${activity.organization} ${activity.document_type}`;
                break;
            case 'upload':
                icon = '<div class="activity-icon bg-success"><i class="fas fa-file-upload"></i></div>';
                content = `<strong>Uploaded</strong> ${activity.organization} ${activity.document_type}`;
                break;
            case 'search':
                icon = '<div class="activity-icon bg-info"><i class="fas fa-search"></i></div>';
                content = `<strong>Searched</strong> for "${activity.query}" (${activity.results} results)`;
                break;
            default:
                icon = '<div class="activity-icon bg-secondary"><i class="fas fa-bell"></i></div>';
                content = `<strong>${activity.type}</strong> ${JSON.stringify(activity).slice(0, 50)}...`;
        }
        
        activityHTML += `
            <div class="activity-item">
                ${icon}
                <div class="activity-content">
                    <div class="activity-title">${content}</div>
                    <div class="activity-time">${time}</div>
                </div>
            </div>
        `;
    });
    
    recentActivityElement.innerHTML = activityHTML;
}

/**
 * Set up file upload functionality
 */
function setupFileUpload() {
    const dropArea = document.getElementById('drop-area');
    const fileInput = document.getElementById('fileInput');
    
    if (!dropArea || !fileInput) return;
    
    // Prevent default behaviors
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, preventDefaults, false);
    });
    
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    // Highlight drop area when file is dragged over it
    ['dragenter', 'dragover'].forEach(eventName => {
        dropArea.addEventListener(eventName, highlight, false);
    });
    
    ['dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, unhighlight, false);
    });
    
    function highlight() {
        dropArea.classList.add('highlight');
    }
    
    function unhighlight() {
        dropArea.classList.remove('highlight');
    }
    
    // Handle dropped files
    dropArea.addEventListener('drop', handleDrop, false);
    
    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        
        handleFiles(files);
    }
    
    // Handle selected files
    fileInput.addEventListener('change', function() {
        handleFiles(this.files);
    });
    
    // Click anywhere in drop area to trigger file input
    dropArea.addEventListener('click', function() {
        fileInput.click();
    });
}

/**
 * Handle uploaded files
 */
function handleFiles(files) {
    if (!files || files.length === 0) return;
    
    console.log(`📁 Processing ${files.length} file(s)...`);
    
    const uploadProgress = document.getElementById('uploadProgress');
    const progressBar = document.getElementById('progressBar');
    const uploadStatus = document.getElementById('uploadStatus');
    
    if (!uploadProgress || !progressBar || !uploadStatus) return;
    
    uploadProgress.style.display = 'block';
    
    // Simulate upload progress
    let progress = 0;
    const interval = setInterval(() => {
        progress += 10;
        progressBar.style.width = progress + '%';
        progressBar.textContent = progress + '%';
        
        if (progress >= 100) {
            clearInterval(interval);
            
            // Show completion status
            uploadStatus.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i> 
                    Successfully uploaded ${files.length} file(s). Analysis will begin shortly.
                </div>
            `;
            
            // Add to upload history
            addToUploadHistory(files);
            
            // Reset after 3 seconds
            setTimeout(() => {
                uploadProgress.style.display = 'none';
                progressBar.style.width = '0%';
                progressBar.textContent = '';
                uploadStatus.innerHTML = '';
            }, 3000);
        }
    }, 200);
}

/**
 * Add uploaded files to history
 */
function addToUploadHistory(files) {
    const uploadHistory = document.getElementById('uploadHistory');
    if (!uploadHistory) return;
    
    // Clear "No recent uploads" message if present
    if (uploadHistory.innerHTML.includes('No recent uploads')) {
        uploadHistory.innerHTML = '';
    }
    
    // Add each file to history
    Array.from(files).forEach(file => {
        const row = document.createElement('tr');
        const now = new Date();
        
        row.innerHTML = `
            <td>${file.name}</td>
            <td>${now.toLocaleDateString()} ${now.toLocaleTimeString()}</td>
            <td><span class="badge bg-success">Processed</span></td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="viewAnalysis('${file.name}')">
                    <i class="fas fa-eye me-1"></i> View
                </button>
            </td>
        `;
        
        uploadHistory.prepend(row);
    });
}

/**
 * Setup search functionality
 */
function setupSearch() {
    // Search suggestions
    const searchInput = document.getElementById('searchInput');
    
    if (!searchInput) return;
    
    // Fix bug: Ensure the search input event listener is properly set
    searchInput.addEventListener('input', function() {
        const query = this.value;
        if (query.length > 2) {
            // Could implement search suggestions here
            console.log('Search suggestions for:', query);
        }
    });

    // Fix bug: Add form submission handling for search form
    const searchForm = document.getElementById('searchForm');
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            performSearch();
        });
    }
}

/**
 * Perform search
 */
async function performSearch() {
    // Fix bug: Get search input value more robustly
    const searchInput = document.getElementById('searchInput');
    if (!searchInput) {
        showNotification('Search input not found', 'error');
        return;
    }

    const query = searchInput.value.trim();
    const orgTypeFilter = document.getElementById('orgTypeFilter')?.value || '';
    const sectorFilter = document.getElementById('sectorFilter')?.value || '';
    const analysisFilter = document.getElementById('analysisFilter')?.value || '';
    
    // Navigate to search section if not already there
    navigateToSection('search');
    
    if (!query && !orgTypeFilter && !sectorFilter && !analysisFilter) {
        showNotification('Please enter a search query or select filters.', 'warning');
        return;
    }
    
    try {
        showLoading(true);
        
        let searchUrl = `${API_CONFIG.search}/search`;
        const params = new URLSearchParams();
        
        if (query) params.append('query', query);
        if (orgTypeFilter) params.append('organization_type', orgTypeFilter);
        if (sectorFilter) params.append('sector', sectorFilter);
        
        if (params.toString()) {
            searchUrl += '?' + params.toString();
        }
        
        const response = await fetch(searchUrl);
        const data = await response.json();
        
        if (response.ok) {
            displaySearchResults(data);
        } else {
            throw new Error(data.error || 'Search failed');
        }
        
    } catch (error) {
        console.error('Search error:', error);
        
        // Show sample results instead
        displaySampleResults();
        
        showNotification('Search API not available. Showing sample results.', 'info');
    } finally {
        showLoading(false);
    }
}

/**
 * Navigate to a specific section
 */
function navigateToSection(sectionName) {
    const navLink = document.querySelector(`.list-group-item[data-section="${sectionName}"]`);
    if (navLink) {
        navLink.click();
    }
}
