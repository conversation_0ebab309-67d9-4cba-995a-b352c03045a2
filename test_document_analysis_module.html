<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Analysis Module Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        
        .upload-area.drag-over {
            border-color: #28a745;
            background-color: #d4edda;
        }
        
        .file-item {
            transition: all 0.3s ease;
        }
        
        .file-item:hover {
            background-color: #f8f9fa;
        }
        
        .analysis-step {
            padding: 8px;
            margin: 5px 0;
            border-radius: 4px;
            background-color: #f8f9fa;
        }
        
        .stat-card {
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .feature-result {
            padding: 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px;
            margin-bottom: 15px;
        }
        
        .keywords-list .badge {
            margin: 2px;
        }
        
        .emotion-bars {
            font-size: 0.9em;
        }
        
        .document-result {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-file-alt me-2"></i>
            Document Analysis Module Test
        </h1>
        
        <!-- Analysis Configuration -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cogs"></i> Analysis Configuration</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label for="analysisType" class="form-label">Analysis Type</label>
                        <select class="form-select" id="analysisType">
                            <option value="comprehensive">Comprehensive Analysis</option>
                            <option value="sentiment">Sentiment Analysis</option>
                            <option value="keywords">Keyword Extraction</option>
                            <option value="classification">Document Classification</option>
                            <option value="similarity">Similarity Analysis</option>
                            <option value="summary">Document Summary</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="analysisLanguage" class="form-label">Language</label>
                        <select class="form-select" id="analysisLanguage">
                            <option value="auto">Auto-detect</option>
                            <option value="en">English</option>
                            <option value="es">Spanish</option>
                            <option value="fr">French</option>
                            <option value="de">German</option>
                            <option value="zh">Chinese</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="analysisDepth" class="form-label">Analysis Depth</label>
                        <select class="form-select" id="analysisDepth">
                            <option value="basic">Basic Analysis</option>
                            <option value="standard" selected>Standard Analysis</option>
                            <option value="deep">Deep Analysis</option>
                            <option value="research">Research Grade</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check mt-4">
                            <input class="form-check-input" type="checkbox" id="enableOCR" checked>
                            <label class="form-check-label" for="enableOCR">
                                Enable OCR for scanned documents
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload Area -->
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="upload-area" id="uploadArea">
                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                    <h4>Drag and drop documents here</h4>
                    <p class="text-muted">or <button class="btn btn-link p-0" onclick="document.getElementById('fileInput').click()">browse files</button></p>
                    <input type="file" id="fileInput" multiple accept=".pdf,.txt,.docx,.csv,.jpg,.png,.md" style="display: none;">
                    <div class="mt-3">
                        <small class="text-muted">Supported: PDF, TXT, DOCX, CSV, JPG, PNG, MD (Max 25MB each)</small>
                    </div>
                    <div class="mt-3">
                        <button class="btn btn-outline-primary btn-sm me-2" onclick="loadSampleDocuments()">
                            <i class="fas fa-file-import"></i> Load Sample Documents
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="pasteTextForAnalysis()">
                            <i class="fas fa-paste"></i> Paste Text
                        </button>
                    </div>
                </div>

                <!-- File List -->
                <div id="fileList" class="mt-4" style="display: none;">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0"><i class="fas fa-list"></i> Selected Documents (<span id="fileCount">0</span>)</h6>
                            <div>
                                <button class="btn btn-outline-info btn-sm me-2" onclick="previewSelectedFiles()">
                                    <i class="fas fa-eye"></i> Preview
                                </button>
                                <button class="btn btn-outline-danger btn-sm" onclick="clearFileList()">
                                    <i class="fas fa-trash"></i> Clear All
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="fileListContent"></div>
                            <div class="mt-3">
                                <div class="row">
                                    <div class="col-md-8">
                                        <button class="btn btn-primary" onclick="startDocumentAnalysis()">
                                            <i class="fas fa-brain"></i> Start Analysis
                                        </button>
                                        <button class="btn btn-outline-secondary ms-2" onclick="addMoreFiles()">
                                            <i class="fas fa-plus"></i> Add More Files
                                        </button>
                                        <button class="btn btn-outline-info ms-2" onclick="batchAnalysisOptions()">
                                            <i class="fas fa-cogs"></i> Batch Options
                                        </button>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <small class="text-muted">
                                            Total size: <span id="totalFileSize">0 MB</span>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Analysis Progress -->
                <div id="uploadProgress" class="mt-4" style="display: none;">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0"><i class="fas fa-brain fa-spin"></i> Document Analysis Progress</h6>
                            <button class="btn btn-outline-danger btn-sm" onclick="cancelAnalysis()">
                                <i class="fas fa-stop"></i> Cancel
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="progress mb-3" style="height: 20px;">
                                <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated"
                                     role="progressbar" style="width: 0%">
                                    <span id="progressText">0%</span>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-8">
                                    <div id="uploadStatus">
                                        <div class="analysis-step">
                                            <i class="fas fa-upload text-primary"></i> Uploading documents...
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="analysis-stats">
                                        <small class="text-muted">
                                            <div>Files processed: <span id="processedCount">0</span>/<span id="totalCount">0</span></div>
                                            <div>Estimated time: <span id="estimatedTime">--</span></div>
                                            <div>Current step: <span id="currentStep">Initializing</span></div>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Analysis Results -->
                <div id="analysisResults" class="mt-4" style="display: none;">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0"><i class="fas fa-chart-bar"></i> Analysis Results</h6>
                            <div>
                                <button class="btn btn-outline-secondary btn-sm me-2" onclick="exportAnalysisResults()">
                                    <i class="fas fa-download"></i> Export
                                </button>
                                <button class="btn btn-outline-primary btn-sm" onclick="shareAnalysisResults()">
                                    <i class="fas fa-share"></i> Share
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="analysisResultsContent">
                                <!-- Results will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Controls -->
        <div class="test-section">
            <h2><i class="fas fa-cogs me-2"></i>Test Controls</h2>
            <div class="row">
                <div class="col-md-6">
                    <h6>Quick Test Functions</h6>
                    <button class="btn btn-outline-primary me-2 mb-2" onclick="testSampleDocuments()">
                        <i class="fas fa-file-import"></i> Test Sample Docs
                    </button>
                    <button class="btn btn-outline-success me-2 mb-2" onclick="testTextPaste()">
                        <i class="fas fa-paste"></i> Test Text Paste
                    </button>
                    <button class="btn btn-outline-info me-2 mb-2" onclick="testAnalysisTypes()">
                        <i class="fas fa-brain"></i> Test Analysis Types
                    </button>
                    <button class="btn btn-outline-warning me-2 mb-2" onclick="testBatchAnalysis()">
                        <i class="fas fa-layer-group"></i> Test Batch Analysis
                    </button>
                </div>
                <div class="col-md-6">
                    <h6>Test Results</h6>
                    <div id="testResults" class="alert alert-info">
                        <p><strong>Test Status:</strong> Ready to test document analysis functionality</p>
                        <ul>
                            <li>✅ Multi-format document support</li>
                            <li>✅ Comprehensive analysis features</li>
                            <li>✅ Real-time progress tracking</li>
                            <li>✅ Detailed results visualization</li>
                            <li>✅ Export and sharing capabilities</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="dashboard/frontend/dashboard.js"></script>
    <script>
        // Test functions
        function testSampleDocuments() {
            console.log('🧪 Testing sample documents...');
            loadSampleDocuments();
            
            const testResults = document.getElementById('testResults');
            testResults.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle"></i> Sample Documents Test</h6>
                    <p>✅ Sample documents loaded successfully</p>
                    <p>✅ File validation working</p>
                    <p>✅ File list display updated</p>
                    <p><strong>Try:</strong> Start analysis to see processing</p>
                </div>
            `;
        }
        
        function testTextPaste() {
            console.log('🧪 Testing text paste...');
            
            // Simulate pasted text
            const sampleText = "This is a sample text for testing the document analysis functionality. It contains various keywords related to artificial intelligence, machine learning, and policy frameworks.";
            
            // Create a text file from the sample content
            const blob = new Blob([sampleText], { type: 'text/plain' });
            const file = new File([blob], 'test_pasted_text.txt', { type: 'text/plain' });
            
            handleFileSelection([file]);
            
            const testResults = document.getElementById('testResults');
            testResults.innerHTML = `
                <div class="alert alert-info">
                    <h6><i class="fas fa-paste"></i> Text Paste Test</h6>
                    <p>✅ Text converted to file successfully</p>
                    <p>✅ File added to analysis queue</p>
                    <p>✅ Ready for analysis processing</p>
                    <p><strong>Try:</strong> Run analysis on the pasted text</p>
                </div>
            `;
        }
        
        function testAnalysisTypes() {
            console.log('🧪 Testing analysis types...');
            
            const analysisTypes = ['comprehensive', 'sentiment', 'keywords', 'classification', 'summary'];
            let currentType = 0;
            
            const cycleTypes = () => {
                document.getElementById('analysisType').value = analysisTypes[currentType];
                currentType = (currentType + 1) % analysisTypes.length;
                
                if (currentType === 0) {
                    clearInterval(typeInterval);
                    
                    const testResults = document.getElementById('testResults');
                    testResults.innerHTML = `
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-brain"></i> Analysis Types Test</h6>
                            <p>✅ All analysis types tested</p>
                            <p>✅ Configuration options working</p>
                            <p>✅ Type selection functional</p>
                            <p><strong>Try:</strong> Select different types and run analysis</p>
                        </div>
                    `;
                }
            };
            
            const typeInterval = setInterval(cycleTypes, 1000);
        }
        
        function testBatchAnalysis() {
            console.log('🧪 Testing batch analysis...');
            
            // Load sample documents first
            loadSampleDocuments();
            
            // Wait a moment then start analysis
            setTimeout(() => {
                startDocumentAnalysis();
                
                const testResults = document.getElementById('testResults');
                testResults.innerHTML = `
                    <div class="alert alert-primary">
                        <h6><i class="fas fa-layer-group"></i> Batch Analysis Test</h6>
                        <p>✅ Multiple documents loaded</p>
                        <p>✅ Batch analysis started</p>
                        <p>✅ Progress tracking active</p>
                        <p><strong>Watch:</strong> Progress bar and file status updates</p>
                    </div>
                `;
            }, 1000);
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Document Analysis Test Page Loaded');
            
            // Initialize document analysis functionality
            if (typeof loadDocumentAnalysis === 'function') {
                loadDocumentAnalysis();
            } else {
                console.warn('Document Analysis functions not loaded. Make sure dashboard.js is included.');
            }
        });
    </script>
</body>
</html>
