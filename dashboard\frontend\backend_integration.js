// ============================================================================
// BACKEND INTEGRATION MODULE FOR AI POLICY DASHBOARD
// ============================================================================

/**
 * 后端集成模块 - 连接前端Dashboard与统一后端系统
 * 提供API调用、数据同步、实时更新等功能
 */

class BackendIntegration {
    constructor() {
        this.baseURL = 'http://localhost:5001/api';
        this.isConnected = false;
        this.retryCount = 0;
        this.maxRetries = 3;
        this.updateInterval = null;
        
        console.log('🔗 初始化后端集成模块...');
        this.initialize();
    }
    
    /**
     * 初始化后端集成
     */
    async initialize() {
        try {
            await this.checkConnection();
            this.startPeriodicUpdates();
            console.log('✅ 后端集成初始化完成');
        } catch (error) {
            console.error('❌ 后端集成初始化失败:', error);
        }
    }
    
    /**
     * 检查后端连接
     */
    async checkConnection() {
        try {
            const response = await this.apiCall('/system/status');
            this.isConnected = true;
            this.retryCount = 0;
            console.log('✅ 后端连接正常');
            return response;
        } catch (error) {
            this.isConnected = false;
            console.warn('⚠️ 后端连接失败:', error.message);
            throw error;
        }
    }
    
    /**
     * 通用API调用方法
     */
    async apiCall(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
        };
        
        const finalOptions = { ...defaultOptions, ...options };
        
        try {
            const response = await fetch(url, finalOptions);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            return data;
        } catch (error) {
            console.error(`API调用失败 ${endpoint}:`, error);
            throw error;
        }
    }
    
    /**
     * 获取系统状态
     */
    async getSystemStatus() {
        return await this.apiCall('/system/status');
    }
    
    /**
     * 扫描和索引文档
     */
    async scanDocuments() {
        return await this.apiCall('/documents/scan', { method: 'POST' });
    }
    
    /**
     * 获取文档列表
     */
    async getDocuments(page = 1, perPage = 50, status = null) {
        let endpoint = `/documents/list?page=${page}&per_page=${perPage}`;
        if (status) {
            endpoint += `&status=${status}`;
        }
        return await this.apiCall(endpoint);
    }
    
    /**
     * 获取文档分析结果
     */
    async getDocumentAnalysis(documentId) {
        return await this.apiCall(`/documents/${documentId}/analysis`);
    }
    
    /**
     * 获取分析摘要
     */
    async getAnalyticsSummary() {
        return await this.apiCall('/analytics/summary');
    }
    
    /**
     * 获取情感分析统计
     */
    async getSentimentAnalytics() {
        return await this.apiCall('/analytics/sentiment');
    }
    
    /**
     * 启动批量处理
     */
    async startBatchProcessing(priority = 1, limit = null) {
        const body = { priority };
        if (limit) body.limit = limit;
        
        return await this.apiCall('/processing/start', {
            method: 'POST',
            body: JSON.stringify(body)
        });
    }
    
    /**
     * 获取处理队列状态
     */
    async getProcessingQueue() {
        return await this.apiCall('/processing/queue');
    }
    
    /**
     * 搜索文档
     */
    async searchDocuments(query, organization = '', status = '', page = 1, perPage = 20) {
        let endpoint = `/search?page=${page}&per_page=${perPage}`;
        if (query) endpoint += `&q=${encodeURIComponent(query)}`;
        if (organization) endpoint += `&organization=${encodeURIComponent(organization)}`;
        if (status) endpoint += `&status=${encodeURIComponent(status)}`;
        
        return await this.apiCall(endpoint);
    }
    
    /**
     * 启动定期更新
     */
    startPeriodicUpdates() {
        // 每30秒更新一次系统状态
        this.updateInterval = setInterval(async () => {
            try {
                if (this.isConnected) {
                    const status = await this.getSystemStatus();
                    this.updateDashboardStatus(status);
                }
            } catch (error) {
                console.warn('定期更新失败:', error);
                this.isConnected = false;
            }
        }, 30000);
    }
    
    /**
     * 停止定期更新
     */
    stopPeriodicUpdates() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }
    
    /**
     * 更新Dashboard状态显示
     */
    updateDashboardStatus(status) {
        // 更新系统状态指示器
        const statusIndicator = document.getElementById('backend-status');
        if (statusIndicator) {
            statusIndicator.className = this.isConnected ? 'badge bg-success' : 'badge bg-danger';
            statusIndicator.textContent = this.isConnected ? 'Connected' : 'Disconnected';
        }
        
        // 更新文档统计
        if (status && typeof updateDocumentStats === 'function') {
            updateDocumentStats(status);
        }
        
        // 触发自定义事件
        window.dispatchEvent(new CustomEvent('backendStatusUpdate', { 
            detail: { status, isConnected: this.isConnected } 
        }));
    }
    
    /**
     * 重试连接
     */
    async retryConnection() {
        if (this.retryCount < this.maxRetries) {
            this.retryCount++;
            console.log(`🔄 尝试重新连接后端 (${this.retryCount}/${this.maxRetries})`);
            
            try {
                await this.checkConnection();
                return true;
            } catch (error) {
                if (this.retryCount >= this.maxRetries) {
                    console.error('❌ 后端连接重试次数已达上限');
                }
                return false;
            }
        }
        return false;
    }
}

// ============================================================================
// DASHBOARD INTEGRATION FUNCTIONS
// ============================================================================

// 全局后端集成实例
let backendIntegration = null;

/**
 * 初始化后端集成
 */
function initializeBackendIntegration() {
    if (!backendIntegration) {
        backendIntegration = new BackendIntegration();
    }
    return backendIntegration;
}

/**
 * 获取后端集成实例
 */
function getBackendIntegration() {
    if (!backendIntegration) {
        backendIntegration = initializeBackendIntegration();
    }
    return backendIntegration;
}

/**
 * 更新文档统计显示
 */
function updateDocumentStats(status) {
    // 更新总文档数
    const totalDocsElement = document.getElementById('total-documents');
    if (totalDocsElement && status.total_documents !== undefined) {
        totalDocsElement.textContent = status.total_documents.toLocaleString();
    }
    
    // 更新已处理文档数
    const processedDocsElement = document.getElementById('processed-documents');
    if (processedDocsElement && status.processed_documents !== undefined) {
        processedDocsElement.textContent = status.processed_documents.toLocaleString();
    }
    
    // 更新处理进度
    const progressElement = document.getElementById('processing-progress');
    if (progressElement && status.processing_rate !== undefined) {
        progressElement.style.width = status.processing_rate + '%';
        progressElement.textContent = status.processing_rate + '%';
    }
    
    // 更新处理率
    const rateElement = document.getElementById('processing-rate');
    if (rateElement && status.processing_rate !== undefined) {
        rateElement.textContent = status.processing_rate + '%';
    }
}

/**
 * 显示后端数据在Dashboard中
 */
async function loadBackendDataToDashboard() {
    try {
        const backend = getBackendIntegration();
        
        // 获取系统状态
        const systemStatus = await backend.getSystemStatus();
        updateDocumentStats(systemStatus);
        
        // 获取分析摘要
        const analyticsSummary = await backend.getAnalyticsSummary();
        updateAnalyticsSummary(analyticsSummary);
        
        // 获取情感分析
        const sentimentAnalytics = await backend.getSentimentAnalytics();
        updateSentimentDisplay(sentimentAnalytics);
        
        console.log('✅ 后端数据加载到Dashboard完成');
        
    } catch (error) {
        console.error('❌ 加载后端数据失败:', error);
        showBackendError(error.message);
    }
}

/**
 * 更新分析摘要显示
 */
function updateAnalyticsSummary(summary) {
    // 更新顶级组织
    const topOrgsElement = document.getElementById('top-organizations');
    if (topOrgsElement && summary.top_organizations) {
        const orgsList = summary.top_organizations.slice(0, 5).map(org => 
            `<li class="list-group-item d-flex justify-content-between">
                <span>${org.organization}</span>
                <span class="badge bg-primary">${org.document_count}</span>
            </li>`
        ).join('');
        
        topOrgsElement.innerHTML = `<ul class="list-group list-group-flush">${orgsList}</ul>`;
    }
    
    // 更新状态统计
    const statusStatsElement = document.getElementById('status-statistics');
    if (statusStatsElement && summary.status_statistics) {
        const statsHTML = Object.entries(summary.status_statistics).map(([status, count]) => 
            `<div class="col-md-3">
                <div class="stat-item text-center">
                    <h4>${count}</h4>
                    <small>${status}</small>
                </div>
            </div>`
        ).join('');
        
        statusStatsElement.innerHTML = `<div class="row">${statsHTML}</div>`;
    }
}

/**
 * 更新情感分析显示
 */
function updateSentimentDisplay(sentimentData) {
    if (!sentimentData || !sentimentData.overall_sentiment) return;
    
    const sentiment = sentimentData.overall_sentiment;
    
    // 更新情感统计
    const sentimentStatsElement = document.getElementById('sentiment-statistics');
    if (sentimentStatsElement) {
        sentimentStatsElement.innerHTML = `
            <div class="row text-center">
                <div class="col-md-4">
                    <div class="sentiment-stat positive">
                        <h3>${sentiment.positive}%</h3>
                        <small>Positive</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="sentiment-stat neutral">
                        <h3>${sentiment.neutral}%</h3>
                        <small>Neutral</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="sentiment-stat negative">
                        <h3>${sentiment.negative}%</h3>
                        <small>Negative</small>
                    </div>
                </div>
            </div>
            <div class="text-center mt-2">
                <small class="text-muted">Based on ${sentiment.total_analyzed} analyzed documents</small>
            </div>
        `;
    }
}

/**
 * 显示后端错误
 */
function showBackendError(message) {
    const errorElement = document.getElementById('backend-error');
    if (errorElement) {
        errorElement.innerHTML = `
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                <strong>Backend Connection Issue:</strong> ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
    }
}

/**
 * 启动批量文档处理
 */
async function startBatchDocumentProcessing() {
    try {
        const backend = getBackendIntegration();
        
        // 显示处理开始提示
        showProcessingStatus('Starting batch processing...');
        
        // 启动批量处理
        const result = await backend.startBatchProcessing(1, 100); // 优先级1，限制100个文档
        
        showProcessingStatus(`Batch processing started: ${result.documents_added} documents added to queue`);
        
        // 开始监控处理进度
        monitorProcessingProgress();
        
    } catch (error) {
        console.error('❌ 启动批量处理失败:', error);
        showProcessingStatus(`Error starting batch processing: ${error.message}`, 'error');
    }
}

/**
 * 监控处理进度
 */
async function monitorProcessingProgress() {
    const backend = getBackendIntegration();
    
    const progressInterval = setInterval(async () => {
        try {
            const status = await backend.getSystemStatus();
            updateDocumentStats(status);
            
            // 如果没有文档在处理中，停止监控
            if (status.processing_documents === 0 && status.queued_documents === 0) {
                clearInterval(progressInterval);
                showProcessingStatus('Batch processing completed!', 'success');
            }
            
        } catch (error) {
            console.error('监控处理进度失败:', error);
            clearInterval(progressInterval);
        }
    }, 5000); // 每5秒检查一次
}

/**
 * 显示处理状态
 */
function showProcessingStatus(message, type = 'info') {
    const statusElement = document.getElementById('processing-status');
    if (statusElement) {
        const alertClass = type === 'error' ? 'alert-danger' : 
                          type === 'success' ? 'alert-success' : 'alert-info';
        
        statusElement.innerHTML = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
    }
}

// ============================================================================
// EVENT LISTENERS AND INITIALIZATION
// ============================================================================

// 页面加载时初始化后端集成
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔗 页面加载完成，初始化后端集成...');
    
    // 初始化后端集成
    initializeBackendIntegration();
    
    // 延迟加载后端数据
    setTimeout(() => {
        loadBackendDataToDashboard();
    }, 2000);
});

// 监听后端状态更新事件
window.addEventListener('backendStatusUpdate', function(event) {
    const { status, isConnected } = event.detail;
    
    if (isConnected) {
        console.log('📡 后端连接正常，状态已更新');
    } else {
        console.warn('⚠️ 后端连接断开');
    }
});

// 导出主要函数供全局使用
window.backendIntegration = {
    initialize: initializeBackendIntegration,
    getInstance: getBackendIntegration,
    loadData: loadBackendDataToDashboard,
    startBatchProcessing: startBatchDocumentProcessing
};
