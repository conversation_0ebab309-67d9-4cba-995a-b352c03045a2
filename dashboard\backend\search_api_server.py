#!/usr/bin/env python3
"""
Search API Server for AI Policy Analyzer Dashboard
Provides Flask server for Search Engine API endpoints

Author: <PERSON> Code
Date: August 13, 2025
"""

import os
import sys
import json
from flask import Flask, jsonify, request
from flask_cors import CORS

# 添加当前目录到path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 导入搜索引擎
from search_api import SearchIndexManager, SearchEngine, initialize_search_system

# 初始化搜索系统
index_manager, search_engine = initialize_search_system()

# 创建Flask应用
app = Flask(__name__)
CORS(app)

@app.route('/api/search/health', methods=['GET'])
def health_check():
    """API健康检查端点"""
    return jsonify({
        'status': 'healthy',
        'service': 'Search API',
        'version': '1.0',
        'port': 5002
    })

@app.route('/api/search/text', methods=['GET', 'POST'])
def text_search():
    """全文搜索API"""
    try:
        if request.method == 'POST':
            data = request.json
            query = data.get('query', '')
            filters = data.get('filters', {})
            limit = data.get('limit', 50)
        else:
            query = request.args.get('query', '')
            filters_str = request.args.get('filters', '{}')
            try:
                filters = json.loads(filters_str)
            except:
                filters = {}
            limit = int(request.args.get('limit', 50))
        
        if not query:
            return jsonify({'error': 'Query parameter is required'}), 400
            
        results = search_engine.full_text_search(query, filters, limit)
        return jsonify({'results': results})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/search/similar/<document_id>', methods=['GET'])
def similar_documents(document_id):
    """相似文档搜索API"""
    try:
        pattern_types_str = request.args.get('pattern_types', None)
        if pattern_types_str:
            pattern_types = pattern_types_str.split(',')
        else:
            pattern_types = None
            
        limit = int(request.args.get('limit', 20))
        
        results = search_engine.similarity_search(document_id, pattern_types, limit)
        return jsonify({'results': results})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def start_server(port=5002):
    """启动Search API服务器"""
    print(f"\n🔍 启动AI Policy Analyzer Search API服务器在端口 {port}")
    print("可用端点:")
    print("  GET /api/search/health - 健康检查")
    print("  GET /api/search/text?query=<query> - 全文搜索")
    print("  POST /api/search/text - 全文搜索(JSON格式)")
    print("  GET /api/search/similar/<document_id> - 相似文档搜索")
    
    app.run(host='0.0.0.0', port=port, debug=False)

if __name__ == '__main__':
    start_server()
